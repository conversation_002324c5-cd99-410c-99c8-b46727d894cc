<div class="row justify-content-center">
  <div class="col-md-8 text-center">
    <div class="error-container my-5">
      <i class="bi bi-exclamation-triangle text-danger" style="font-size: 5rem;"></i>
      
      <h1 class="mt-4 mb-3"><%= typeof error !== 'undefined' ? error : 'Error' %></h1>
      
      <div class="alert alert-danger">
        <%= typeof message !== 'undefined' ? message : 'An unexpected error occurred.' %>
      </div>
      
      <p class="mb-4">Please try again or contact support if the problem persists.</p>
      
      <div class="d-flex justify-content-center gap-3">
        <a href="javascript:history.back()" class="btn btn-outline-secondary">
          <i class="bi bi-arrow-left"></i> Go Back
        </a>
        <a href="/" class="btn btn-success">
          <i class="bi bi-house"></i> Go Home
        </a>
      </div>
    </div>
  </div>
</div>
