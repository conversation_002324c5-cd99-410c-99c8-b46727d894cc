import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import * as networkService from '../services/networkService.js';

const router = express.Router();

// Test route to verify network routes are working (no auth required)
router.get('/test', (req, res) => {
  console.log('=== NETWORK TEST ROUTE ACCESSED ===');
  console.log('Request URL:', req.originalUrl);
  console.log('Request method:', req.method);
  res.json({
    success: true,
    message: 'Network routes are working!',
    user: req.user ? req.user.uid : 'No user',
    authenticated: !!req.user,
    timestamp: new Date().toISOString()
  });
});

// Test route with authentication
router.get('/test-auth', isAuthenticated, (req, res) => {
  console.log('=== NETWORK AUTH TEST ROUTE ACCESSED ===');
  res.json({
    success: true,
    message: 'Network auth routes are working!',
    user: req.user.uid,
    timestamp: new Date().toISOString()
  });
});

// Network home page - Feed
router.get('/', isAuthenticated, async (req, res) => {
  try {
    console.log('=== NETWORK ROUTE ACCESSED ===');
    console.log('Network route accessed by user:', req.user ? req.user.uid : 'No user');
    console.log('User authenticated:', !!req.user);

    // Use the imported networkService with user context
    const feedData = await networkService.getNetworkFeed(20, null, req.user);

    // Get connections count
    const connections = await networkService.getUserConnections('accepted', req.user);
    const connectionsCount = connections.length;

    res.render('network/index', {
      user: req.user,
      userData: req.userData || {},
      title: 'Farmer Network',
      posts: feedData.posts || [],
      connectionsCount: connectionsCount
    });
  } catch (error) {
    console.error('Error rendering network home:', error);

    // Check if this is a permission error
    if (error.code === 'permission-denied' || error.message.includes('Missing or insufficient permissions')) {
      res.render('network/index', {
        user: req.user,
        userData: req.userData || {},
        title: 'Farmer Network',
        posts: [],
        connectionsCount: 0,
        indexError: true,
        errorMessage: 'Database permissions are being configured. Please try again in a few minutes or contact support.'
      });
    }
    // Check if this is an index error
    else if (error.message && error.message.includes('requires an index')) {
      res.render('network/index', {
        user: req.user,
        userData: req.userData || {},
        title: 'Farmer Network',
        posts: [],
        connectionsCount: 0,
        indexError: true,
        errorMessage: 'The database index is still being built. Please try again in a few minutes.'
      });
    } else {
      // For other errors, render the network page with empty data instead of error page
      res.render('network/index', {
        user: req.user,
        userData: req.userData || {},
        title: 'Farmer Network',
        posts: [],
        connectionsCount: 0,
        indexError: true,
        errorMessage: 'Unable to load network feed at the moment. Please try again later.'
      });
    }
  }
});

// User profile page
router.get('/profile/:userId', isAuthenticated, async (req, res) => {
  try {
    const { userId } = req.params;

    // Get user data for the profile
    const profileUserData = await networkService.getUserData(userId);

    if (!profileUserData) {
      throw new Error('User not found');
    }

    // Check if the current user is connected to the profile user
    const connection = await networkService.checkConnectionExists(req.user.uid, userId);

    let connectionStatus = 'none';
    let connectionId = null;
    let isRequester = false;

    if (connection) {
      connectionStatus = connection.status;
      connectionId = connection.id;
      isRequester = connection.requesterId === req.user.uid;
    }

    res.render('network/profile', {
      user: req.user,
      userData: req.userData,
      title: `${profileUserData.displayName || 'User'} - Profile`,
      profileUser: {
        uid: userId,
        ...profileUserData
      },
      isCurrentUser: userId === req.user.uid,
      connectionStatus: connectionStatus,
      connectionId: connectionId,
      isRequester: isRequester
    });
  } catch (error) {
    console.error('Error viewing user profile:', error);
    res.render('error', {
      error: 'Error loading user profile',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// Connections page
router.get('/connections', isAuthenticated, async (req, res) => {
  try {
    // Get accepted connections
    const connections = await networkService.getUserConnections('accepted', req.user);

    // Get pending connection requests
    const pendingRequests = await networkService.getPendingConnectionRequests(req.user);

    res.render('network/connections', {
      user: req.user,
      userData: req.userData,
      title: 'My Connections',
      connections: connections,
      pendingRequests: pendingRequests
    });
  } catch (error) {
    console.error('Error viewing connections:', error);
    res.render('error', {
      error: 'Error loading connections',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// Search page
router.get('/search', isAuthenticated, async (req, res) => {
  try {
    const { q } = req.query;

    // Search for farmers based on the query
    const results = q ? await networkService.searchFarmers(q) : [];

    res.render('network/search', {
      user: req.user,
      userData: req.userData,
      title: 'Find Farmers',
      searchTerm: q || '',
      results: results
    });
  } catch (error) {
    console.error('Error searching farmers:', error);
    res.render('error', {
      error: 'Error searching for farmers',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// API endpoints for network functionality

// Get user connections
router.get('/api/connections', isAuthenticated, async (req, res) => {
  try {
    const connections = await networkService.getUserConnections('accepted', req.user);
    res.json({ success: true, connections });
  } catch (error) {
    console.error('Error getting connections:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get pending connection requests
router.get('/api/connections/pending', isAuthenticated, async (req, res) => {
  try {
    const requests = await networkService.getPendingConnectionRequests(req.user);
    res.json({ success: true, requests });
  } catch (error) {
    console.error('Error getting pending requests:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get suggested connections
router.get('/api/connections/suggestions', isAuthenticated, async (req, res) => {
  try {
    const suggestions = await networkService.getSuggestedConnections(6);
    res.json({ success: true, suggestions });
  } catch (error) {
    console.error('Error getting suggested connections:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Search for farmers
router.get('/api/search', isAuthenticated, async (req, res) => {
  try {
    const { q, limit } = req.query;
    const results = await networkService.searchFarmers(q, limit ? parseInt(limit) : 20);
    res.json({ success: true, results });
  } catch (error) {
    console.error('Error searching farmers:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Send connection request
router.post('/api/connections/request', isAuthenticated, async (req, res) => {
  try {
    const { recipientId } = req.body;

    if (!recipientId) {
      return res.status(400).json({ success: false, message: 'Recipient ID is required' });
    }

    // Send the connection request
    const result = await networkService.sendConnectionRequest(recipientId);

    res.json({ success: true, message: 'Connection request sent successfully', data: result });
  } catch (error) {
    console.error('Error sending connection request:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Accept connection request
router.post('/api/connections/accept', isAuthenticated, async (req, res) => {
  try {
    const { connectionId } = req.body;

    if (!connectionId) {
      return res.status(400).json({ success: false, message: 'Connection ID is required' });
    }

    // Accept the connection request
    await networkService.acceptConnectionRequest(connectionId);

    res.json({ success: true, message: 'Connection request accepted' });
  } catch (error) {
    console.error('Error accepting connection request:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Reject connection request
router.post('/api/connections/reject', isAuthenticated, async (req, res) => {
  try {
    const { connectionId } = req.body;

    if (!connectionId) {
      return res.status(400).json({ success: false, message: 'Connection ID is required' });
    }

    // Reject the connection request
    await networkService.rejectConnectionRequest(connectionId);

    res.json({ success: true, message: 'Connection request rejected' });
  } catch (error) {
    console.error('Error rejecting connection request:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Remove connection
router.post('/api/connections/remove', isAuthenticated, async (req, res) => {
  try {
    const { connectionId } = req.body;

    if (!connectionId) {
      return res.status(400).json({ success: false, message: 'Connection ID is required' });
    }

    // Remove the connection
    await networkService.removeConnection(connectionId);

    res.json({ success: true, message: 'Connection removed successfully' });
  } catch (error) {
    console.error('Error removing connection:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Create post
router.post('/api/posts', isAuthenticated, async (req, res) => {
  try {
    const { text, type, imageUrl } = req.body;

    if (!text && !imageUrl) {
      return res.status(400).json({ success: false, message: 'Post must have text or an image' });
    }

    // Create the post
    const result = await networkService.createPost({
      userId: req.user.uid,
      text,
      type: type || 'text',
      imageUrl,
      createdAt: new Date()
    });

    res.json({ success: true, message: 'Post created successfully', data: result });
  } catch (error) {
    console.error('Error creating post:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Like post
router.post('/api/posts/:postId/like', isAuthenticated, async (req, res) => {
  try {
    const { postId } = req.params;

    if (!postId) {
      return res.status(400).json({ success: false, message: 'Post ID is required' });
    }

    // Like the post
    const result = await networkService.likePost(postId);

    res.json({
      success: true,
      message: result.liked ? 'Post liked successfully' : 'Post unliked successfully',
      liked: result.liked
    });
  } catch (error) {
    console.error('Error liking post:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Comment on post
router.post('/api/posts/:postId/comment', isAuthenticated, async (req, res) => {
  try {
    const { postId } = req.params;
    const { text } = req.body;

    if (!postId) {
      return res.status(400).json({ success: false, message: 'Post ID is required' });
    }

    if (!text) {
      return res.status(400).json({ success: false, message: 'Comment text is required' });
    }

    // Add the comment
    const result = await networkService.addComment(postId, text);

    res.json({ success: true, message: 'Comment added successfully', data: result });
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

export default router;
