<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">User Profile</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-4">
    <div class="card mb-4">
      <div class="card-header bg-success text-white">
        <h4 class="mb-0">User Profile</h4>
      </div>
      <div class="card-body">
        <div class="text-center mb-4">
          <% if (profileUser.photoURL) { %>
            <img src="<%= profileUser.photoURL %>" alt="Profile Picture" class="rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
          <% } else { %>
            <div class="mx-auto rounded-circle bg-light d-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
              <i class="bi bi-person-circle text-muted" style="font-size: 5rem;"></i>
            </div>
          <% } %>
          <h3 class="mb-0"><%= profileUser.displayName || 'User' %></h3>
          <% if (profileUser.farmName) { %>
            <p class="text-muted mb-0"><%= profileUser.farmName %></p>
          <% } %>
        </div>

        <% if (profileUser.location) { %>
          <div class="mb-3">
            <strong><i class="bi bi-geo-alt me-2"></i>Location:</strong>
            <p><%= profileUser.location %></p>
          </div>
        <% } %>

        <% if (profileUser.bio) { %>
          <div class="mb-3">
            <strong><i class="bi bi-info-circle me-2"></i>Bio:</strong>
            <p><%= profileUser.bio %></p>
          </div>
        <% } %>

        <div class="mb-3">
          <strong><i class="bi bi-calendar-check me-2"></i>Member Since:</strong>
          <p><%= profileUser.createdAt ? new Date(profileUser.createdAt).toLocaleDateString() : 'N/A' %></p>
        </div>

        <% if (isOwnProfile) { %>
          <div class="d-grid gap-2">
            <a href="/profile" class="btn btn-outline-success">
              <i class="bi bi-pencil-square me-2"></i>Edit Profile
            </a>
          </div>
        <% } %>
      </div>
    </div>
  </div>

  <div class="col-md-8">
    <div class="card">
      <div class="card-header bg-success text-white">
        <h4 class="mb-0"><%= profileUser.displayName %>'s Uploads</h4>
      </div>
      <div class="card-body">
        <% if (uploads && uploads.length > 0) { %>
          <div class="row">
            <% uploads.forEach(upload => { %>
              <div class="col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                  <% if (upload.fileUrl) { %>
                    <%
                      // Determine file type from either the fileType property or the URL extension
                      let isImage = false;
                      let isVideo = false;
                      let isPdf = false;

                      if (upload.fileType) {
                        // If we have a fileType property, use it
                        isImage = upload.fileType.startsWith('image/');
                        isVideo = upload.fileType.startsWith('video/');
                        isPdf = upload.fileType === 'application/pdf';
                      } else {
                        // Otherwise try to determine from URL
                        const url = upload.fileUrl.toLowerCase();
                        isImage = url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.webp') || url.includes('data:image/');
                        isVideo = url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg') || url.includes('data:video/');
                        isPdf = url.endsWith('.pdf') || url.includes('data:application/pdf');
                      }
                    %>

                    <% if (isImage) { %>
                      <img src="<%= upload.fileUrl %>" class="card-img-top" alt="<%= upload.title %>" style="height: 180px; object-fit: cover;">
                    <% } else if (isVideo) { %>
                      <div class="card-img-top bg-dark d-flex align-items-center justify-content-center" style="height: 180px;">
                        <i class="bi bi-film text-light" style="font-size: 3rem;"></i>
                      </div>
                    <% } else if (isPdf) { %>
                      <div class="card-img-top bg-danger d-flex align-items-center justify-content-center" style="height: 180px;">
                        <i class="bi bi-file-pdf text-light" style="font-size: 3rem;"></i>
                      </div>
                    <% } else { %>
                      <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 180px;">
                        <i class="bi bi-file-earmark text-light" style="font-size: 3rem;"></i>
                      </div>
                    <% } %>
                  <% } else { %>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 180px;">
                      <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                    </div>
                  <% } %>
                  <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                      <h5 class="card-title"><%= upload.title %></h5>
                      <span class="badge bg-secondary"><%= upload.category %></span>
                    </div>
                    <p class="card-text"><%= upload.description %></p>
                  </div>
                  <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                      <small class="text-muted">
                        Posted on <%= new Date(upload.createdAt).toLocaleDateString() %>
                      </small>
                      <a href="<%= upload.fileUrl %>" class="btn btn-sm btn-outline-primary" target="_blank">
                        <i class="bi bi-eye"></i> View
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            <% }); %>
          </div>
        <% } else { %>
          <div class="text-center py-5">
            <div class="mb-4">
              <i class="bi bi-cloud-upload text-muted" style="font-size: 5rem;"></i>
            </div>
            <h3>No Content Uploaded Yet</h3>
            <p class="text-muted">This user hasn't uploaded any content yet.</p>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>
