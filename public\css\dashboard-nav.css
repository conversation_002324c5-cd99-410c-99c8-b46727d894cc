/* Dashboard Navigation Styles */

.dashboard-nav {
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 1rem 0;
  position: sticky;
  top: 76px; /* Adjusted to account for fixed header */
}

.dashboard-nav .nav-item {
  width: 100%;
  margin-bottom: 2px;
}

.dashboard-nav-link {
  color: #333;
  padding: 12px 15px;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  font-weight: 500;
}

.dashboard-nav-link:hover {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
  border-left-color: #198754;
}

.dashboard-nav-link.active {
  background-color: rgba(25, 135, 84, 0.15);
  color: #198754;
  border-left-color: #198754;
}

.dashboard-nav-link i {
  width: 24px;
  text-align: center;
  font-size: 1.1rem;
}

/* Hover effects */
.dashboard-nav-link {
  position: relative;
  overflow: hidden;
}

.dashboard-nav-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: rgba(25, 135, 84, 0.05);
  transition: width 0.3s ease;
  z-index: -1;
}

.dashboard-nav-link:hover::after {
  width: 100%;
}

/* Active link animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 135, 84, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
  }
}

.dashboard-nav-link.active {
  animation: pulse 2s infinite;
}

/* Ripple effect */
.dashboard-nav-link {
  position: relative;
  overflow: hidden;
}

.nav-ripple {
  position: absolute;
  background: rgba(25, 135, 84, 0.3);
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
  width: 100px;
  height: 100px;
  margin-left: -50px;
  margin-top: -50px;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Mobile styles */
@media (max-width: 767.98px) {
  .dashboard-nav {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 5px;
  }

  .dashboard-nav .nav {
    display: flex;
    flex-direction: row;
  }

  .dashboard-nav .nav-item {
    width: auto;
    margin-right: 5px;
    margin-bottom: 0;
  }

  .dashboard-nav-link {
    padding: 8px 12px;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .dashboard-nav-link:hover,
  .dashboard-nav-link.active {
    border-left-color: transparent;
    border-bottom-color: #198754;
  }

  .dashboard-nav-link i {
    margin-right: 0;
    font-size: 1.2rem;
  }

  .dashboard-nav-link span {
    display: none;
  }
}
