<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4">Farming Technique Articles</h1>

    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>

    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <a href="/articles/user/my-articles" class="btn btn-outline-success">
          <i class="bi bi-list-check"></i> My Articles
        </a>
        <a href="/articles/new" class="btn btn-success ms-2">
          <i class="bi bi-plus-circle"></i> Write New Article
        </a>
      </div>

      <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
          Filter by Category
        </button>
        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
          <li><a class="dropdown-item" href="/articles">All Categories</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="/articles/category/sustainable-practices">Sustainable Practices</a></li>
          <li><a class="dropdown-item" href="/articles/category/pest-control">Pest Control</a></li>
          <li><a class="dropdown-item" href="/articles/category/soil-health">Soil Health</a></li>
          <li><a class="dropdown-item" href="/articles/category/water-conservation">Water Conservation</a></li>
          <li><a class="dropdown-item" href="/articles/category/crop-rotation">Crop Rotation</a></li>
          <li><a class="dropdown-item" href="/articles/category/other">Other</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <% if (articles && articles.length > 0) { %>
    <% articles.forEach(article => { %>
      <div class="col-md-6 mb-4">
        <div class="card h-100 shadow-sm">
          <div class="row g-0">
            <div class="col-md-4">
              <% if (article.imageUrl) { %>
                <img src="<%= article.imageUrl %>" class="img-fluid rounded-start h-100" alt="<%= article.title %>" style="object-fit: cover;">
              <% } else { %>
                <div class="bg-light d-flex align-items-center justify-content-center h-100 rounded-start">
                  <i class="bi bi-file-text text-muted" style="font-size: 3rem;"></i>
                </div>
              <% } %>
            </div>
            <div class="col-md-8">
              <div class="card-body d-flex flex-column h-100">
                <h5 class="card-title"><%= article.title %></h5>
                <p class="card-text text-muted small">
                  <span class="badge bg-secondary"><%= article.category %></span>
                  <span class="ms-2"><i class="bi bi-calendar"></i> <%= new Date(article.createdAt).toLocaleDateString() %></span>
                </p>
                <p class="card-text flex-grow-1"><%= article.summary %></p>
                <div class="d-flex justify-content-between align-items-center mt-2">
                  <small class="text-muted">
                    By <%= article.userName %>
                  </small>
                  <a href="/articles/<%= article.id %>" class="btn btn-sm btn-outline-success">Read More</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <div class="col-md-12">
      <div class="text-center py-5">
        <div class="mb-4">
          <i class="bi bi-file-earmark-text text-muted" style="font-size: 5rem;"></i>
        </div>
        <h3>No Articles Available Yet</h3>
        <p class="text-muted mb-4">Be the first to share your farming knowledge with the community!</p>
        <a href="/articles/new" class="btn btn-lg btn-success">
          <i class="bi bi-pencil-square me-2"></i> Write Your First Article
        </a>
      </div>
    </div>
  <% } %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript functionality here
  });
</script>
