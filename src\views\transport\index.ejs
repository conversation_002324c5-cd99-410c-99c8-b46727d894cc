<%- include('../partials/header') %>

<!-- Transport CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-3">
      <!-- Sidebar -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            Transport Services
          </h5>
        </div>
        <div class="p-3">
          <div class="d-grid gap-2">
            <a href="/transport" class="linkedin-btn linkedin-btn-primary">
              <i class="bi bi-house-door-fill me-2"></i> Home
            </a>
            <a href="/transport/my-bookings" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-calendar-check me-2"></i> My Bookings
            </a>
            <% if (userData && userData.isDriver) { %>
              <a href="/transport/driver-dashboard" class="linkedin-btn linkedin-btn-outline">
                <i class="bi bi-speedometer2 me-2"></i> Driver Dashboard
              </a>
            <% } else { %>
              <a href="/transport/register-driver" class="linkedin-btn linkedin-btn-outline">
                <i class="bi bi-person-plus-fill me-2"></i> Register as Driver
              </a>
            <% } %>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-9">
      <!-- Main Content -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            Book Transport for Your Goods
          </h5>
        </div>
        <div class="linkedin-card-body p-4">
          <div class="row">
            <div class="col-md-6">
              <h4>Need to transport your goods?</h4>
              <p>Connect with local drivers to transport your:</p>
              <ul>
                <li>Harvested crops</li>
                <li>Farming equipment</li>
                <li>Seeds and fertilizers</li>
                <li>Raw materials</li>
                <li>Livestock feed</li>
              </ul>
              <p>Browse available drivers below and book transport services directly through our platform.</p>
            </div>
            <div class="col-md-6">
              <img src="/img/transport-illustration.jpg" alt="Transport illustration" class="img-fluid rounded" style="max-height: 250px; width: 100%; object-fit: cover;">
            </div>
          </div>
        </div>
      </div>

      <!-- Index Error Alert -->
      <% if (typeof indexError !== 'undefined' && indexError) { %>
        <div class="alert alert-warning mb-4">
          <i class="bi bi-exclamation-triangle-fill me-2"></i>
          <strong>Service Notice:</strong> <%= errorMessage %>
        </div>
      <% } %>

      <!-- Available Drivers -->
      <div class="linkedin-card">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-people me-2" style="color: var(--network-primary);"></i>
            Available Drivers
          </h5>
        </div>
        <div class="linkedin-card-body p-0">
          <% if (drivers && drivers.length > 0) { %>
            <div class="row m-0">
              <% drivers.forEach(driver => { %>
                <div class="col-md-6 mb-3 p-3">
                  <div class="linkedin-card h-100 hover-shadow">
                    <div class="p-3">
                      <div class="d-flex align-items-center mb-3">
                        <div class="position-relative">
                          <% if (driver.user && driver.user.photoURL) { %>
                            <img src="<%= driver.user.photoURL %>" alt="Driver photo" class="connection-photo" style="width: 80px; height: 80px; border: 3px solid var(--network-primary-light); padding: 2px;">
                          <% } else { %>
                            <div class="connection-photo d-flex align-items-center justify-content-center bg-light" style="width: 80px; height: 80px; border-radius: 50%; border: 3px solid var(--network-primary-light); padding: 2px;">
                              <i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>
                            </div>
                          <% } %>
                          <span class="online-indicator position-absolute" style="bottom: 5px; right: 5px; background-color: var(--network-primary);"></span>
                        </div>
                        <div class="ms-3">
                          <h6 class="connection-name fw-bold mb-1"><%= driver.user ? driver.user.displayName : 'Driver' %></h6>
                          <div class="small text-muted">
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <%= driver.rating || '0.0' %> (<%= driver.totalRatings || 0 %> ratings)
                          </div>
                          <div class="small text-muted">
                            <i class="bi bi-calendar-check me-1"></i>
                            <%= driver.experience || 0 %> years experience
                          </div>
                        </div>
                      </div>
                      <p class="small text-muted mb-3"><%= driver.bio || 'No bio available' %></p>
                      <div class="d-grid">
                        <a href="/transport/book/<%= driver.id %>" class="linkedin-btn linkedin-btn-primary">
                          <i class="bi bi-calendar-plus me-1"></i> Book Transport
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              <% }); %>
            </div>
          <% } else { %>
            <div class="p-5 text-center">
              <i class="bi bi-truck text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Drivers Available</h5>
              <p class="text-muted">
                <% if (typeof indexError !== 'undefined' && indexError) { %>
                  Please wait while the service is being configured.
                <% } else { %>
                  There are no drivers available at the moment. Please check back later.
                <% } %>
              </p>
              <% if (typeof indexError === 'undefined' || !indexError) { %>
                <a href="/transport/register-driver" class="linkedin-btn linkedin-btn-primary mt-2">
                  <i class="bi bi-person-plus me-1"></i> Register as a Driver
                </a>
              <% } %>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/toast-notifications.js"></script>

<%- include('../partials/footer') %>
