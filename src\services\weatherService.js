import axios from 'axios';

// OpenWeatherMap API key
const API_KEY = '********************************';
const BASE_URL = 'https://api.openweathermap.org/data/2.5';
const ONE_CALL_URL = 'https://api.openweathermap.org/data/3.0/onecall';
const AIR_QUALITY_URL = 'https://api.openweathermap.org/data/2.5/air_pollution';


/**
 * Get current weather data for a location
 * @param {string} lat - Latitude
 * @param {string} lon - Longitude
 * @returns {Promise} - Weather data
 */
async function getCurrentWeather(lat, lon) {
  try {
    console.log(`Fetching current weather for lat: ${lat}, lon: ${lon}`);

    const response = await axios.get(`${BASE_URL}/weather`, {
      params: {
        lat,
        lon,
        appid: API_KEY,
        units: 'metric', // Use metric units (Celsius)
        lang: 'en'       // Use English language
      }
    });

    console.log('Current weather API response:', response.status);
    return response.data;
  } catch (error) {
    console.error('Error fetching current weather:', error.response ? error.response.data : error.message);

    // Throw a more descriptive error
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data.message || 'Unknown error';

      if (status === 401) {
        throw new Error('API key is invalid or expired');
      } else if (status === 404) {
        throw new Error('Location not found');
      } else if (status === 429) {
        throw new Error('Too many requests. Please try again later');
      } else {
        throw new Error(`Weather API error (${status}): ${message}`);
      }
    }

    throw error;
  }
}

/**
 * Get weather forecast for a location
 * @param {string} lat - Latitude
 * @param {string} lon - Longitude
 * @param {number} days - Number of days for forecast (max 7)
 * @returns {Promise} - Forecast data
 */
async function getWeatherForecast(lat, lon, days = 5) {
  try {
    console.log(`Fetching forecast for lat: ${lat}, lon: ${lon}, days: ${days}`);

    const response = await axios.get(`${BASE_URL}/forecast`, {
      params: {
        lat,
        lon,
        appid: API_KEY,
        units: 'metric',
        cnt: days * 8, // Each day has 8 data points (every 3 hours)
        lang: 'en'     // Use English language
      }
    });

    console.log('Forecast API response:', response.status);
    return response.data;
  } catch (error) {
    console.error('Error fetching weather forecast:', error.response ? error.response.data : error.message);

    // Throw a more descriptive error
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data.message || 'Unknown error';

      if (status === 401) {
        throw new Error('API key is invalid or expired');
      } else if (status === 404) {
        throw new Error('Location not found');
      } else if (status === 429) {
        throw new Error('Too many requests. Please try again later');
      } else {
        throw new Error(`Weather API error (${status}): ${message}`);
      }
    }

    throw error;
  }
}

/**
 * Get weather data for a city by name
 * @param {string} cityName - Name of the city
 * @returns {Promise} - Weather data
 */
async function getWeatherByCity(cityName) {
  try {
    console.log(`Fetching current weather for city: ${cityName}`);

    const response = await axios.get(`${BASE_URL}/weather`, {
      params: {
        q: cityName,
        appid: API_KEY,
        units: 'metric',
        lang: 'en'
      }
    });

    console.log('City weather API response:', response.status);
    return response.data;
  } catch (error) {
    console.error('Error fetching weather by city:', error.response ? error.response.data : error.message);

    // Throw a more descriptive error
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data.message || 'Unknown error';

      if (status === 401) {
        throw new Error('API key is invalid or expired');
      } else if (status === 404) {
        throw new Error(`City "${cityName}" not found. Please check the spelling and try again.`);
      } else if (status === 429) {
        throw new Error('Too many requests. Please try again later');
      } else {
        throw new Error(`Weather API error (${status}): ${message}`);
      }
    }

    throw error;
  }
}

/**
 * Get weather forecast for a city by name
 * @param {string} cityName - Name of the city
 * @param {number} days - Number of days for forecast (max 7)
 * @returns {Promise} - Forecast data
 */
async function getForecastByCity(cityName, days = 5) {
  try {
    console.log(`Fetching forecast for city: ${cityName}, days: ${days}`);

    const response = await axios.get(`${BASE_URL}/forecast`, {
      params: {
        q: cityName,
        appid: API_KEY,
        units: 'metric',
        cnt: days * 8,
        lang: 'en'
      }
    });

    console.log('City forecast API response:', response.status);
    return response.data;
  } catch (error) {
    console.error('Error fetching forecast by city:', error.response ? error.response.data : error.message);

    // Throw a more descriptive error
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data.message || 'Unknown error';

      if (status === 401) {
        throw new Error('API key is invalid or expired');
      } else if (status === 404) {
        throw new Error(`City "${cityName}" not found. Please check the spelling and try again.`);
      } else if (status === 429) {
        throw new Error('Too many requests. Please try again later');
      } else {
        throw new Error(`Weather API error (${status}): ${message}`);
      }
    }

    throw error;
  }
}

/**
 * Format weather data for display
 * @param {Object} weatherData - Raw weather data from API
 * @returns {Object} - Formatted weather data
 */
function formatWeatherData(weatherData) {
  if (!weatherData) return null;

  return {
    location: weatherData.name,
    country: weatherData.sys.country,
    temperature: Math.round(weatherData.main.temp),
    feelsLike: Math.round(weatherData.main.feels_like),
    description: weatherData.weather[0].description,
    icon: weatherData.weather[0].icon,
    humidity: weatherData.main.humidity,
    windSpeed: weatherData.wind.speed,
    sunrise: new Date(weatherData.sys.sunrise * 1000).toLocaleTimeString(),
    sunset: new Date(weatherData.sys.sunset * 1000).toLocaleTimeString(),
    date: new Date(weatherData.dt * 1000).toLocaleDateString()
  };
}

/**
 * Format forecast data for display
 * @param {Object} forecastData - Raw forecast data from API
 * @returns {Array} - Formatted forecast data grouped by day
 */
function formatForecastData(forecastData) {
  if (!forecastData || !forecastData.list) return [];

  // Group forecast data by day
  const dailyForecasts = {};

  forecastData.list.forEach(item => {
    const date = new Date(item.dt * 1000).toLocaleDateString();

    if (!dailyForecasts[date]) {
      dailyForecasts[date] = {
        date,
        temperatures: [],
        icons: [],
        descriptions: []
      };
    }

    dailyForecasts[date].temperatures.push(item.main.temp);
    dailyForecasts[date].icons.push(item.weather[0].icon);
    dailyForecasts[date].descriptions.push(item.weather[0].description);
  });

  // Convert to array and calculate averages
  return Object.values(dailyForecasts).map(day => {
    const mostFrequentIcon = findMostFrequent(day.icons);
    const mostFrequentDescription = findMostFrequent(day.descriptions);

    return {
      date: day.date,
      avgTemp: Math.round(day.temperatures.reduce((sum, temp) => sum + temp, 0) / day.temperatures.length),
      minTemp: Math.round(Math.min(...day.temperatures)),
      maxTemp: Math.round(Math.max(...day.temperatures)),
      icon: mostFrequentIcon,
      description: mostFrequentDescription
    };
  });
}

/**
 * Find the most frequent item in an array
 * @param {Array} arr - Array of items
 * @returns {*} - Most frequent item
 */
function findMostFrequent(arr) {
  const counts = {};
  let maxItem = arr[0];
  let maxCount = 1;

  for (const item of arr) {
    counts[item] = (counts[item] || 0) + 1;
    if (counts[item] > maxCount) {
      maxCount = counts[item];
      maxItem = item;
    }
  }

  return maxItem;
}

/**
 * Get air quality data for a location
 * @param {string} lat - Latitude
 * @param {string} lon - Longitude
 * @returns {Promise} - Air quality data
 */
async function getAirQuality(lat, lon) {
  try {
    console.log(`Fetching air quality for lat: ${lat}, lon: ${lon}`);

    const response = await axios.get(AIR_QUALITY_URL, {
      params: {
        lat,
        lon,
        appid: API_KEY
      }
    });

    console.log('Air quality API response:', response.status);
    return response.data;
  } catch (error) {
    console.error('Error fetching air quality:', error.response ? error.response.data : error.message);

    // Throw a more descriptive error
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data.message || 'Unknown error';

      if (status === 401) {
        throw new Error('API key is invalid or expired');
      } else if (status === 404) {
        throw new Error('Location not found');
      } else if (status === 429) {
        throw new Error('Too many requests. Please try again later');
      } else {
        throw new Error(`Air quality API error (${status}): ${message}`);
      }
    }

    throw error;
  }
}

/**
 * Get comprehensive weather data including current, forecast, and alerts
 * @param {string} lat - Latitude
 * @param {string} lon - Longitude
 * @returns {Promise} - Comprehensive weather data
 */
async function getOneCallWeather(lat, lon) {
  try {
    console.log(`Fetching comprehensive weather for lat: ${lat}, lon: ${lon}`);

    const response = await axios.get(ONE_CALL_URL, {
      params: {
        lat,
        lon,
        appid: API_KEY,
        units: 'metric',
        exclude: 'minutely', // Exclude minutely data to reduce response size
        lang: 'en'
      }
    });

    console.log('One Call API response:', response.status);
    return response.data;
  } catch (error) {
    console.error('Error fetching comprehensive weather:', error.response ? error.response.data : error.message);

    // Throw a more descriptive error
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data.message || 'Unknown error';

      if (status === 401) {
        throw new Error('API key is invalid or expired');
      } else if (status === 404) {
        throw new Error('Location not found');
      } else if (status === 429) {
        throw new Error('Too many requests. Please try again later');
      } else {
        throw new Error(`One Call API error (${status}): ${message}`);
      }
    }

    throw error;
  }
}

/**
 * Format air quality data for display
 * @param {Object} airQualityData - Raw air quality data from API
 * @returns {Object} - Formatted air quality data
 */
function formatAirQualityData(airQualityData) {
  if (!airQualityData || !airQualityData.list || airQualityData.list.length === 0) return null;

  const data = airQualityData.list[0];

  // AQI levels according to OpenWeatherMap
  // 1: Good, 2: Fair, 3: Moderate, 4: Poor, 5: Very Poor
  const aqiLabels = {
    1: { label: 'Good', color: '#00e400' },
    2: { label: 'Fair', color: '#ffff00' },
    3: { label: 'Moderate', color: '#ff7e00' },
    4: { label: 'Poor', color: '#ff0000' },
    5: { label: 'Very Poor', color: '#99004c' }
  };

  const aqi = data.main.aqi;
  const aqiInfo = aqiLabels[aqi] || { label: 'Unknown', color: '#cccccc' };

  return {
    aqi: aqi,
    label: aqiInfo.label,
    color: aqiInfo.color,
    components: {
      co: data.components.co,         // Carbon monoxide (μg/m³)
      no: data.components.no,         // Nitrogen monoxide (μg/m³)
      no2: data.components.no2,       // Nitrogen dioxide (μg/m³)
      o3: data.components.o3,         // Ozone (μg/m³)
      so2: data.components.so2,       // Sulphur dioxide (μg/m³)
      pm2_5: data.components.pm2_5,   // Fine particles (μg/m³)
      pm10: data.components.pm10,     // Coarse particles (μg/m³)
      nh3: data.components.nh3        // Ammonia (μg/m³)
    },
    timestamp: new Date(data.dt * 1000).toLocaleString()
  };
}

/**
 * Format comprehensive weather data for display
 * @param {Object} oneCallData - Raw data from One Call API
 * @returns {Object} - Formatted comprehensive weather data
 */
function formatOneCallData(oneCallData) {
  if (!oneCallData) return null;

  // Format current weather
  const current = {
    temperature: Math.round(oneCallData.current.temp),
    feelsLike: Math.round(oneCallData.current.feels_like),
    humidity: oneCallData.current.humidity,
    windSpeed: oneCallData.current.wind_speed,
    windDirection: oneCallData.current.wind_deg,
    pressure: oneCallData.current.pressure,
    uvIndex: oneCallData.current.uvi,
    visibility: oneCallData.current.visibility / 1000, // Convert to km
    description: oneCallData.current.weather[0].description,
    icon: oneCallData.current.weather[0].icon,
    timestamp: new Date(oneCallData.current.dt * 1000).toLocaleString()
  };

  // Format daily forecast
  const daily = oneCallData.daily.map(day => ({
    date: new Date(day.dt * 1000).toLocaleDateString(),
    sunrise: new Date(day.sunrise * 1000).toLocaleTimeString(),
    sunset: new Date(day.sunset * 1000).toLocaleTimeString(),
    tempDay: Math.round(day.temp.day),
    tempNight: Math.round(day.temp.night),
    tempMin: Math.round(day.temp.min),
    tempMax: Math.round(day.temp.max),
    feelsLikeDay: Math.round(day.feels_like.day),
    feelsLikeNight: Math.round(day.feels_like.night),
    pressure: day.pressure,
    humidity: day.humidity,
    windSpeed: day.wind_speed,
    windDirection: day.wind_deg,
    description: day.weather[0].description,
    icon: day.weather[0].icon,
    uvIndex: day.uvi,
    pop: Math.round(day.pop * 100), // Probability of precipitation (%)
    rain: day.rain || 0, // Rain volume in mm (if available)
    snow: day.snow || 0  // Snow volume in mm (if available)
  }));

  // Format hourly forecast
  const hourly = oneCallData.hourly.map(hour => ({
    time: new Date(hour.dt * 1000).toLocaleTimeString(),
    temperature: Math.round(hour.temp),
    feelsLike: Math.round(hour.feels_like),
    humidity: hour.humidity,
    description: hour.weather[0].description,
    icon: hour.weather[0].icon,
    pop: Math.round(hour.pop * 100) // Probability of precipitation (%)
  }));

  // Format alerts (if available)
  const alerts = oneCallData.alerts ? oneCallData.alerts.map(alert => ({
    senderName: alert.sender_name,
    event: alert.event,
    start: new Date(alert.start * 1000).toLocaleString(),
    end: new Date(alert.end * 1000).toLocaleString(),
    description: alert.description
  })) : [];

  return {
    lat: oneCallData.lat,
    lon: oneCallData.lon,
    timezone: oneCallData.timezone,
    current,
    daily,
    hourly,
    alerts
  };
}

export {
  getCurrentWeather,
  getWeatherForecast,
  getWeatherByCity,
  getForecastByCity,
  getAirQuality,
  getOneCallWeather,
  formatWeatherData,
  formatForecastData,
  formatAirQualityData,
  formatOneCallData
};
