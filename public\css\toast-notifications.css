/* Toast Notifications CSS */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 350px;
}

.toast {
  background-color: white;
  color: #333;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(100%);
  position: relative;
  border-left: 4px solid #4CAF50;
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast.hide {
  opacity: 0;
  transform: translateX(100%);
}

.toast-icon {
  margin-right: 12px;
  font-size: 20px;
  flex-shrink: 0;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 16px;
}

.toast-message {
  font-size: 14px;
  margin: 0;
}

.toast-close {
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  transition: color 0.2s;
}

.toast-close:hover {
  color: #333;
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.1);
  width: 100%;
}

.toast-progress-bar {
  height: 100%;
  background-color: #4CAF50;
  width: 100%;
}

/* Toast types */
.toast-success {
  border-left-color: #4CAF50;
}

.toast-success .toast-icon {
  color: #4CAF50;
}

.toast-success .toast-progress-bar {
  background-color: #4CAF50;
}

.toast-error {
  border-left-color: #F44336;
}

.toast-error .toast-icon {
  color: #F44336;
}

.toast-error .toast-progress-bar {
  background-color: #F44336;
}

.toast-info {
  border-left-color: #2196F3;
}

.toast-info .toast-icon {
  color: #2196F3;
}

.toast-info .toast-progress-bar {
  background-color: #2196F3;
}

.toast-warning {
  border-left-color: #FF9800;
}

.toast-warning .toast-icon {
  color: #FF9800;
}

.toast-warning .toast-progress-bar {
  background-color: #FF9800;
}

/* Animation */
@keyframes progress {
  from { width: 100%; }
  to { width: 0%; }
}
