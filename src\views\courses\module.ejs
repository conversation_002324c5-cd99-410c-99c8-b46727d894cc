<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/">Home</a></li>
          <li class="breadcrumb-item"><a href="/courses">Courses</a></li>
          <li class="breadcrumb-item"><a href="/courses/<%= course.id %>"><%= course.title %></a></li>
          <li class="breadcrumb-item active" aria-current="page"><%= module.title %></li>
        </ol>
      </nav>
    </div>
  </div>

  <% if (typeof error !== 'undefined') { %>
    <div class="row">
      <div class="col-md-12">
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <!-- Module Content -->
    <div class="col-md-8">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0"><%= module.title %></h4>
          <span class="badge bg-light text-dark">
            <i class="bi bi-clock"></i> <%= module.duration %> min
          </span>
        </div>
        <div class="card-body">
          <% if (module.videoUrl) { %>
            <div class="ratio ratio-16x9 mb-4">
              <iframe src="<%= module.videoUrl %>" title="<%= module.title %>" allowfullscreen></iframe>
            </div>
          <% } %>

          <div class="module-content">
            <%- module.content %>
          </div>

          <% if (module.resources && module.resources.length > 0) { %>
            <div class="mt-4">
              <h5>Resources</h5>
              <div class="list-group">
                <% module.resources.forEach(resource => { %>
                  <a href="<%= resource.url %>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" target="_blank">
                    <div>
                      <% if (resource.type === 'pdf') { %>
                        <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                      <% } else if (resource.type === 'doc') { %>
                        <i class="bi bi-file-earmark-word text-primary me-2"></i>
                      <% } else if (resource.type === 'xls') { %>
                        <i class="bi bi-file-earmark-excel text-success me-2"></i>
                      <% } else { %>
                        <i class="bi bi-file-earmark text-secondary me-2"></i>
                      <% } %>
                      <strong><%= resource.name %></strong>
                      <% if (resource.description) { %>
                        <p class="mb-0 text-muted small"><%= resource.description %></p>
                      <% } %>
                    </div>
                    <i class="bi bi-download"></i>
                  </a>
                <% }); %>
              </div>
            </div>
          <% } %>

          <div class="d-flex justify-content-between mt-4">
            <% if (prevModule) { %>
              <a href="/courses/<%= course.id %>/module/<%= prevModule.id %>" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Previous: <%= prevModule.title %>
              </a>
            <% } else { %>
              <a href="/courses/<%= course.id %>" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Course
              </a>
            <% } %>

            <% if (progress && progress.completedModules && progress.completedModules.includes(module.id)) { %>
              <% if (nextModule) { %>
                <a href="/courses/<%= course.id %>/module/<%= nextModule.id %>" class="btn btn-success">
                  Next: <%= nextModule.title %> <i class="bi bi-arrow-right"></i>
                </a>
              <% } else { %>
                <a href="/courses/<%= course.id %>/complete" class="btn btn-success">
                  Complete Course <i class="bi bi-check-circle"></i>
                </a>
              <% } %>
            <% } else { %>
              <form action="/courses/<%= course.id %>/module/<%= module.id %>/complete" method="POST">
                <button type="submit" class="btn btn-success">
                  Mark as Complete <i class="bi bi-check-circle"></i>
                </button>
              </form>
            <% } %>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Progress</h5>
        </div>
        <div class="card-body">
          <div class="progress mb-3" style="height: 10px;">
            <div class="progress-bar bg-success" role="progressbar" style="width: <%= progress.progress %>%;" aria-valuenow="<%= progress.progress %>" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
          <p class="text-center"><%= progress.progress %>% complete</p>
          
          <h6 class="mb-3">Course Modules</h6>
          <div class="list-group">
            <% modules.forEach((mod, index) => { %>
              <a href="/courses/<%= course.id %>/module/<%= mod.id %>" 
                class="list-group-item list-group-item-action d-flex justify-content-between align-items-center
                      <%= mod.id === module.id ? 'active' : '' %>">
                <div>
                  <div class="fw-bold">Module <%= index + 1 %></div>
                  <small><%= mod.title %></small>
                </div>
                <% if (progress.completedModules && progress.completedModules.includes(mod.id)) { %>
                  <span class="badge bg-success rounded-pill">
                    <i class="bi bi-check"></i>
                  </span>
                <% } else if (mod.id === module.id) { %>
                  <span class="badge bg-primary rounded-pill">
                    <i class="bi bi-play-fill"></i>
                  </span>
                <% } %>
              </a>
            <% }); %>
          </div>
        </div>
      </div>

      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Quick Notes</h5>
        </div>
        <div class="card-body">
          <form action="/courses/<%= course.id %>/notes" method="POST">
            <div class="mb-3">
              <textarea class="form-control" name="notes" rows="5" placeholder="Take notes while studying this module..."><%= progress.notes || '' %></textarea>
            </div>
            <div class="d-grid">
              <button type="submit" class="btn btn-success">
                <i class="bi bi-save"></i> Save Notes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
