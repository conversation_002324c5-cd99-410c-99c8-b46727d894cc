<%- include('../partials/header') %>

<!-- Facebook Messenger Style CSS -->
<link rel="stylesheet" href="/css/facebook-messenger.css">

<div class="messenger-container">
  <!-- Left Sidebar - Conversation List -->
  <div class="conversation-list-panel">
    <!-- Conversation List Header -->
    <div class="conversation-list-header">
      <div class="conversation-list-title">Chats</div>
      <div class="conversation-list-actions">
        <div class="conversation-list-icon">
          <i class="bi bi-three-dots"></i>
        </div>
        <div class="conversation-list-icon" id="new-message-btn">
          <i class="bi bi-pencil-square"></i>
        </div>
      </div>
    </div>

    <!-- Search Box -->
    <div class="search-container">
      <div class="search-box">
        <i class="bi bi-search search-icon"></i>
        <input type="text" placeholder="Search Messenger" id="search-conversations">
      </div>
    </div>

    <!-- Index Error Alert -->
    <% if (typeof indexError !== 'undefined' && indexError) { %>
      <div class="alert alert-warning m-3">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <strong>Database Index Building:</strong> <%= errorMessage %>
      </div>
    <% } %>

    <!-- Conversation List -->
    <div class="conversation-list">
      <% if (conversations && conversations.length > 0) { %>
        <% conversations.forEach(conversation => { %>
          <a href="/messaging/<%= conversation.id %>" class="text-decoration-none">
            <div class="conversation-item <%= activeConversation && activeConversation.id === conversation.id ? 'active' : '' %>">
              <% if (conversation.otherParticipant.photoURL) { %>
                <div class="conversation-avatar">
                  <img src="<%= conversation.otherParticipant.photoURL %>" class="w-100 h-100 rounded-circle">
                  <div class="online-indicator"></div>
                </div>
              <% } else { %>
                <div class="conversation-avatar">
                  <div class="w-100 h-100 rounded-circle d-flex align-items-center justify-content-center bg-light">
                    <i class="bi bi-person-fill text-secondary"></i>
                  </div>
                  <div class="online-indicator"></div>
                </div>
              <% } %>
              <div class="conversation-content">
                <div class="conversation-header">
                  <div class="conversation-name"><%= conversation.otherParticipant.displayName || 'User' %></div>
                  <% if (conversation.lastMessageTimestamp) { %>
                    <div class="conversation-time">
                      <%= new Date(conversation.lastMessageTimestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                    </div>
                  <% } %>
                </div>
                <div class="conversation-message">
                  <% if (conversation.lastMessage) { %>
                    <% if (conversation.lastMessage.senderId === user.uid) { %>
                      <div class="conversation-message-status">
                        <i class="bi bi-check-circle-fill"></i>
                      </div>
                    <% } %>
                    <div class="conversation-message-preview">
                      <% if (conversation.lastMessage.senderId === user.uid) { %>
                        <span>You: </span>
                      <% } %>
                      <%= conversation.lastMessage.text %>
                    </div>
                  <% } else { %>
                    <div class="conversation-message-preview">No messages yet</div>
                  <% } %>
                </div>
              </div>
              <% if (conversation.unreadCount && conversation.unreadCount > 0) { %>
                <div class="conversation-unread"><%= conversation.unreadCount %></div>
              <% } %>
            </div>
          </a>
        <% }); %>
      <% } else { %>
        <div class="text-center py-5">
          <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
          <h5 class="mt-3">No Conversations Yet</h5>
          <p class="text-muted">
            <% if (typeof indexError !== 'undefined' && indexError) { %>
              Please wait while the database index is being built.
            <% } else { %>
              Start a new conversation with your connections.
            <% } %>
          </p>
          <button class="btn btn-primary" id="start-conversation-btn">New Message</button>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Main Content - Messages -->
  <div class="chat-panel">
    <% if (activeConversation) { %>
      <!-- Chat Header -->
      <div class="chat-header">
        <div class="chat-header-info">
          <button class="chat-header-icon d-none chat-back-button">
            <i class="bi bi-arrow-left"></i>
          </button>
          <% if (activeConversation.otherParticipant.photoURL) { %>
            <div class="chat-avatar">
              <img src="<%= activeConversation.otherParticipant.photoURL %>" class="w-100 h-100 rounded-circle">
              <div class="online-indicator"></div>
            </div>
          <% } else { %>
            <div class="chat-avatar">
              <div class="w-100 h-100 rounded-circle d-flex align-items-center justify-content-center bg-light">
                <i class="bi bi-person-fill text-secondary"></i>
              </div>
              <div class="online-indicator"></div>
            </div>
          <% } %>
          <div>
            <div class="chat-name"><%= activeConversation.otherParticipant.displayName || 'User' %></div>
            <div class="chat-status">Active now</div>
          </div>
        </div>
        <div class="chat-header-actions">
          <div class="chat-header-icon">
            <i class="bi bi-telephone"></i>
          </div>
          <div class="chat-header-icon">
            <i class="bi bi-camera-video"></i>
          </div>
          <div class="chat-header-icon" id="info-toggle-btn">
            <i class="bi bi-info-circle"></i>
          </div>
        </div>
      </div>

      <!-- Messages Container -->
      <div class="messages-container" id="messages-container">
        <% if (messages && messages.length > 0) { %>
          <% messages.forEach(message => { %>
            <div class="message <%= message.senderId === user.uid ? 'outgoing' : 'incoming' %>">
              <div class="message-bubble">
                <div class="message-text"><%= message.text %></div>
                <% if (message.attachmentURL) { %>
                  <div class="mt-2">
                    <img src="<%= message.attachmentURL %>" class="img-fluid rounded" style="max-height: 200px;">
                  </div>
                <% } %>
              </div>
              <div class="message-time">
                <%= new Date(message.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                <% if (message.senderId === user.uid) { %>
                  <span class="message-status <%= message.read ? 'read' : 'delivered' %>">
                    <i class="bi bi-check-circle-fill"></i>
                  </span>
                <% } %>
              </div>
            </div>
          <% }); %>
        <% } else { %>
          <div class="text-center py-5">
            <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3">No Messages Yet</h5>
            <p class="text-muted">Start the conversation by sending a message below.</p>
          </div>
        <% } %>
      </div>

      <!-- Chat Footer -->
      <div class="chat-footer">
        <div class="chat-footer-icon emoji-btn">
          <i class="bi bi-emoji-smile"></i>
        </div>
        <div class="chat-footer-icon" id="attachment-btn">
          <i class="bi bi-paperclip"></i>
        </div>
        <div class="message-input-container">
          <input type="text" class="message-input" placeholder="Aa" id="message-input">
        </div>
        <button class="send-button" id="send-message-btn">
          <i class="bi bi-send-fill"></i>
        </button>
        <input type="file" id="attachment-input" class="d-none" accept="image/*">
        <div id="attachment-preview" class="mt-2 d-none">
          <div class="position-relative d-inline-block">
            <img id="attachment-image" class="img-thumbnail" style="max-height: 100px;">
            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0" id="remove-attachment-btn">
              <i class="bi bi-x"></i>
            </button>
          </div>
        </div>
      </div>
    <% } else { %>
      <!-- Empty Chat State -->
      <div class="empty-chat">
        <img src="https://static.xx.fbcdn.net/rsrc.php/v3/y2/r/LwwGJGYECXc.png" class="empty-chat-icon">
        <h1 class="empty-chat-title">Your Messages</h1>
        <p class="empty-chat-subtitle">
          Select a chat to start messaging with farmers in your network.
        </p>
      </div>
    <% } %>
  </div>

  <!-- Right Sidebar - Info Panel -->
  <% if (activeConversation) { %>
    <div class="info-panel">
      <div class="info-header">
        <% if (activeConversation.otherParticipant.photoURL) { %>
          <img src="<%= activeConversation.otherParticipant.photoURL %>" class="info-avatar">
        <% } else { %>
          <div class="info-avatar d-flex align-items-center justify-content-center bg-light">
            <i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>
          </div>
        <% } %>
        <div class="info-name"><%= activeConversation.otherParticipant.displayName || 'User' %></div>
        <div class="info-status">Active now</div>
      </div>

      <div class="info-section">
        <div class="info-section-title">Customize Chat</div>
        <div class="d-flex justify-content-between mb-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-palette me-3 text-primary"></i>
            <span>Change Theme</span>
          </div>
          <i class="bi bi-chevron-right text-secondary"></i>
        </div>
        <div class="d-flex justify-content-between">
          <div class="d-flex align-items-center">
            <i class="bi bi-emoji-smile me-3 text-warning"></i>
            <span>Change Emoji</span>
          </div>
          <i class="bi bi-chevron-right text-secondary"></i>
        </div>
      </div>

      <div class="info-section">
        <div class="info-section-title">Privacy & Support</div>
        <div class="d-flex justify-content-between mb-3">
          <div class="d-flex align-items-center">
            <i class="bi bi-bell me-3 text-danger"></i>
            <span>Mute Notifications</span>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="muteNotifications">
          </div>
        </div>
        <div class="d-flex justify-content-between">
          <div class="d-flex align-items-center">
            <i class="bi bi-shield-exclamation me-3 text-secondary"></i>
            <span>Report</span>
          </div>
          <i class="bi bi-chevron-right text-secondary"></i>
        </div>
      </div>

      <div class="info-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="info-section-title mb-0">Shared Media</div>
          <span class="text-primary">See All</span>
        </div>
        <div class="shared-media">
          <% if (messages && messages.length > 0) { %>
            <%
              const mediaMessages = messages.filter(message => message.attachmentURL);
              const mediaToShow = mediaMessages.slice(0, 6);

              if (mediaToShow.length > 0) {
                mediaToShow.forEach(message => { %>
                  <div class="shared-media-item">
                    <img src="<%= message.attachmentURL %>" alt="Shared media">
                  </div>
                <% });
              } else { %>
                <div class="col-12 text-center text-muted py-3">
                  No shared media
                </div>
              <% }
            %>
          <% } else { %>
            <div class="col-12 text-center text-muted py-3">
              No shared media
            </div>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- New Message Modal -->
<div class="modal fade new-message-modal" id="newMessageModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background-color: var(--fb-blue); color: white;">
        <h5 class="modal-title">New Message</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="contact-search">
          <div class="search-box" style="background-color: var(--fb-panel-background); border-radius: 50px;">
            <i class="bi bi-search search-icon"></i>
            <input type="text" id="contact-search" placeholder="Search people" style="background-color: transparent;">
          </div>
        </div>
        <div id="contacts-list" class="contact-list">
          <!-- Contacts will be loaded here -->
          <div class="text-center py-3">
            <div class="spinner-border" style="color: var(--fb-blue);" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Scroll to bottom of messages container
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // New message button
    const newMessageBtn = document.getElementById('new-message-btn');
    const startConversationBtn = document.getElementById('start-conversation-btn');
    const selectContactBtn = document.getElementById('select-contact-btn');

    const openNewMessageModal = () => {
      // Initialize the modal
      const newMessageModal = new bootstrap.Modal(document.getElementById('newMessageModal'));

      // Load contacts
      loadContacts();

      // Show the modal
      newMessageModal.show();
    };

    if (newMessageBtn) {
      newMessageBtn.addEventListener('click', openNewMessageModal);
    }

    if (startConversationBtn) {
      startConversationBtn.addEventListener('click', openNewMessageModal);
    }

    if (selectContactBtn) {
      selectContactBtn.addEventListener('click', openNewMessageModal);
    }

    // Message input and send button
    const messageInput = document.getElementById('message-input');
    const sendMessageBtn = document.getElementById('send-message-btn');

    if (messageInput && sendMessageBtn) {
      // Send message when clicking the send button
      sendMessageBtn.addEventListener('click', function() {
        sendMessage();
      });

      // Send message when pressing Enter (without Shift)
      messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });
    }

    // Mobile back button
    const chatBackButton = document.querySelector('.chat-back-button');
    if (chatBackButton) {
      chatBackButton.addEventListener('click', function() {
        const conversationPanel = document.querySelector('.conversation-panel');
        const chatPanel = document.querySelector('.chat-panel');

        if (conversationPanel && chatPanel) {
          conversationPanel.classList.remove('hidden');
          chatPanel.style.display = 'none';
        }
      });
    }

    // Handle mobile view
    function handleMobileView() {
      const conversationPanel = document.querySelector('.conversation-panel');
      const chatPanel = document.querySelector('.chat-panel');
      const chatBackButton = document.querySelector('.chat-back-button');

      if (window.innerWidth <= 768 && conversationPanel && chatPanel) {
        if (window.location.pathname.includes('/messaging/') &&
            !window.location.pathname.endsWith('/messaging/')) {
          // Show chat panel and hide conversation panel on mobile when in a conversation
          conversationPanel.classList.add('hidden');
          chatPanel.style.display = 'flex';
          if (chatBackButton) chatBackButton.classList.remove('d-none');
        } else {
          // Show conversation panel and hide chat panel on mobile when not in a conversation
          conversationPanel.classList.remove('hidden');
          if (chatBackButton) chatBackButton.classList.add('d-none');
        }
      } else {
        // Reset for desktop view
        if (conversationPanel) conversationPanel.classList.remove('hidden');
        if (chatPanel) chatPanel.style.display = 'flex';
        if (chatBackButton) chatBackButton.classList.add('d-none');
      }
    }

    // Call on load and on resize
    handleMobileView();
    window.addEventListener('resize', handleMobileView);

    // Attachment button
    const attachmentBtn = document.getElementById('attachment-btn');
    const attachmentInput = document.getElementById('attachment-input');
    const attachmentPreview = document.getElementById('attachment-preview');
    const attachmentImage = document.getElementById('attachment-image');
    const removeAttachmentBtn = document.getElementById('remove-attachment-btn');

    if (attachmentBtn && attachmentInput) {
      attachmentBtn.addEventListener('click', function() {
        attachmentInput.click();
      });

      attachmentInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
          const reader = new FileReader();

          reader.onload = function(e) {
            attachmentImage.src = e.target.result;
            attachmentPreview.classList.remove('d-none');
          };

          reader.readAsDataURL(this.files[0]);
        }
      });

      if (removeAttachmentBtn) {
        removeAttachmentBtn.addEventListener('click', function() {
          attachmentInput.value = '';
          attachmentPreview.classList.add('d-none');
        });
      }
    }
  });

  // Function to load contacts
  function loadContacts() {
    const contactsList = document.getElementById('contacts-list');

    // This would be replaced with an actual API call
    fetch('/network/api/connections')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.connections && data.connections.length > 0) {
          let html = '';

          data.connections.forEach(connection => {
            html += `
              <div class="contact-item">
                <a href="/messaging/${connection.otherUser.uid}" class="text-decoration-none">
                  ${connection.otherUser.photoURL
                    ? `<img src="${connection.otherUser.photoURL}" class="contact-avatar">`
                    : `<div class="contact-avatar d-flex align-items-center justify-content-center bg-light">
                        <i class="bi bi-person-fill text-secondary"></i>
                      </div>`
                  }
                  <div class="contact-info">
                    <div class="contact-name">${connection.otherUser.displayName || 'User'}</div>
                    <div class="contact-status">${connection.otherUser.headline || 'Sustainable Farmer'}</div>
                  </div>
                </a>
              </div>
            `;
          });

          contactsList.innerHTML = html;
        } else {
          contactsList.innerHTML = `
            <div class="text-center py-3">
              <p class="text-muted">No connections found. <a href="/network/search">Find farmers</a> to connect with.</p>
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('Error loading contacts:', error);
        contactsList.innerHTML = `
          <div class="text-center py-3">
            <p class="text-danger">Error loading contacts. Please try again.</p>
          </div>
        `;
      });
  }

  // Function to send a message
  function sendMessage() {
    const messageInput = document.getElementById('message-input');
    const text = messageInput.value.trim();

    if (!text) return;

    const conversationId = '<%= activeConversation ? activeConversation.id : "" %>';

    if (!conversationId) return;

    // Clear the input
    messageInput.value = '';

    // Add a temporary message to the UI
    addTemporaryMessage(text);

    // Send the message to the server
    fetch(`/messaging/api/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ text })
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        alert('Error sending message: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error sending message:', error);
      alert('An error occurred while sending your message. Please try again.');
    });
  }

  // Function to add a temporary message to the UI
  function addTemporaryMessage(text) {
    const messagesContainer = document.getElementById('messages-container');

    if (!messagesContainer) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message outgoing';

    const messageBubble = document.createElement('div');
    messageBubble.className = 'message-bubble';

    const messageText = document.createElement('div');
    messageText.className = 'message-text';
    messageText.textContent = text;

    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';

    const timeText = document.createTextNode(
      new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
    );

    const statusIcon = document.createElement('span');
    statusIcon.className = 'message-status sent';
    statusIcon.innerHTML = '<i class="bi bi-check"></i>';

    messageTime.appendChild(timeText);
    messageTime.appendChild(statusIcon);

    messageBubble.appendChild(messageText);
    messageDiv.appendChild(messageBubble);
    messageDiv.appendChild(messageTime);
    messagesContainer.appendChild(messageDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }
</script>

<!-- Include WhatsApp-style enhancements -->
<script src="/js/facebook-messenger.js"></script>

<%- include('../partials/footer') %>
