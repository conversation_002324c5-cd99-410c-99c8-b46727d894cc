/* Enhanced Design System for Sustainable Farming App */

:root {
  /* Enhanced Color Palette */
  --primary-50: #f0f9f0;
  --primary-100: #dcf2dc;
  --primary-200: #bae5ba;
  --primary-300: #8dd48d;
  --primary-400: #5cb85c;
  --primary-500: #4CAF50;
  --primary-600: #43a047;
  --primary-700: #388e3c;
  --primary-800: #2e7d32;
  --primary-900: #1b5e20;

  /* Semantic Colors */
  --success: #4CAF50;
  --warning: #FF9800;
  --error: #F44336;
  --info: #2196F3;

  /* Neutral Colors */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Enhanced Typography */
body {
  font-family: var(--font-family-primary);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.3;
  color: var(--gray-900);
}

/* Enhanced Button System */
.btn-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  text-decoration: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-enhanced:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-enhanced:hover:before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
  text-decoration: none;
}

.btn-secondary {
  background: white;
  color: var(--primary-600);
  border-color: var(--primary-200);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  color: var(--primary-700);
  text-decoration: none;
}

/* Enhanced Card System */
.card-enhanced {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-enhanced-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50), white);
}

.card-enhanced-body {
  padding: var(--space-6);
}

.card-enhanced-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* Enhanced Form Controls */
.form-enhanced {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group-enhanced {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label-enhanced {
  font-weight: 600;
  color: var(--gray-700);
  font-size: var(--text-sm);
}

.form-input-enhanced {
  padding: var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: all var(--transition-normal);
  background: white;
}

.form-input-enhanced:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-input-enhanced:invalid {
  border-color: var(--error);
}

/* Enhanced Navigation */
.nav-enhanced {
  background: white;
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-enhanced-brand {
  font-family: var(--font-family-heading);
  font-weight: 700;
  font-size: var(--text-xl);
  color: var(--primary-600);
  text-decoration: none;
}

.nav-enhanced-link {
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.nav-enhanced-link:hover {
  background: var(--primary-50);
  color: var(--primary-600);
  text-decoration: none;
}

.nav-enhanced-link.active {
  background: var(--primary-100);
  color: var(--primary-700);
}

/* Enhanced Alerts */
.alert-enhanced {
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid;
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.alert-enhanced-success {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-800);
}

.alert-enhanced-warning {
  background: #fff3e0;
  border-color: #ffcc02;
  color: #e65100;
}

.alert-enhanced-error {
  background: #ffebee;
  border-color: #ffcdd2;
  color: #c62828;
}

.alert-enhanced-info {
  background: #e3f2fd;
  border-color: #bbdefb;
  color: #1565c0;
}

/* Enhanced Loading States */
.loading-enhanced {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.spinner-enhanced {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Badges */
.badge-enhanced {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-enhanced-primary {
  background: var(--primary-100);
  color: var(--primary-800);
}

.badge-enhanced-success {
  background: var(--primary-100);
  color: var(--primary-800);
}

.badge-enhanced-warning {
  background: #fff3e0;
  color: #e65100;
}

.badge-enhanced-error {
  background: #ffebee;
  color: #c62828;
}

/* Enhanced Tooltips */
.tooltip-enhanced {
  position: relative;
  cursor: help;
}

.tooltip-enhanced:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-900);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: var(--space-1);
}

/* Enhanced Progress Bars */
.progress-enhanced {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-enhanced-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.progress-enhanced-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .btn-enhanced {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-xs);
  }

  .card-enhanced-header,
  .card-enhanced-body,
  .card-enhanced-footer {
    padding: var(--space-4);
  }

  .form-input-enhanced {
    padding: var(--space-3);
  }
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Animation Classes */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.4s ease-out;
}

/* Enhanced Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.btn-enhanced:focus,
.form-input-enhanced:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
}

/* Enhanced Hover Effects */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

/* Enhanced Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Micro-interactions */
.btn-enhanced {
  position: relative;
  overflow: hidden;
}

.btn-enhanced::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-enhanced:active::after {
  width: 300px;
  height: 300px;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-500);
}
