/**
 * LinkedIn-style Network Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
  // Load pending connection requests
  loadPendingRequests();

  // Load suggested connections
  loadSuggestedConnections();

  // Set up event listeners for the post creation
  setupPostCreation();

  // Set up event delegation for connection actions
  setupConnectionActions();
});

/**
 * Load pending connection requests
 */
function loadPendingRequests() {
  fetch('/network/api/connections/pending')
    .then(response => response.json())
    .then(data => {
      const container = document.getElementById('pending-requests-container');

      // Clear loading spinner
      container.innerHTML = '';

      if (data.success && data.requests && data.requests.length > 0) {
        // Get the template
        const template = document.getElementById('connection-request-template');

        // Filter for incoming requests (where the user is not the requester)
        const incomingRequests = data.requests.filter(request => !request.isRequester);

        if (incomingRequests.length > 0) {
          // Update the invitation counter
          updateInvitationCounter();

          incomingRequests.forEach(request => {
            // Clone the template
            const requestElement = template.content.cloneNode(true);

            // Set connection ID
            requestElement.querySelector('.connection-card').dataset.connectionId = request.id;

            // Set profile photo
            const photoElement = requestElement.querySelector('.connection-photo');
            if (request.otherUser.photoURL) {
              photoElement.src = request.otherUser.photoURL;
            } else {
              // Replace img with a div containing an icon for users without photos
              const parentElement = photoElement.parentElement;
              const placeholderDiv = document.createElement('div');
              placeholderDiv.className = 'connection-photo d-flex align-items-center justify-content-center';
              placeholderDiv.innerHTML = '<i class="bi bi-person-fill text-secondary"></i>';
              parentElement.replaceChild(placeholderDiv, photoElement);
            }

            // Set name and headline
            requestElement.querySelector('.connection-name').textContent = request.otherUser.displayName || 'User';
            requestElement.querySelector('.connection-headline').textContent = request.otherUser.headline || 'Sustainable Farmer';

            // Set location if available
            const locationElement = requestElement.querySelector('.connection-location');
            if (request.otherUser.location) {
              locationElement.innerHTML = `<i class="bi bi-geo-alt-fill me-1"></i> ${request.otherUser.location}`;
            } else {
              locationElement.remove();
            }

            // Set button data attributes
            requestElement.querySelector('.accept-request-btn').dataset.id = request.id;
            requestElement.querySelector('.reject-request-btn').dataset.id = request.id;

            // Append to container
            container.appendChild(requestElement);
          });
        } else {
          // No incoming requests
          container.innerHTML = `
            <div class="p-5 text-center">
              <i class="bi bi-envelope-open text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Pending Invitations</h5>
              <p class="text-muted">You don't have any pending connection invitations at this time.</p>
              <a href="/network/search" class="linkedin-btn linkedin-btn-primary mt-2">
                <i class="bi bi-search me-1"></i> Find Farmers to Connect With
              </a>
            </div>
          `;
        }
      } else {
        // No requests or error
        container.innerHTML = `
          <div class="p-5 text-center">
            <i class="bi bi-envelope-open text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3">No Pending Invitations</h5>
            <p class="text-muted">You don't have any pending connection invitations at this time.</p>
            <a href="/network/search" class="linkedin-btn linkedin-btn-primary mt-2">
              <i class="bi bi-search me-1"></i> Find Farmers to Connect With
            </a>
          </div>
        `;
      }

      // Show/hide the section based on whether there are requests
      const requestsSection = document.getElementById('connection-requests-section');
      if (container.querySelector('.connection-card') === null) {
        // Don't hide the section, just show the empty state
        requestsSection.style.display = 'block';
      } else {
        requestsSection.style.display = 'block';
      }
    })
    .catch(error => {
      console.error('Error loading pending requests:', error);
      document.getElementById('pending-requests-container').innerHTML = `
        <div class="p-5 text-center">
          <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
          <h5 class="mt-3">Oops! Something went wrong</h5>
          <p class="text-muted">We couldn't load your invitations at this time. Please try again later.</p>
          <button class="linkedin-btn linkedin-btn-primary mt-2" onclick="loadPendingRequests()">
            <i class="bi bi-arrow-clockwise me-1"></i> Try Again
          </button>
        </div>
      `;
    });
}

/**
 * Load suggested connections (people you may know)
 */
function loadSuggestedConnections() {
  fetch('/network/api/connections/suggestions')
    .then(response => response.json())
    .then(data => {
      const container = document.getElementById('suggested-connections-container');

      // Clear loading spinner
      container.innerHTML = '';

      if (data.success && data.suggestions && data.suggestions.length > 0) {
        // Get the template
        const template = document.getElementById('suggested-connection-template');

        // Display up to 9 suggestions (increased from 6)
        const suggestionsToShow = data.suggestions.slice(0, 9);

        suggestionsToShow.forEach(farmer => {
          // Clone the template
          const suggestionElement = template.content.cloneNode(true);

          // Set profile photo
          const photoElement = suggestionElement.querySelector('.connection-photo');
          if (farmer.photoURL) {
            photoElement.src = farmer.photoURL;
          } else {
            // Replace img with a div containing an icon for users without photos
            const parentElement = photoElement.parentElement.parentElement;
            const photoContainer = photoElement.parentElement;
            const placeholderDiv = document.createElement('div');
            placeholderDiv.className = 'connection-photo d-flex align-items-center justify-content-center bg-light';
            placeholderDiv.style.width = '80px';
            placeholderDiv.style.height = '80px';
            placeholderDiv.style.borderRadius = '50%';
            placeholderDiv.style.border = '3px solid var(--network-primary-light)';
            placeholderDiv.style.padding = '2px';
            placeholderDiv.innerHTML = '<i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>';
            photoContainer.replaceChild(placeholderDiv, photoElement);
          }

          // Set name and headline
          suggestionElement.querySelector('.connection-name').textContent = farmer.displayName || 'User';
          suggestionElement.querySelector('.connection-headline').textContent = farmer.headline || 'Sustainable Farmer';

          // Set location if available
          const locationElement = suggestionElement.querySelector('.connection-location');
          if (farmer.location) {
            locationElement.innerHTML = `<i class="bi bi-geo-alt-fill me-1"></i> ${farmer.location}`;
          } else {
            locationElement.remove();
          }

          // Set online indicator (randomly for demo purposes)
          const onlineIndicator = suggestionElement.querySelector('.online-indicator');
          if (Math.random() > 0.5) {
            onlineIndicator.style.backgroundColor = 'var(--network-primary)';
          } else {
            onlineIndicator.remove();
          }

          // Set button data attribute
          suggestionElement.querySelector('.connect-btn').dataset.id = farmer.uid;

          // Set view profile link
          const viewProfileBtn = suggestionElement.querySelector('.view-profile-btn');
          viewProfileBtn.href = `/network/profile/${farmer.uid}`;

          // Append to container
          container.appendChild(suggestionElement);
        });
      } else {
        // No suggestions or error
        container.innerHTML = `
          <div class="col-12 p-5 text-center">
            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3">No Suggestions Available</h5>
            <p class="text-muted">We couldn't find any farmers to suggest at this time.</p>
            <a href="/network/search" class="linkedin-btn linkedin-btn-primary mt-2">
              <i class="bi bi-search me-1"></i> Find Farmers
            </a>
          </div>
        `;
      }
    })
    .catch(error => {
      console.error('Error loading suggested connections:', error);
      document.getElementById('suggested-connections-container').innerHTML = `
        <div class="col-12 p-5 text-center">
          <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
          <h5 class="mt-3">Oops! Something went wrong</h5>
          <p class="text-muted">We couldn't load suggestions at this time. Please try again later.</p>
          <button class="linkedin-btn linkedin-btn-primary mt-2" onclick="loadSuggestedConnections()">
            <i class="bi bi-arrow-clockwise me-1"></i> Try Again
          </button>
        </div>
      `;
    });
}

/**
 * Set up event listeners for connection actions using event delegation
 */
function setupConnectionActions() {
  // Event delegation for accept/reject buttons
  document.addEventListener('click', function(event) {
    // Accept connection request
    if (event.target.classList.contains('accept-request-btn')) {
      const connectionId = event.target.dataset.id;
      acceptConnectionRequest(connectionId, event.target);
    }

    // Reject connection request
    if (event.target.classList.contains('reject-request-btn')) {
      const connectionId = event.target.dataset.id;
      rejectConnectionRequest(connectionId, event.target);
    }

    // Send connection request - handle both the button and its child elements
    if (event.target.classList.contains('connect-btn') ||
        (event.target.parentElement && event.target.parentElement.classList.contains('connect-btn'))) {

      // Get the actual button element
      const button = event.target.classList.contains('connect-btn') ?
                    event.target :
                    event.target.parentElement;

      const recipientId = button.dataset.id;
      console.log('Connect button clicked for recipient:', recipientId);

      if (recipientId) {
        sendConnectionRequest(recipientId, button);
      } else {
        console.error('No recipient ID found on connect button');
      }
    }
  });
}

/**
 * Accept a connection request
 */
function acceptConnectionRequest(connectionId, buttonElement) {
  // Disable buttons to prevent multiple clicks
  const parentCard = buttonElement.closest('.connection-card');
  const buttons = parentCard.querySelectorAll('button');
  buttons.forEach(btn => btn.disabled = true);

  // Show loading state
  buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Accepting...';

  // Get user name if available
  let userName = 'this user';
  const nameElement = parentCard.querySelector('.connection-name');
  if (nameElement) {
    userName = nameElement.textContent;
  }

  fetch('/network/api/connections/accept', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ connectionId })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Update the UI
      parentCard.innerHTML = `
        <div class="p-3 text-center text-success">
          <i class="bi bi-check-circle-fill me-2"></i>
          Connection accepted!
        </div>
      `;

      // Show success toast notification
      toast.success(
        `You are now connected with ${userName}. You can now message each other and view each other's content.`,
        'Connection Accepted',
        { duration: 5000 }
      );

      // Fade out and remove after a delay
      setTimeout(() => {
        parentCard.style.opacity = '0';
        parentCard.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          parentCard.remove();

          // Check if there are any more requests
          const container = document.getElementById('pending-requests-container');
          if (container.querySelector('.connection-card') === null) {
            // Update the empty state instead of hiding
            container.innerHTML = `
              <div class="p-5 text-center">
                <i class="bi bi-envelope-open text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3">No Pending Invitations</h5>
                <p class="text-muted">You don't have any pending connection invitations at this time.</p>
                <a href="/network/search" class="linkedin-btn linkedin-btn-primary mt-2">
                  <i class="bi bi-search me-1"></i> Find Farmers to Connect With
                </a>
              </div>
            `;
          }

          // Update connection count if available
          updateConnectionCount(1);

          // Update the title to remove the counter
          updateInvitationCounter();
        }, 500);
      }, 1500);
    } else {
      // Re-enable buttons
      buttons.forEach(btn => btn.disabled = false);

      // Reset button text
      buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> Accept';

      // Show error toast
      toast.error(
        data.message || 'Failed to accept connection. Please try again.',
        'Error',
        { duration: 5000 }
      );
    }
  })
  .catch(error => {
    console.error('Error accepting connection request:', error);

    // Re-enable buttons
    buttons.forEach(btn => btn.disabled = false);

    // Reset button text
    buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> Accept';

    // Show error toast
    toast.error(
      'An error occurred while accepting the connection. Please try again.',
      'Error',
      { duration: 5000 }
    );
  });
}

/**
 * Reject a connection request
 */
function rejectConnectionRequest(connectionId, buttonElement) {
  // Disable buttons to prevent multiple clicks
  const parentCard = buttonElement.closest('.connection-card');
  const buttons = parentCard.querySelectorAll('button');
  buttons.forEach(btn => btn.disabled = true);

  // Show loading state
  buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Ignoring...';

  // Get user name if available
  let userName = 'this user';
  const nameElement = parentCard.querySelector('.connection-name');
  if (nameElement) {
    userName = nameElement.textContent;
  }

  fetch('/network/api/connections/reject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ connectionId })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Show info toast notification
      toast.info(
        `You've ignored the invitation from ${userName}.`,
        'Invitation Ignored',
        { duration: 3000 }
      );

      // Fade out and remove
      parentCard.style.opacity = '0';
      parentCard.style.transition = 'opacity 0.5s';
      setTimeout(() => {
        parentCard.remove();

        // Check if there are any more requests
        const container = document.getElementById('pending-requests-container');
        if (container.querySelector('.connection-card') === null) {
          // Update the empty state instead of hiding
          container.innerHTML = `
            <div class="p-5 text-center">
              <i class="bi bi-envelope-open text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Pending Invitations</h5>
              <p class="text-muted">You don't have any pending connection invitations at this time.</p>
              <a href="/network/search" class="linkedin-btn linkedin-btn-primary mt-2">
                <i class="bi bi-search me-1"></i> Find Farmers to Connect With
              </a>
            </div>
          `;
        }

        // Update the title to remove the counter
        updateInvitationCounter();
      }, 500);
    } else {
      // Re-enable buttons
      buttons.forEach(btn => btn.disabled = false);

      // Reset button text
      buttonElement.innerHTML = '<i class="bi bi-x-lg me-1"></i> Ignore';

      // Show error toast
      toast.error(
        data.message || 'Failed to ignore invitation. Please try again.',
        'Error',
        { duration: 5000 }
      );
    }
  })
  .catch(error => {
    console.error('Error rejecting connection request:', error);

    // Re-enable buttons
    buttons.forEach(btn => btn.disabled = false);

    // Reset button text
    buttonElement.innerHTML = '<i class="bi bi-x-lg me-1"></i> Ignore';

    // Show error toast
    toast.error(
      'An error occurred while ignoring the invitation. Please try again.',
      'Error',
      { duration: 5000 }
    );
  });
}

/**
 * Send a connection request
 */
function sendConnectionRequest(recipientId, buttonElement) {
  // Disable button to prevent multiple clicks
  buttonElement.disabled = true;

  // Show loading state
  const originalButtonText = buttonElement.innerHTML;
  buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Sending...';

  // Get the card element
  const card = buttonElement.closest('.linkedin-card') || buttonElement.closest('.connection-card');

  // Get user name if available
  let userName = 'this user';
  if (card) {
    const nameElement = card.querySelector('.connection-name');
    if (nameElement) {
      userName = nameElement.textContent;
    }
  }

  // Log the request for debugging
  console.log('Sending connection request to:', recipientId);

  fetch('/network/api/connections/request', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify({ recipientId: recipientId })
  })
  .then(response => {
    console.log('Response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Response data:', data);
    if (data.success) {
      // Update the button
      buttonElement.innerHTML = '<i class="bi bi-check2 me-1"></i> Invitation sent';
      buttonElement.classList.remove('linkedin-btn-outline');
      buttonElement.classList.add('linkedin-btn-text');
      buttonElement.disabled = true;

      // Add a "pending" badge to the card if it exists
      if (card) {
        const badgeContainer = document.createElement('div');
        badgeContainer.className = 'position-absolute top-0 end-0 m-2';
        badgeContainer.innerHTML = `
          <span class="badge rounded-pill" style="background-color: var(--network-primary);">
            <i class="bi bi-clock me-1"></i> Pending
          </span>
        `;
        card.style.position = 'relative';
        card.appendChild(badgeContainer);
      }

      // Show success toast notification
      toast.success(
        `Your invitation has been sent to ${userName}. You'll be notified when they respond.`,
        'Invitation Sent',
        { duration: 5000 }
      );
    } else {
      // Reset button
      buttonElement.innerHTML = originalButtonText;
      buttonElement.disabled = false;

      // Show error toast
      toast.error(
        data.message || 'Failed to send invitation. Please try again.',
        'Error',
        { duration: 5000 }
      );
    }
  })
  .catch(error => {
    console.error('Error sending connection request:', error);

    // Reset button
    buttonElement.innerHTML = originalButtonText;
    buttonElement.disabled = false;

    // Show error toast
    toast.error(
      'An error occurred while sending the invitation. Please try again.',
      'Error',
      { duration: 5000 }
    );
  });
}

/**
 * Update the connection count in the sidebar
 */
function updateConnectionCount(increment = 0) {
  const countElement = document.getElementById('connections-count');
  if (countElement) {
    let currentCount = parseInt(countElement.textContent.trim()) || 0;
    countElement.textContent = currentCount + increment;
  }
}

/**
 * Update the invitation counter in the title
 */
function updateInvitationCounter() {
  const container = document.getElementById('pending-requests-container');
  const titleElement = document.querySelector('#connection-requests-section .linkedin-card-title');

  if (titleElement) {
    const invitationCards = container.querySelectorAll('.connection-card');
    const count = invitationCards.length;

    if (count > 0) {
      titleElement.innerHTML = `
        <i class="bi bi-envelope me-2" style="color: var(--network-primary);"></i>
        Network Invitations
        <span class="badge rounded-pill" style="background-color: var(--network-primary); font-size: 0.7rem; vertical-align: middle; margin-left: 5px;">
          ${count}
        </span>
      `;
    } else {
      titleElement.innerHTML = `
        <i class="bi bi-envelope me-2" style="color: var(--network-primary);"></i>
        Network Invitations
      `;
    }
  }
}

/**
 * Set up event listeners for post creation
 */
function setupPostCreation() {
  // Image upload button
  const uploadImageBtn = document.getElementById('upload-image-btn');
  const postImageInput = document.getElementById('post-image');
  const imagePreviewContainer = document.getElementById('image-preview-container');
  const imagePreview = document.getElementById('image-preview');
  const removeImageBtn = document.getElementById('remove-image-btn');

  if (uploadImageBtn && postImageInput) {
    uploadImageBtn.addEventListener('click', function() {
      postImageInput.click();
    });

    postImageInput.addEventListener('change', function() {
      if (this.files && this.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
          imagePreview.src = e.target.result;
          imagePreviewContainer.classList.remove('d-none');
        };
        reader.readAsDataURL(this.files[0]);
      }
    });

    if (removeImageBtn) {
      removeImageBtn.addEventListener('click', function() {
        postImageInput.value = '';
        imagePreviewContainer.classList.add('d-none');
      });
    }
  }

  // Create post button
  const createPostBtn = document.getElementById('create-post-btn');
  const postTextArea = document.getElementById('post-text');

  if (createPostBtn && postTextArea) {
    createPostBtn.addEventListener('click', function() {
      // Handle post creation (using existing network.js functionality)
      // This would typically call a function from the original network.js file
      if (typeof createPost === 'function') {
        createPost();
      } else {
        console.warn('createPost function not found. Make sure network.js is loaded.');
      }
    });
  }
}
