/* Admin Dashboard Styles */
:root {
  --admin-primary: var(--theme-primary, #4CAF50);
  --admin-primary-dark: var(--theme-primary-dark, #388E3C);
  --admin-primary-light: var(--theme-primary-light, #8BC34A);
  --admin-text-light: var(--theme-text-light, white);
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

@media (max-width: 767.98px) {
  .sidebar {
    top: 5rem;
  }
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  padding: 0.5rem 1rem;
  margin-bottom: 0.2rem;
  border-radius: 0.25rem;
}

.sidebar .nav-link:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
  color: var(--admin-primary);
  background-color: rgba(76, 175, 80, 0.1);
}

.sidebar .nav-link i {
  margin-right: 4px;
  color: #999;
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
  color: inherit;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}

/* Main content */
main {
  padding-top: 1.5rem;
}

/* Admin cards */
.admin-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.admin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.admin-card .card-header {
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.admin-card .card-footer {
  background-color: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Stats cards */
.stats-card {
  border-left: 4px solid var(--admin-primary);
}

.stats-card .stats-icon {
  font-size: 2rem;
  color: var(--admin-primary);
}

/* Tables */
.admin-table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.admin-table td {
  vertical-align: middle;
}

/* Forms */
.admin-form label {
  font-weight: 500;
}

.admin-form .form-control:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

/* Buttons */
.btn-admin-primary {
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
  color: var(--admin-text-light);
}

.btn-admin-primary:hover {
  background-color: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  color: var(--admin-text-light);
}

/* Utilities */
.admin-badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
}

/* Resource items */
.resource-item {
  transition: all 0.2s ease;
}

.resource-item:hover {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Module items */
.module-item {
  transition: background-color 0.2s ease;
}

.module-item:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.module-item .handle {
  cursor: grab;
}

.module-item .handle:active {
  cursor: grabbing;
}

/* CKEditor customization */
.ck-editor__editable {
  min-height: 300px;
}

/* Image preview */
#imagePreview img {
  max-height: 200px;
  object-fit: contain;
}
