<!-- Facebook-style Header -->
<%
// Set default value for currentPath if it's not defined
const path = typeof currentPath !== 'undefined' ? currentPath : '';
%>
<header class="fb-header">
  <!-- Left side elements -->
  <div class="fb-header-left">
    <!-- Logo -->
    <div class="fb-header-logo">
      <i class="bi bi-flower3 me-2" style="font-size: 24px; color: #F5DEB3;"></i>
      <h5 class="text-white mb-0">Sustainable Farming</h5>
    </div>

    <!-- Search Bar (Now on the left) -->
    <div class="fb-search">
      <i class="bi bi-search"></i>
      <input type="text" placeholder="Search Sustainable Farming">
    </div>
  </div>

  <!-- Center Navigation Links -->
  <div class="fb-header-center">
    <a href="/dashboard" class="fb-nav-link <%= path === '/dashboard' ? 'active' : '' %>">
      <i class="bi bi-house-door-fill"></i>
      <span>Home</span>
    </a>
    <a href="/network" class="fb-nav-link <%= path.includes('/network') ? 'active' : '' %>">
      <i class="bi bi-people-fill"></i>
      <span>Network</span>
    </a>
    <a href="/messaging" class="fb-nav-link <%= path.includes('/messaging') ? 'active' : '' %>">
      <i class="bi bi-chat-dots-fill"></i>
      <span>Messages</span>
    </a>
    <a href="/courses" class="fb-nav-link <%= path.includes('/courses') ? 'active' : '' %>">
      <i class="bi bi-book-fill"></i>
      <span>Courses</span>
    </a>
    <a href="/transport" class="fb-nav-link <%= path.includes('/transport') ? 'active' : '' %>">
      <i class="bi bi-truck"></i>
      <span>Transport</span>
    </a>
    <a href="/market-trends" class="fb-nav-link <%= path.includes('/market-trends') ? 'active' : '' %>">
      <i class="bi bi-graph-up-arrow"></i>
      <span>Market Trends</span>
    </a>
  </div>

  <!-- Right side elements -->
  <div class="fb-header-right">
    <!-- Weather Widget -->
    <div class="fb-weather-widget" id="weather-widget">
      <%- include('./weather-widget') %>
    </div>

    <!-- Navigation Icons -->
    <div class="fb-header-nav">
      <div class="fb-header-icon">
        <i class="bi bi-bell-fill"></i>
      </div>
      <% if (user && user.photoURL) { %>
        <img src="<%= user.photoURL %>" alt="<%= user.displayName %>" class="fb-user-avatar">
      <% } else { %>
        <div class="fb-header-icon">
          <i class="bi bi-person-fill"></i>
        </div>
      <% } %>
    </div>
  </div>
</header>
