<!-- Header with title -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1>Messages</h1>
      <% if (user && user.displayName) { %>
        <div class="greeting-container" style="text-align: right;">
          <div id="greeting-text">
            <span class="time-greeting">Good day</span><span class="me-2">,</span>
            <span class="fw-bold"><%= user.displayName %></span>
          </div>
        </div>
      <% } %>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
  </div>
</div>

<!-- Main content with left navigation and messages -->
<div class="row">
  <!-- Left Side Navigation -->
  <div class="col-md-3 mb-4">
    <div class="dashboard-nav">
      <ul class="nav flex-column">
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/dashboard">
            <i class="bi bi-house-door me-2"></i> Dashboard
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses">
            <i class="bi bi-book me-2"></i> Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/resources">
            <i class="bi bi-file-earmark-text me-2"></i> Resources
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses/my-courses">
            <i class="bi bi-journal-check me-2"></i> My Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/profile">
            <i class="bi bi-person-circle me-2"></i> Profile
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/new">
            <i class="bi bi-cloud-upload me-2"></i> Upload Content
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/my-uploads">
            <i class="bi bi-collection me-2"></i> My Uploads
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/weather">
            <i class="bi bi-cloud-sun me-2"></i> Weather
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/connections">
            <i class="bi bi-people-fill me-2"></i> Farmer Network
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link active" href="/messages">
            <i class="bi bi-chat-dots-fill me-2"></i> Messages
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/auth/logout">
            <i class="bi bi-box-arrow-right me-2"></i> Logout
          </a>
        </li>
      </ul>
    </div>
  </div>

    <!-- Main content -->
    <div class="col-md-9">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">Messages</h4>
          <a href="/connections" class="btn btn-light btn-sm">
            <i class="bi bi-people-fill me-1"></i> My Connections
          </a>
        </div>
        <div class="card-body p-0">
          <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger m-3" role="alert">
              <%= error %>
            </div>
          <% } %>

          <div class="row g-0">
            <!-- Conversations list -->
            <div class="col-md-4 border-end">
              <div class="conversations-header p-3 border-bottom">
                <h5 class="mb-0">Conversations</h5>
              </div>

              <div class="conversations-list">
                <% if (!conversations || conversations.length === 0) { %>
                  <div class="text-center py-5">
                    <i class="bi bi-chat-dots text-muted" style="font-size: 2rem;"></i>
                    <p class="mt-3 text-muted">No conversations yet</p>
                    <a href="/connections" class="btn btn-success btn-sm mt-2">
                      <i class="bi bi-people-fill me-1"></i> Find Connections
                    </a>
                  </div>
                <% } else { %>
                  <% conversations.forEach(conversation => {
                    const isActive = activeConversation &&
                                    conversation.otherUser.uid === activeConversation.otherUser.uid;
                  %>
                    <a href="/messages/conversation/<%= conversation.otherUser.uid %>"
                       class="conversation-item d-flex align-items-center p-3 border-bottom text-decoration-none <%= isActive ? 'bg-light' : 'text-dark' %>">
                      <div class="me-3 position-relative">
                        <% if (conversation.otherUser.photoURL) { %>
                          <img src="<%= conversation.otherUser.photoURL %>" alt="<%= conversation.otherUser.displayName %>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                        <% } else { %>
                          <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="bi bi-person-fill text-secondary" style="font-size: 1.5rem;"></i>
                          </div>
                        <% } %>

                        <!-- Online indicator (placeholder) -->
                        <span class="position-absolute bottom-0 end-0 bg-success rounded-circle" style="width: 12px; height: 12px; border: 2px solid white;"></span>
                      </div>
                      <div class="flex-grow-1 min-width-0">
                        <div class="d-flex justify-content-between align-items-center">
                          <h6 class="mb-0 text-truncate"><%= conversation.otherUser.displayName %></h6>
                          <small class="text-muted ms-2">
                            <%= conversation.lastMessageAt ? new Date(conversation.lastMessageAt.seconds * 1000).toLocaleDateString() : 'New' %>
                          </small>
                        </div>
                        <p class="mb-0 small text-truncate text-muted">
                          <% if (conversation.lastMessage && conversation.lastMessage.text) { %>
                            <% if (conversation.lastMessage.senderId === user.uid) { %>
                              <span class="text-muted">You: </span>
                            <% } %>
                            <%= conversation.lastMessage.text %>
                          <% } else { %>
                            Start a conversation
                          <% } %>
                        </p>
                      </div>
                    </a>
                  <% }); %>
                <% } %>
              </div>
            </div>

            <!-- Message content area -->
            <div class="col-md-8">
              <% if (activeConversation) { %>
                <!-- Conversation header -->
                <div class="conversation-header p-3 border-bottom d-flex align-items-center">
                  <div class="me-3 position-relative">
                    <% if (activeConversation.otherUser.photoURL) { %>
                      <img src="<%= activeConversation.otherUser.photoURL %>" alt="<%= activeConversation.otherUser.displayName %>" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                    <% } else { %>
                      <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="bi bi-person-fill text-secondary" style="font-size: 1.2rem;"></i>
                      </div>
                    <% } %>

                    <!-- Online indicator (placeholder) -->
                    <span class="position-absolute bottom-0 end-0 bg-success rounded-circle" style="width: 10px; height: 10px; border: 2px solid white;"></span>
                  </div>
                  <div>
                    <h5 class="mb-0"><%= activeConversation.otherUser.displayName %></h5>
                    <small class="text-muted">
                      <% if (activeConversation.otherUser.farmName) { %>
                        <%= activeConversation.otherUser.farmName %>
                      <% } %>
                      <% if (activeConversation.otherUser.location) { %>
                        <% if (activeConversation.otherUser.farmName) { %> • <% } %>
                        <%= activeConversation.otherUser.location %>
                      <% } %>
                    </small>
                  </div>
                  <div class="ms-auto">
                    <a href="/connections/user/<%= activeConversation.otherUser.uid %>" class="btn btn-outline-secondary btn-sm">
                      <i class="bi bi-person-fill"></i>
                    </a>
                  </div>
                </div>

                <!-- Messages container -->
                <div class="messages-container p-3" id="messagesContainer" style="height: 400px; overflow-y: auto;">
                  <div id="encryptionNotice" class="text-center mb-3">
                    <span class="badge bg-success">
                      <i class="bi bi-shield-lock-fill me-1"></i> End-to-End Encrypted
                    </span>
                    <p class="small text-muted mt-1">Messages are encrypted and can only be read by you and <%= activeConversation.otherUser.displayName %></p>
                  </div>

                  <div id="messagesContent">
                    <% if (!activeConversation.messages || activeConversation.messages.length === 0) { %>
                      <div class="text-center py-4" id="noMessagesPlaceholder">
                        <i class="bi bi-chat-text text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted">No messages yet</p>
                        <p class="small text-muted">Send a message to start the conversation</p>
                      </div>
                    <% } else { %>
                      <% activeConversation.messages.forEach(message => {
                        const isOwnMessage = message.senderId === user.uid;
                      %>
                        <div class="message-wrapper d-flex mb-3 <%= isOwnMessage ? 'justify-content-end' : 'justify-content-start' %>">
                          <div class="message <%= isOwnMessage ? 'message-own' : 'message-other' %> p-2 px-3 rounded"
                               style="max-width: 75%; background-color: <%= isOwnMessage ? '#dcf8c6' : '#f1f0f0' %>;">
                            <div class="message-content">
                              <% if (message.encrypted) { %>
                                <div class="encrypted-message" data-message-id="<%= message.id %>" data-encrypted-text="<%= message.text %>">
                                  <div class="placeholder-text">
                                    <i class="bi bi-shield-lock me-1"></i> Encrypted message
                                  </div>
                                </div>
                              <% } else { %>
                                <%= message.text %>
                              <% } %>
                            </div>
                            <div class="message-meta d-flex justify-content-end align-items-center mt-1">
                              <small class="text-muted" style="font-size: 0.7rem;">
                                <%= new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                              </small>
                              <% if (isOwnMessage) { %>
                                <i class="bi bi-check2-all ms-1 text-primary" style="font-size: 0.8rem;"></i>
                              <% } %>
                            </div>
                          </div>
                        </div>
                      <% }); %>
                    <% } %>
                  </div>
                </div>

                <!-- Message input -->
                <div class="message-input p-3 border-top">
                  <form id="messageForm" class="d-flex">
                    <input type="hidden" id="conversationId" value="<%= activeConversation.id %>">
                    <input type="hidden" id="recipientId" value="<%= activeConversation.otherUser.uid %>">
                    <input type="hidden" id="recipientPublicKey" value="<%= activeConversation.recipientPublicKey %>">
                    <input type="hidden" id="privateKey" value="<%= privateKey %>">

                    <div class="input-group">
                      <input type="text" id="messageText" class="form-control" placeholder="Type a message..." required>
                      <button type="submit" class="btn btn-success">
                        <i class="bi bi-send-fill"></i>
                      </button>
                    </div>
                  </form>
                </div>
              <% } else { %>
                <div class="text-center py-5">
                  <i class="bi bi-chat-square-text text-muted" style="font-size: 4rem;"></i>
                  <h5 class="mt-3">Select a conversation</h5>
                  <p class="text-muted">Choose a conversation from the list or start a new one</p>
                  <a href="/connections" class="btn btn-success mt-2">
                    <i class="bi bi-person-plus-fill me-1"></i> Connect with Farmers
                  </a>
                </div>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    <% if (activeConversation) { %>
      const messagesContainer = document.getElementById('messagesContainer');
      const messageForm = document.getElementById('messageForm');
      const messageText = document.getElementById('messageText');
      const conversationId = document.getElementById('conversationId').value;
      const recipientId = document.getElementById('recipientId').value;
      const recipientPublicKey = document.getElementById('recipientPublicKey').value;
      const privateKey = document.getElementById('privateKey').value;
      const messagesContent = document.getElementById('messagesContent');
      const noMessagesPlaceholder = document.getElementById('noMessagesPlaceholder');

      // Scroll to bottom of messages container
      function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // Scroll to bottom initially
      scrollToBottom();

      // Function to decrypt messages
      async function decryptMessages() {
        const encryptedMessages = document.querySelectorAll('.encrypted-message');

        if (encryptedMessages.length === 0) return;

        try {
          // Import private key
          const privateKeyObj = await importPrivateKey(privateKey);

          // Decrypt each message
          for (const messageElement of encryptedMessages) {
            const encryptedText = messageElement.dataset.encryptedText;

            try {
              const decryptedText = await decryptMessage(encryptedText, privateKeyObj);
              messageElement.innerHTML = `<div>${decryptedText}</div>`;
            } catch (decryptError) {
              console.error('Error decrypting message:', decryptError);
              messageElement.innerHTML = `
                <div class="text-danger">
                  <i class="bi bi-exclamation-triangle me-1"></i> Unable to decrypt message
                </div>
              `;
            }
          }
        } catch (error) {
          console.error('Error importing private key:', error);
          alert('Error decrypting messages. Please try refreshing the page.');
        }
      }

      // Import a private key from base64 string
      async function importPrivateKey(privateKeyBase64) {
        const binaryString = atob(privateKeyBase64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        return await window.crypto.subtle.importKey(
          "pkcs8",
          bytes.buffer,
          {
            name: "RSA-OAEP",
            hash: "SHA-256",
          },
          true,
          ["decrypt"]
        );
      }

      // Decrypt a message using the user's private key
      async function decryptMessage(encryptedMessage, privateKey) {
        // Convert the base64 encrypted message to an ArrayBuffer
        const binaryString = atob(encryptedMessage);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        // Decrypt the data
        const decryptedData = await window.crypto.subtle.decrypt(
          {
            name: "RSA-OAEP"
          },
          privateKey,
          bytes.buffer
        );

        // Convert the decrypted data to a string
        const decoder = new TextDecoder();
        return decoder.decode(decryptedData);
      }

      // Decrypt messages on page load
      decryptMessages();

      // Handle message form submission
      messageForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const message = messageText.value.trim();

        if (!message) return;

        // Clear input
        messageText.value = '';

        // Add temporary message to UI
        const tempMessageId = 'temp-' + Date.now();
        const tempMessageHtml = `
          <div class="message-wrapper d-flex mb-3 justify-content-end" id="${tempMessageId}">
            <div class="message message-own p-2 px-3 rounded" style="max-width: 75%; background-color: #dcf8c6;">
              <div class="message-content">${message}</div>
              <div class="message-meta d-flex justify-content-end align-items-center mt-1">
                <small class="text-muted" style="font-size: 0.7rem;">
                  ${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                </small>
                <i class="bi bi-clock ms-1" style="font-size: 0.8rem;"></i>
              </div>
            </div>
          </div>
        `;

        // Remove no messages placeholder if it exists
        if (noMessagesPlaceholder) {
          noMessagesPlaceholder.remove();
        }

        // Add message to UI
        messagesContent.insertAdjacentHTML('beforeend', tempMessageHtml);

        // Scroll to bottom
        scrollToBottom();

        // Send message to server
        fetch(`/messages/api/conversation/${conversationId}/send`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            message: message,
            recipientPublicKey: recipientPublicKey
          }),
          credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Update the temporary message with sent status
            const tempMessage = document.getElementById(tempMessageId);
            if (tempMessage) {
              const statusIcon = tempMessage.querySelector('.message-meta i');
              statusIcon.className = 'bi bi-check ms-1';
            }
          } else {
            // Show error
            const tempMessage = document.getElementById(tempMessageId);
            if (tempMessage) {
              const statusIcon = tempMessage.querySelector('.message-meta i');
              statusIcon.className = 'bi bi-exclamation-circle ms-1 text-danger';
            }
            alert(data.error || 'Failed to send message');
          }
        })
        .catch(error => {
          console.error('Error sending message:', error);
          // Show error
          const tempMessage = document.getElementById(tempMessageId);
          if (tempMessage) {
            const statusIcon = tempMessage.querySelector('.message-meta i');
            statusIcon.className = 'bi bi-exclamation-circle ms-1 text-danger';
          }
          alert('Failed to send message. Please try again.');
        });
      });

      // Mark messages as read
      fetch(`/messages/api/conversation/${conversationId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include'
      })
      .catch(error => {
        console.error('Error marking messages as read:', error);
      });

      // Set up real-time updates (placeholder - would use WebSockets or Firebase in a real app)
      // This would be implemented with Firebase's onSnapshot listener in a real application
    <% } %>
  });
</script>

<link rel="stylesheet" href="/css/messages.css">
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize time-based greeting
    updateGreeting();
  });

  function updateGreeting() {
    const greetingElement = document.querySelector('.time-greeting');
    if (!greetingElement) return;

    const hour = new Date().getHours();
    let greeting = 'Good day';

    if (hour < 12) {
      greeting = 'Good morning';
    } else if (hour < 18) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }

    greetingElement.textContent = greeting;
  }
</script>
