<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/toast-notifications.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Function Test Page</h1>
        <div class="alert alert-info">
            <strong>Instructions:</strong> Open the browser console (F12) to see the test results.
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Buttons</h3>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="window.sendMessage ? window.sendMessage() : alert('sendMessage not found')">
                        Test sendMessage
                    </button>
                    <button class="btn btn-secondary" onclick="window.sendSuggestion ? window.sendSuggestion('test') : alert('sendSuggestion not found')">
                        Test sendSuggestion
                    </button>
                    <button class="btn btn-success" onclick="window.handleChatKeyPress ? alert('handleChatKeyPress found') : alert('handleChatKeyPress not found')">
                        Test handleChatKeyPress
                    </button>
                    <button class="btn btn-info" onclick="window.showSavedCharts ? window.showSavedCharts() : alert('showSavedCharts not found')">
                        Test showSavedCharts
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <h3>Function Status</h3>
                <div id="status"></div>
            </div>
        </div>
        
        <!-- Test input elements -->
        <div class="mt-4">
            <h3>Test Elements</h3>
            <div class="input-group mb-3">
                <input type="text" id="chat-input" class="form-control" placeholder="Test input">
                <button class="btn btn-success" id="send-button">Send</button>
            </div>
        </div>
    </div>

    <!-- Load scripts in the same order as chart-bot page -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/toast-notifications.js"></script>
    <script src="/js/chart-bot.js"></script>

    <script>
        function checkFunctionStatus() {
            const functions = [
                'sendMessage', 'sendSuggestion', 'handleChatKeyPress', 
                'showSavedCharts', 'initializeChartBot', 'showNotification'
            ];
            
            let html = '<ul class="list-group">';
            let allFound = true;
            
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                if (!exists) allFound = false;
                const status = exists ? 'success' : 'danger';
                const icon = exists ? 'check-circle' : 'x-circle';
                html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                    ${funcName}
                    <span class="badge bg-${status}"><i class="bi bi-${icon}"></i></span>
                </li>`;
            });
            
            html += '</ul>';
            
            if (allFound) {
                html = '<div class="alert alert-success">✅ All functions found!</div>' + html;
            } else {
                html = '<div class="alert alert-danger">❌ Some functions missing!</div>' + html;
            }
            
            document.getElementById('status').innerHTML = html;
            
            // Log to console
            console.log('Function Status Check:');
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                console.log(`${funcName}: ${exists ? '✅ Found' : '❌ Missing'}`);
            });
            
            return allFound;
        }
        
        // Test immediately when script loads
        console.log('=== Function Test Page Loaded ===');
        console.log('Testing function availability...');
        
        // Test multiple times with delays
        setTimeout(() => {
            console.log('=== Test 1 (immediate) ===');
            checkFunctionStatus();
        }, 100);
        
        setTimeout(() => {
            console.log('=== Test 2 (500ms delay) ===');
            checkFunctionStatus();
        }, 500);
        
        setTimeout(() => {
            console.log('=== Test 3 (1000ms delay) ===');
            checkFunctionStatus();
        }, 1000);
        
        // DOM ready test
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DOM Ready Test ===');
            setTimeout(() => {
                const allFound = checkFunctionStatus();
                if (allFound) {
                    console.log('🎉 SUCCESS: All functions are available!');
                } else {
                    console.log('❌ FAILURE: Some functions are missing!');
                }
            }, 100);
        });
    </script>
</body>
</html>
