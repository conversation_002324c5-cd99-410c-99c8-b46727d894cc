/* LinkedIn-style Network Page CSS */

:root {
  --linkedin-blue: #0a66c2;
  --linkedin-light-blue: #e7f3ff;
  --linkedin-dark-blue: #004182;
  --linkedin-black: #191919;
  --linkedin-gray: #666666;
  --linkedin-light-gray: #f3f2ef;
  --linkedin-border: #e0e0e0;
  --linkedin-success: #057642;
  --linkedin-hover: #f9fafb;
}

/* Main container styles */
.linkedin-container {
  background-color: var(--linkedin-light-gray);
  padding: 20px 0;
  min-height: calc(100vh - 56px);
}

/* Card styles */
.linkedin-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
}

.linkedin-card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--linkedin-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.linkedin-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--linkedin-black);
  margin: 0;
}

.linkedin-card-body {
  padding: 16px 20px;
}

.linkedin-card-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--linkedin-border);
  background-color: var(--linkedin-hover);
}

/* Profile card */
.profile-card {
  text-align: center;
  padding: 20px;
}

.profile-background {
  height: 60px;
  background-color: var(--linkedin-light-blue);
  margin: -20px -20px 0;
}

.profile-photo {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  border: 2px solid #fff;
  margin-top: -36px;
  object-fit: cover;
  background-color: #fff;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  margin: 16px 0 0;
}

.profile-headline {
  font-size: 14px;
  color: var(--linkedin-gray);
  margin: 4px 0 16px;
}

/* Connection cards */
.connection-card {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid var(--linkedin-border);
  transition: background-color 0.2s;
}

.connection-card:last-child {
  border-bottom: none;
}

.connection-card:hover {
  background-color: var(--linkedin-hover);
}

.connection-photo {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.connection-info {
  flex: 1;
}

.connection-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--linkedin-black);
  margin: 0 0 4px;
}

.connection-headline {
  font-size: 14px;
  color: var(--linkedin-gray);
  margin: 0 0 8px;
}

.connection-location {
  font-size: 12px;
  color: var(--linkedin-gray);
  display: flex;
  align-items: center;
}

.connection-location i {
  margin-right: 4px;
  font-size: 12px;
}

.connection-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* Button styles */
.linkedin-btn {
  border-radius: 16px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.linkedin-btn i {
  margin-right: 6px;
}

.linkedin-btn-primary {
  background-color: var(--linkedin-blue);
  color: white;
}

.linkedin-btn-primary:hover {
  background-color: var(--linkedin-dark-blue);
}

.linkedin-btn-outline {
  background-color: transparent;
  color: var(--linkedin-gray);
  border: 1px solid var(--linkedin-gray);
}

.linkedin-btn-outline:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--linkedin-black);
  color: var(--linkedin-black);
}

.linkedin-btn-text {
  background-color: transparent;
  color: var(--linkedin-gray);
  padding: 6px 8px;
}

.linkedin-btn-text:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--linkedin-black);
}

/* Tabs */
.linkedin-tabs {
  display: flex;
  border-bottom: 1px solid var(--linkedin-border);
  margin-bottom: 16px;
}

.linkedin-tab {
  padding: 16px 20px;
  font-size: 14px;
  font-weight: 600;
  color: var(--linkedin-gray);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.linkedin-tab.active {
  color: var(--linkedin-blue);
  border-bottom-color: var(--linkedin-blue);
}

.linkedin-tab:hover:not(.active) {
  color: var(--linkedin-black);
  background-color: var(--linkedin-hover);
}

/* Badge */
.linkedin-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background-color: var(--linkedin-light-gray);
  color: var(--linkedin-gray);
}

.linkedin-badge-primary {
  background-color: var(--linkedin-light-blue);
  color: var(--linkedin-blue);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .linkedin-container {
    padding: 12px 0;
  }
  
  .connection-actions {
    flex-direction: column;
  }
}
