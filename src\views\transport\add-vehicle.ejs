<%- include('../partials/header') %>

<!-- Transport CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-3">
      <!-- Sidebar -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            Transport Services
          </h5>
        </div>
        <div class="p-3">
          <div class="d-grid gap-2">
            <a href="/transport" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-house-door-fill me-2"></i> Home
            </a>
            <a href="/transport/my-bookings" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-calendar-check me-2"></i> My Bookings
            </a>
            <a href="/transport/driver-dashboard" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-speedometer2 me-2"></i> Driver Dashboard
            </a>
            <a href="/transport/add-vehicle" class="linkedin-btn linkedin-btn-primary">
              <i class="bi bi-plus-circle me-2"></i> Add Vehicle
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-9">
      <!-- Main Content -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-plus-circle me-2" style="color: var(--network-primary);"></i>
            Add New Vehicle
          </h5>
        </div>
        <div class="linkedin-card-body p-4">
          <% if (locals.error) { %>
            <div class="alert alert-danger" role="alert">
              <%= error %>
            </div>
          <% } %>
          
          <form action="/transport/add-vehicle" method="POST">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="vehicleType" class="form-label">Vehicle Type</label>
                <select class="form-select" id="vehicleType" name="vehicleType" required>
                  <option value="" disabled <%= !locals.formData ? 'selected' : '' %>>Select vehicle type</option>
                  <% Object.entries(vehicleTypes).forEach(([key, value]) => { %>
                    <option value="<%= value %>" <%= locals.formData && formData.vehicleType === value ? 'selected' : '' %>>
                      <%= value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) %>
                    </option>
                  <% }); %>
                </select>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="vehicleMake" class="form-label">Vehicle Make</label>
                <input type="text" class="form-control" id="vehicleMake" name="vehicleMake" required value="<%= locals.formData ? formData.vehicleMake : '' %>">
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="vehicleModel" class="form-label">Vehicle Model</label>
                <input type="text" class="form-control" id="vehicleModel" name="vehicleModel" required value="<%= locals.formData ? formData.vehicleModel : '' %>">
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="vehicleYear" class="form-label">Vehicle Year</label>
                <input type="number" class="form-control" id="vehicleYear" name="vehicleYear" min="1900" max="<%= new Date().getFullYear() %>" required value="<%= locals.formData ? formData.vehicleYear : new Date().getFullYear() %>">
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="vehiclePlate" class="form-label">License Plate</label>
                <input type="text" class="form-control" id="vehiclePlate" name="vehiclePlate" required value="<%= locals.formData ? formData.vehiclePlate : '' %>">
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="vehicleCapacity" class="form-label">Cargo Capacity (kg)</label>
                <input type="text" class="form-control" id="vehicleCapacity" name="vehicleCapacity" required value="<%= locals.formData ? formData.vehicleCapacity : '' %>">
              </div>
              
              <div class="col-12 mt-3">
                <div class="d-grid gap-2">
                  <button type="submit" class="linkedin-btn linkedin-btn-primary">
                    <i class="bi bi-plus-lg me-2"></i> Add Vehicle
                  </button>
                  <a href="/transport/driver-dashboard" class="linkedin-btn linkedin-btn-outline">
                    <i class="bi bi-x-lg me-2"></i> Cancel
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/toast-notifications.js"></script>

<%- include('../partials/footer') %>
