/**
 * Market Trends CSS
 * Styles for the market trends page
 */

/* Variables */
:root {
  --primary-color: #4CAF50;
  --primary-light: #8BC34A;
  --primary-dark: #388E3C;
  --primary-bg-light: rgba(76, 175, 80, 0.1);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
}

/* General Styles */
.market-trends-container {
  padding: 1rem;
}

/* Card Styles */
.market-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.market-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.market-card .card-header {
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  font-weight: 600;
}

/* Table Styles */
.market-table th {
  font-weight: 600;
  color: var(--gray-700);
  background-color: var(--gray-100);
}

.market-table td {
  vertical-align: middle;
}

.market-table tr:hover {
  background-color: var(--primary-bg-light);
}

/* Price Change Indicators */
.price-up {
  color: var(--success-color);
}

.price-down {
  color: var(--danger-color);
}

.price-neutral {
  color: var(--gray-600);
}

/* Filter Form */
.filter-form {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-form label {
  font-weight: 500;
  color: var(--gray-700);
}

.filter-form .form-control,
.filter-form .form-select {
  border-color: var(--gray-300);
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

/* Chart Containers */
.chart-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 400px;
}

/* Alert Styles */
.price-alert {
  background-color: white;
  border-left: 4px solid var(--warning-color);
  border-radius: 0.25rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-alert .alert-icon {
  color: var(--warning-color);
  font-size: 1.25rem;
  margin-right: 0.75rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }

  .market-table {
    font-size: 0.875rem;
  }

  .market-table th,
  .market-table td {
    padding: 0.5rem;
  }
}

/* Loading Indicators */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-spinner .spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--primary-color);
}

.loading-text {
  margin-top: 1rem;
  color: var(--gray-600);
}

/* Modal Styles */
.market-modal .modal-header {
  background-color: var(--primary-color);
  color: white;
}

.market-modal .modal-footer {
  border-top: 1px solid var(--gray-200);
}

/* Stats Tables */
.stats-table th {
  width: 40%;
  font-weight: 600;
  color: var(--gray-700);
}

.stats-table td {
  width: 60%;
}

/* Chart Type Toggle */
.chart-type-toggle .btn {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.chart-type-toggle .btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--danger-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
