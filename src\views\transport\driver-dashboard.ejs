<%- include('../partials/header') %>

<!-- Transport CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-3">
      <!-- Sidebar -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            Transport Services
          </h5>
        </div>
        <div class="p-3">
          <div class="d-grid gap-2">
            <a href="/transport" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-house-door-fill me-2"></i> Home
            </a>
            <a href="/transport/my-bookings" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-calendar-check me-2"></i> My Bookings
            </a>
            <a href="/transport/driver-dashboard" class="linkedin-btn linkedin-btn-primary">
              <i class="bi bi-speedometer2 me-2"></i> Driver Dashboard
            </a>
            <a href="/transport/add-vehicle" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-plus-circle me-2"></i> Add Vehicle
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-9">
      <!-- Main Content -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-speedometer2 me-2" style="color: var(--network-primary);"></i>
            Driver Dashboard
          </h5>
        </div>
        <div class="linkedin-card-body p-4">
          <div class="row">
            <div class="col-md-6 mb-4">
              <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                  <h5 class="card-title">
                    <i class="bi bi-calendar-check me-2 text-primary"></i>
                    Booking Requests
                  </h5>
                  <h2 class="display-4 fw-bold text-center my-3">
                    <%= bookings.filter(b => b.status === bookingStatus.PENDING).length %>
                  </h2>
                  <p class="card-text text-center text-muted">Pending booking requests</p>
                </div>
              </div>
            </div>
            
            <div class="col-md-6 mb-4">
              <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                  <h5 class="card-title">
                    <i class="bi bi-truck me-2 text-success"></i>
                    My Vehicles
                  </h5>
                  <h2 class="display-4 fw-bold text-center my-3">
                    <%= vehicles.length %>
                  </h2>
                  <p class="card-text text-center text-muted">Registered vehicles</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Booking Requests -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-calendar-check me-2" style="color: var(--network-primary);"></i>
            Booking Requests
          </h5>
        </div>
        <div class="linkedin-card-body p-0">
          <% 
            const pendingBookings = bookings.filter(b => b.status === bookingStatus.PENDING);
            if (pendingBookings.length > 0) { 
          %>
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Customer</th>
                    <th>Pickup</th>
                    <th>Delivery</th>
                    <th>Cargo</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% pendingBookings.forEach(booking => { %>
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <% if (booking.user && booking.user.photoURL) { %>
                            <img src="<%= booking.user.photoURL %>" alt="Customer photo" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                          <% } else { %>
                            <div class="d-flex align-items-center justify-content-center bg-light rounded-circle me-2" style="width: 40px; height: 40px;">
                              <i class="bi bi-person-fill text-secondary"></i>
                            </div>
                          <% } %>
                          <div>
                            <div class="fw-bold"><%= booking.user ? booking.user.displayName : 'Customer' %></div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div><%= booking.pickupLocation %></div>
                        <div class="small text-muted">
                          <%= booking.pickupDate ? new Date(booking.pickupDate).toLocaleDateString() : 'N/A' %>
                          <br>
                          <%= booking.pickupDate ? new Date(booking.pickupDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : '' %>
                        </div>
                      </td>
                      <td>
                        <div><%= booking.deliveryLocation %></div>
                        <div class="small text-muted">
                          <%= booking.deliveryDate ? new Date(booking.deliveryDate).toLocaleDateString() : 'N/A' %>
                          <br>
                          <%= booking.deliveryDate ? new Date(booking.deliveryDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : '' %>
                        </div>
                      </td>
                      <td>
                        <div><%= booking.cargoDescription %></div>
                        <div class="small text-muted"><%= booking.cargoWeight %> kg</div>
                      </td>
                      <td>
                        <div class="btn-group">
                          <button class="btn btn-sm btn-success">
                            <i class="bi bi-check-lg me-1"></i> Accept
                          </button>
                          <button class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-x-lg me-1"></i> Decline
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          <% } else { %>
            <div class="p-4 text-center">
              <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Pending Requests</h5>
              <p class="text-muted">You don't have any pending booking requests at the moment.</p>
            </div>
          <% } %>
        </div>
      </div>
      
      <!-- My Vehicles -->
      <div class="linkedin-card">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            My Vehicles
          </h5>
          <a href="/transport/add-vehicle" class="linkedin-btn linkedin-btn-text">
            <i class="bi bi-plus-lg me-1"></i> Add Vehicle
          </a>
        </div>
        <div class="linkedin-card-body p-0">
          <% if (vehicles && vehicles.length > 0) { %>
            <div class="row m-0">
              <% vehicles.forEach(vehicle => { %>
                <div class="col-md-6 p-3">
                  <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                      <h5 class="card-title">
                        <%= vehicle.make %> <%= vehicle.model %> (<%= vehicle.year %>)
                      </h5>
                      <div class="mb-2">
                        <span class="badge bg-primary">
                          <%= vehicle.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) %>
                        </span>
                        <span class="badge bg-secondary">
                          <%= vehicle.licensePlate %>
                        </span>
                      </div>
                      <p class="card-text">
                        <strong>Capacity:</strong> <%= vehicle.capacity %> kg
                      </p>
                      <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="vehicle-<%= vehicle.id %>" <%= vehicle.isAvailable ? 'checked' : '' %>>
                        <label class="form-check-label" for="vehicle-<%= vehicle.id %>">
                          <%= vehicle.isAvailable ? 'Available for bookings' : 'Not available' %>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              <% }); %>
            </div>
          <% } else { %>
            <div class="p-4 text-center">
              <i class="bi bi-truck text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Vehicles</h5>
              <p class="text-muted">You haven't added any vehicles yet.</p>
              <a href="/transport/add-vehicle" class="linkedin-btn linkedin-btn-primary mt-2">
                <i class="bi bi-plus-lg me-1"></i> Add Vehicle
              </a>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/toast-notifications.js"></script>

<%- include('../partials/footer') %>
