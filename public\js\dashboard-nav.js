document.addEventListener('DOMContentLoaded', function() {
  // Set active link based on current URL
  const currentPath = window.location.pathname;
  const navLinks = document.querySelectorAll('.dashboard-nav-link');
  
  navLinks.forEach(link => {
    // Remove active class from all links
    link.classList.remove('active');
    
    // Get the href attribute
    const href = link.getAttribute('href');
    
    // Check if the current path matches the link's href
    if (currentPath === href || 
        (href !== '/dashboard' && currentPath.startsWith(href))) {
      link.classList.add('active');
    } else if (currentPath === '/' && href === '/dashboard') {
      // Special case for home page
      link.classList.add('active');
    }
  });
  
  // Add hover effects
  navLinks.forEach(link => {
    link.addEventListener('mouseenter', function() {
      this.style.transform = 'translateX(5px)';
    });
    
    link.addEventListener('mouseleave', function() {
      this.style.transform = 'translateX(0)';
    });
  });
  
  // Add click animation
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      // Don't add animation if it's the current page
      if (this.classList.contains('active')) {
        return;
      }
      
      // Create ripple effect
      const ripple = document.createElement('span');
      ripple.classList.add('nav-ripple');
      
      // Position the ripple
      const rect = this.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      
      // Add ripple to the link
      this.appendChild(ripple);
      
      // Remove ripple after animation
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });
  
  // Mobile navigation handling
  const isMobile = window.innerWidth < 768;
  
  if (isMobile) {
    // Add text labels for mobile
    navLinks.forEach(link => {
      const icon = link.querySelector('i');
      const text = link.textContent.trim();
      
      // Clear the link content
      link.innerHTML = '';
      
      // Add back the icon
      link.appendChild(icon);
      
      // Add a span for the text
      const span = document.createElement('span');
      span.textContent = text;
      span.classList.add('ms-2', 'd-none', 'd-md-inline');
      link.appendChild(span);
    });
  }
});
