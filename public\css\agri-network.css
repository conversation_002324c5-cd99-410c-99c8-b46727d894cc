/* Agricultural-themed Network Page CSS */

:root {
  /* Use the main app's theme variables */
  --network-primary: var(--theme-primary, #4CAF50);
  --network-primary-dark: var(--theme-primary-dark, #388E3C);
  --network-primary-light: var(--theme-primary-light, #8BC34A);
  --network-secondary: var(--theme-secondary, #795548);
  --network-secondary-light: var(--theme-secondary-light, #A1887F);
  --network-accent: var(--theme-accent, #F5DEB3);
  --network-background: var(--theme-background, #f0f2f5);
  --network-border: var(--theme-border, #8BC34A);
  --network-black: var(--theme-text-dark, #333333);
  --network-gray: var(--theme-text-muted, #666666);
  --network-light-gray: #f3f2ef;
  --network-hover: var(--theme-hover, rgba(76, 175, 80, 0.1));
  --network-success: var(--theme-primary-dark, #388E3C);
}

/* Main container styles */
.linkedin-container {
  background-color: var(--network-background);
  padding: 20px 0;
  min-height: calc(100vh - 56px);
}

/* Card styles */
.linkedin-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
}

.linkedin-card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--network-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.linkedin-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--network-black);
  margin: 0;
}

.linkedin-card-body {
  padding: 16px 20px;
}

.linkedin-card-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--network-border);
  background-color: var(--network-hover);
}

/* Profile card */
.profile-card {
  text-align: center;
  padding: 20px;
}

.profile-background {
  height: 60px;
  background-color: var(--network-primary-light);
  margin: -20px -20px 0;
}

.profile-photo {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  border: 2px solid #fff;
  margin-top: -36px;
  object-fit: cover;
  background-color: #fff;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  margin: 16px 0 0;
}

.profile-headline {
  font-size: 14px;
  color: var(--network-gray);
  margin: 4px 0 16px;
}

/* Connection cards */
.connection-card {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid var(--network-border);
  transition: all 0.3s ease;
  position: relative;
}

.connection-card:last-child {
  border-bottom: none;
}

.connection-card:hover {
  background-color: var(--network-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.connection-card.active {
  background-color: rgba(76, 175, 80, 0.1);
}

/* Hover shadow effect for cards */
.hover-shadow {
  transition: all 0.3s ease;
}

.hover-shadow:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.connection-photo {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.connection-info {
  flex: 1;
}

.connection-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--network-black);
  margin: 0 0 4px;
}

.connection-headline {
  font-size: 14px;
  color: var(--network-gray);
  margin: 0 0 8px;
}

.connection-location {
  font-size: 12px;
  color: var(--network-gray);
  display: flex;
  align-items: center;
}

.connection-location i {
  margin-right: 4px;
  font-size: 12px;
}

.connection-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* Button styles */
.linkedin-btn {
  border-radius: 16px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.linkedin-btn i {
  margin-right: 6px;
}

.linkedin-btn-primary {
  background-color: var(--network-primary);
  color: white;
}

.linkedin-btn-primary:hover {
  background-color: var(--network-primary-dark);
}

.linkedin-btn-outline {
  background-color: transparent;
  color: var(--network-primary);
  border: 1px solid var(--network-primary);
}

.linkedin-btn-outline:hover {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--network-primary-dark);
  color: var(--network-primary-dark);
}

.linkedin-btn-text {
  background-color: transparent;
  color: var(--network-gray);
  padding: 6px 8px;
}

.linkedin-btn-text:hover {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--network-primary);
}

/* Tabs */
.linkedin-tabs {
  display: flex;
  border-bottom: 1px solid var(--network-border);
  margin-bottom: 16px;
}

.linkedin-tab {
  padding: 16px 20px;
  font-size: 14px;
  font-weight: 600;
  color: var(--network-gray);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.linkedin-tab.active {
  color: var(--network-primary);
  border-bottom-color: var(--network-primary);
}

.linkedin-tab:hover:not(.active) {
  color: var(--network-black);
  background-color: rgba(76, 175, 80, 0.1);
}

/* Badge */
.linkedin-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background-color: var(--network-light-gray);
  color: var(--network-gray);
}

.linkedin-badge-primary {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--network-primary);
}

/* Online indicator */
.online-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--network-primary);
  border: 2px solid white;
  position: absolute;
  bottom: 0;
  right: 0;
}

/* Conversation list icon */
.conversation-list-icon {
  color: var(--network-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .linkedin-container {
    padding: 12px 0;
  }

  .connection-actions {
    flex-direction: column;
  }
}
