<div class="row justify-content-center">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header bg-success text-white">
        <h3 class="mb-0">Reset Your Password</h3>
      </div>
      <div class="card-body">
        <% if (typeof error !== 'undefined') { %>
          <div class="alert alert-danger" role="alert">
            <%= error %>
          </div>
        <% } %>
        
        <% if (typeof success !== 'undefined') { %>
          <div class="alert alert-success" role="alert">
            <%= success %>
          </div>
        <% } %>
        
        <p class="mb-4">Enter your email address below and we'll send you instructions to reset your password.</p>
        
        <form action="/auth/forgot-password" method="POST" id="forgotPasswordForm">
          <div class="mb-3">
            <label for="email" class="form-label">Email address</label>
            <input type="email" class="form-control" id="email" name="email" required>
          </div>
          
          <div class="d-grid">
            <button type="submit" class="btn btn-success">Send Reset Link</button>
          </div>
        </form>
        
        <div class="mt-3 text-center">
          <p>Remembered your password? <a href="/auth/login">Back to Login</a></p>
        </div>
        
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide success and error alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
              setTimeout(() => {
                alert.classList.add('fade');
                setTimeout(() => {
                  alert.style.display = 'none';
                }, 500);
              }, 5000);
            });
          });
        </script>
      </div>
    </div>
  </div>
</div>
