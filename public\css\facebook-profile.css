/* Facebook-style Profile CSS */

/* Cover Photo */
.profile-cover {
  position: relative;
  height: 350px;
  background-color: #f0f2f5;
  background-size: cover;
  background-position: center;
  border-radius: 0 0 8px 8px;
  margin-bottom: 60px;
}

.profile-cover-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9ecef;
  color: #6c757d;
}

.cover-photo-edit {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.cover-photo-edit:hover {
  background-color: #ffffff;
}

/* Profile Picture */
.profile-picture-container {
  position: absolute;
  bottom: -50px;
  left: 30px;
  z-index: 10;
}

.profile-picture {
  width: 168px;
  height: 168px;
  border-radius: 50%;
  border: 4px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  background-color: #ffffff;
}

.profile-picture-placeholder {
  width: 168px;
  height: 168px;
  border-radius: 50%;
  border: 4px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9ecef;
  color: #6c757d;
}

.profile-picture-edit {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: #e4e6eb;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.profile-picture-edit:hover {
  background-color: #d8dadf;
}

/* Profile Info */
.profile-info {
  padding-left: 200px;
  padding-bottom: 20px;
  position: relative;
}

.profile-name {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.profile-headline {
  font-size: 15px;
  color: #65676b;
  margin-bottom: 10px;
}

.profile-meta {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #65676b;
}

.profile-meta-item {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.profile-meta-item i {
  margin-right: 5px;
}

.profile-actions {
  position: absolute;
  right: 15px;
  bottom: 20px;
  display: flex;
  gap: 10px;
}

/* Profile Navigation */
.profile-nav {
  border-top: 1px solid #e4e6eb;
  border-bottom: 1px solid #e4e6eb;
  margin-bottom: 20px;
}

.profile-nav .nav-link {
  padding: 15px 20px;
  font-weight: 600;
  color: #65676b;
  position: relative;
}

.profile-nav .nav-link.active {
  color: #1877f2;
}

.profile-nav .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #1877f2;
}

.profile-nav .nav-link:hover {
  background-color: #f0f2f5;
}

/* Profile Content */
.profile-content {
  margin-bottom: 30px;
}

/* About Section */
.about-section {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.about-section h5 {
  font-weight: 600;
  margin-bottom: 15px;
  color: #050505;
}

.about-item {
  display: flex;
  margin-bottom: 15px;
}

.about-item-icon {
  width: 36px;
  height: 36px;
  background-color: #e4e6eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.about-item-content {
  flex-grow: 1;
}

.about-item-title {
  font-weight: 600;
  margin-bottom: 0;
}

.about-item-subtitle {
  color: #65676b;
  font-size: 13px;
}

/* Posts Section */
.post-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.post-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
}

.post-user-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.post-user-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  background-color: #e4e6eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #65676b;
}

.post-user-info {
  flex-grow: 1;
}

.post-user-name {
  font-weight: 600;
  margin-bottom: 0;
  font-size: 15px;
}

.post-time {
  color: #65676b;
  font-size: 13px;
}

.post-content {
  padding: 0 16px 16px;
}

.post-text {
  margin-bottom: 12px;
}

.post-image {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
  border-radius: 8px;
  margin-bottom: 12px;
}

.post-actions {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  border-top: 1px solid #e4e6eb;
}

.post-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  color: #65676b;
  font-weight: 600;
  cursor: pointer;
  border-radius: 4px;
}

.post-action:hover {
  background-color: #f0f2f5;
}

.post-action i {
  margin-right: 5px;
}

/* Photos Section */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.photo-item {
  position: relative;
  padding-bottom: 100%;
  overflow: hidden;
  border-radius: 4px;
}

.photo-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* JavaScript Enhancements */

/* Profile Picture Hover Effect */
.profile-picture-edit {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.profile-picture-container:hover .profile-picture-edit {
  opacity: 1;
}

/* Cover Photo Hover Effect */
.cover-photo-edit {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.profile-cover:hover .cover-photo-edit {
  opacity: 1;
}

/* Post Creation */
.post-card textarea {
  resize: none;
  overflow: hidden;
  min-height: 60px;
  transition: height 0.2s ease;
}

.post-photo-preview {
  transition: all 0.3s ease;
}

/* Photo Gallery */
.photo-item {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.photo-item:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Notification */
.profile-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  max-width: 350px;
  display: none;
}

.profile-notification.success {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.profile-notification.error {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

/* Tab Transitions */
.tab-pane {
  transition: opacity 0.3s ease;
}

.tab-pane.fade:not(.show) {
  display: block;
  opacity: 0;
  height: 0;
  overflow: hidden;
}

/* Profile Info Shadow */
.profile-info.shadow {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Post Hover Effect */
.post-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.post-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

/* Post Actions Hover Effect */
.post-action {
  transition: background-color 0.2s ease, color 0.2s ease;
}

.post-action:hover {
  background-color: #e9ecef;
  color: #198754;
}

/* About Section Hover Effect */
.about-item {
  transition: background-color 0.2s ease;
  border-radius: 8px;
  padding: 8px;
}

.about-item:hover {
  background-color: #f8f9fa;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .profile-cover {
    height: 200px;
    margin-bottom: 80px;
  }

  .profile-picture {
    width: 120px;
    height: 120px;
  }

  .profile-picture-placeholder {
    width: 120px;
    height: 120px;
  }

  .profile-info {
    padding-left: 15px;
  }

  .profile-name {
    font-size: 24px;
    margin-top: 60px;
  }

  .profile-actions {
    position: static;
    margin-top: 15px;
  }

  .photos-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .profile-notification {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}
