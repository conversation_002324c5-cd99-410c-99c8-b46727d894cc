/* Chart Bot Styles */

.chart-bot-page {
  background-color: #f8f9fa;
}

/* Chat Interface Styles */
.chat-messages-container {
  height: 500px;
  overflow-y: auto;
  padding: 1rem;
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.chat-message {
  display: flex;
  margin-bottom: 1rem;
  animation: slideInUp 0.3s ease-out;
}

.chat-message.user-message {
  justify-content: flex-end;
}

.chat-message.bot-message {
  justify-content: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0.5rem;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #4CAF50;
  color: white;
  order: 2;
}

.bot-message .message-avatar {
  background-color: #2196F3;
  color: white;
  order: 1;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  position: relative;
}

.user-message .message-content {
  background-color: #4CAF50;
  color: white;
  border-bottom-right-radius: 4px;
  order: 1;
}

.bot-message .message-content {
  background-color: #f1f3f4;
  color: #333;
  border-bottom-left-radius: 4px;
  order: 2;
}

.message-text {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

.message-sources {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.message-sources .badge {
  font-size: 0.7rem;
  margin: 0.1rem;
}

/* Chat Input Styles */
.chat-input-container {
  background-color: #ffffff;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.chat-suggestions {
  margin-top: 0.5rem;
}

.chat-suggestions .btn {
  margin: 0.2rem;
  font-size: 0.8rem;
}

/* Status Indicator */
.chart-bot-status {
  display: flex;
  align-items: center;
}

.chart-bot-status .badge {
  font-size: 0.75rem;
}

/* Chart Modal Styles */
.modal-xl {
  max-width: 90%;
}

#chart-container {
  position: relative;
  width: 100%;
  height: 400px;
}

#chart-canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Saved Charts Styles */
.saved-chart-item {
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.saved-chart-item:hover {
  border-color: #4CAF50;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.saved-chart-item h6 {
  color: #333;
  font-size: 0.9rem;
}

.saved-chart-item small {
  font-size: 0.75rem;
}

/* Quick Actions */
.fb-card .btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.85rem;
}

/* Loading Animation */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-messages-container {
    height: 400px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .modal-xl {
    max-width: 95%;
  }
  
  .chat-suggestions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

@media (max-width: 576px) {
  .chat-messages-container {
    height: 350px;
    padding: 0.5rem;
  }
  
  .message-content {
    max-width: 90%;
    padding: 0.5rem 0.75rem;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
  }
  
  .chat-input-container {
    padding: 0.75rem;
  }
  
  .saved-chart-item {
    padding: 0.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .chart-bot-page {
    background-color: #1a1a1a;
  }
  
  .chat-messages-container,
  .chat-input-container,
  .saved-chart-item {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
  
  .bot-message .message-content {
    background-color: #404040;
    color: #ffffff;
  }
  
  .saved-chart-item h6 {
    color: #ffffff;
  }
}

/* Custom Scrollbar */
.chat-messages-container::-webkit-scrollbar {
  width: 6px;
}

.chat-messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Chart Bot Specific Animations */
.chart-bot-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(76, 175, 80, 0.3);
  border-radius: 50%;
  border-top-color: #4CAF50;
  animation: spin 1s ease-in-out infinite;
}

/* Success/Error States */
.message-success {
  border-left: 4px solid #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.message-error {
  border-left: 4px solid #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.message-warning {
  border-left: 4px solid #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

/* Chart Type Indicators */
.chart-type-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 0.5rem;
}

.chart-type-line {
  background-color: #2196F3;
}

.chart-type-bar {
  background-color: #4CAF50;
}

.chart-type-pie {
  background-color: #FF9800;
}

.chart-type-doughnut {
  background-color: #9C27B0;
}
