import express from 'express';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../config/initFirebase.js';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// This route is for development purposes only
// It allows a logged-in user to make themselves an admin
router.get('/', isAuthenticated, async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).send('You must be logged in to use this feature');
    }
    
    const userRef = doc(db, 'users', req.user.uid);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return res.status(404).send('User not found in database');
    }
    
    // Make the user an admin
    await updateDoc(userRef, {
      isAdmin: true
    });
    
    res.send(`
      <h1>Admin Access Granted</h1>
      <p>You are now an admin user.</p>
      <p><a href="/admin">Go to Admin Dashboard</a></p>
    `);
  } catch (error) {
    console.error('Error making user admin:', error);
    res.status(500).send('Error making user admin: ' + error.message);
  }
});

export default router;
