<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Auth Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Firebase Authentication Test</h3>
                    </div>
                    <div class="card-body">
                        <div id="status" class="alert alert-info">
                            Initializing Firebase...
                        </div>
                        
                        <div id="auth-section" style="display: none;">
                            <h5>Register Test User</h5>
                            <form id="registerForm">
                                <div class="mb-3">
                                    <input type="email" class="form-control" id="regEmail" placeholder="Email" value="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <input type="password" class="form-control" id="regPassword" placeholder="Password" value="test123456">
                                </div>
                                <button type="submit" class="btn btn-primary">Register</button>
                            </form>
                            
                            <hr>
                            
                            <h5>Login Test User</h5>
                            <form id="loginForm">
                                <div class="mb-3">
                                    <input type="email" class="form-control" id="loginEmail" placeholder="Email" value="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <input type="password" class="form-control" id="loginPassword" placeholder="Password" value="test123456">
                                </div>
                                <button type="submit" class="btn btn-success">Login</button>
                            </form>
                            
                            <hr>
                            
                            <div id="userInfo" style="display: none;">
                                <h5>Current User</h5>
                                <div id="userDetails"></div>
                                <button id="logoutBtn" class="btn btn-danger">Logout</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            createUserWithEmailAndPassword,
            signInWithEmailAndPassword,
            signOut,
            onAuthStateChanged
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
            authDomain: "sustainablefarming-bf265.firebaseapp.com",
            projectId: "sustainablefarming-bf265",
            storageBucket: "sustainablefarming-bf265.appspot.com",
            messagingSenderId: "89904373415",
            appId: "1:89904373415:web:2b8bbc14c7802554cac582"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        const status = document.getElementById('status');
        const authSection = document.getElementById('auth-section');
        const userInfo = document.getElementById('userInfo');
        const userDetails = document.getElementById('userDetails');

        // Show auth section when Firebase is ready
        setTimeout(() => {
            status.textContent = 'Firebase initialized successfully!';
            status.className = 'alert alert-success';
            authSection.style.display = 'block';
        }, 1000);

        // Auth state observer
        onAuthStateChanged(auth, (user) => {
            if (user) {
                userDetails.innerHTML = `
                    <p><strong>UID:</strong> ${user.uid}</p>
                    <p><strong>Email:</strong> ${user.email}</p>
                    <p><strong>Display Name:</strong> ${user.displayName || 'Not set'}</p>
                `;
                userInfo.style.display = 'block';
                status.textContent = 'User is logged in!';
                status.className = 'alert alert-success';
            } else {
                userInfo.style.display = 'none';
                status.textContent = 'No user logged in';
                status.className = 'alert alert-warning';
            }
        });

        // Register form
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            
            try {
                status.textContent = 'Creating user...';
                status.className = 'alert alert-info';
                
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                console.log('User created:', userCredential.user);
                
                status.textContent = 'User created successfully!';
                status.className = 'alert alert-success';
            } catch (error) {
                console.error('Registration error:', error);
                status.textContent = `Registration error: ${error.message}`;
                status.className = 'alert alert-danger';
            }
        });

        // Login form
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                status.textContent = 'Logging in...';
                status.className = 'alert alert-info';
                
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                console.log('User logged in:', userCredential.user);
                
                status.textContent = 'Logged in successfully!';
                status.className = 'alert alert-success';
            } catch (error) {
                console.error('Login error:', error);
                status.textContent = `Login error: ${error.message}`;
                status.className = 'alert alert-danger';
            }
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', async () => {
            try {
                await signOut(auth);
                status.textContent = 'Logged out successfully!';
                status.className = 'alert alert-info';
            } catch (error) {
                console.error('Logout error:', error);
                status.textContent = `Logout error: ${error.message}`;
                status.className = 'alert alert-danger';
            }
        });
    </script>
</body>
</html>
