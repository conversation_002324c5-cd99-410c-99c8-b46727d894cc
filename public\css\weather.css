/* Enhanced Weather Page Styles - Dark Theme */

:root {
  /* Use the main app's theme variables */
  --weather-primary: var(--theme-primary, #4CAF50);
  --weather-secondary: #2196F3;
  --weather-accent: #FF9800;
  --weather-danger: #F44336;
  --weather-warning: #FFC107;
  --weather-info: #03A9F4;
  --weather-success: var(--theme-primary, #4CAF50);
  --weather-light: #f8f9fa;
  --weather-dark: #1a1a2e;
  --weather-dark-lighter: #242444;
  --weather-dark-card: #2d2d52;
  --weather-text-primary: #ffffff;
  --weather-text-secondary: rgba(255, 255, 255, 0.7);
  --weather-text-muted: rgba(255, 255, 255, 0.5);
  --weather-gradient-start: rgba(42, 42, 85, 0.9);
  --weather-gradient-end: rgba(26, 26, 46, 0.9);
  --weather-card-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* Main weather page */
body.weather-page {
  background-color: var(--weather-dark);
  color: var(--weather-text-primary);
}

.weather-page .container {
  max-width: 1200px;
}

/* Main weather container */
.weather-container {
  background-color: var(--weather-dark-lighter);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--weather-card-shadow);
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.weather-header {
  background: linear-gradient(135deg, var(--weather-gradient-start), var(--weather-gradient-end));
  color: var(--weather-text-primary);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.weather-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/weather-pattern.png');
  opacity: 0.05;
  z-index: 1;
}

.weather-header-content {
  position: relative;
  z-index: 2;
}

.weather-icon {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

.forecast-card {
  background-color: var(--weather-dark-card);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.forecast-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.weather-detail-icon {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--weather-primary);
}

.weather-temp {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.5rem;
  color: var(--weather-text-primary);
}

.weather-feels-like {
  font-size: 1.1rem;
  color: var(--weather-text-secondary);
  margin-bottom: 1rem;
}

.weather-description {
  text-transform: capitalize;
  font-size: 1.4rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--weather-text-primary);
}

.weather-location {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--weather-text-primary);
}

.weather-date {
  font-size: 1.1rem;
  color: var(--weather-text-secondary);
  margin-bottom: 1rem;
}

.weather-detail-card {
  background-color: var(--weather-dark-card);
  border-radius: 10px;
  padding: 1.25rem;
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.weather-detail-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.weather-detail-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--weather-text-primary);
}

.weather-detail-label {
  font-size: 0.9rem;
  color: var(--weather-text-secondary);
  margin-top: 0.25rem;
}

.forecast-day {
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  color: var(--weather-text-primary);
}

.forecast-date {
  font-size: 0.85rem;
  color: var(--weather-text-secondary);
  margin-bottom: 0.75rem;
}

.forecast-temp {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--weather-text-primary);
  margin-bottom: 0.25rem;
}

.forecast-min-max {
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.forecast-max {
  color: var(--weather-danger);
  font-weight: 600;
}

.forecast-min {
  color: var(--weather-info);
  font-weight: 600;
}

.forecast-description {
  text-transform: capitalize;
  font-size: 0.9rem;
  color: var(--weather-text-secondary);
  margin-bottom: 0.75rem;
}

.forecast-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--weather-text-secondary);
}

/* Loading spinner */
.weather-loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(26, 26, 46, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: var(--weather-text-primary);
}

.weather-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.weather-loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Spinning animation for loading icon */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Weather map */
#weatherMap {
  border-radius: 5px;
  overflow: hidden;
}

/* Farming weather recommendations */
.farming-recommendation {
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: 10px;
  background-color: var(--weather-dark-card);
  border-left: 5px solid var(--weather-primary);
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.farming-recommendation:hover {
  transform: translateX(5px);
}

.farming-recommendation-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--weather-text-primary);
}

.farming-recommendation-icon {
  color: var(--weather-primary);
  font-size: 1.2rem;
}

.farming-condition {
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: 10px;
  background-color: var(--weather-dark-card);
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.farming-condition:hover {
  transform: translateX(5px);
}

.farming-condition-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--weather-text-primary);
}

.condition-favorable {
  border-left: 5px solid var(--weather-success);
}

.condition-favorable .farming-condition-icon {
  color: var(--weather-success);
}

.condition-neutral {
  border-left: 5px solid var(--weather-warning);
}

.condition-neutral .farming-condition-icon {
  color: var(--weather-warning);
}

.condition-unfavorable {
  border-left: 5px solid var(--weather-danger);
}

.condition-unfavorable .farming-condition-icon {
  color: var(--weather-danger);
}

/* Air Quality Gauge */
.aqi-gauge {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 12px solid var(--weather-dark-lighter);
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease;
  background-color: var(--weather-dark-card);
}

.aqi-gauge:hover {
  transform: scale(1.05);
}

.aqi-gauge::before {
  content: '';
  position: absolute;
  top: -12px;
  left: -12px;
  right: -12px;
  bottom: -12px;
  border-radius: 50%;
  background: conic-gradient(
    #00e400 0% 20%,
    #ffff00 20% 40%,
    #ff7e00 40% 60%,
    #ff0000 60% 80%,
    #99004c 80% 100%
  );
  z-index: -1;
  opacity: 0.7;
}

.aqi-value {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.25rem;
  color: var(--weather-text-primary);
}

.aqi-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--weather-text-primary);
}

/* Air Quality Components */
.aqi-component-card {
  background-color: var(--weather-dark-card);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: var(--weather-card-shadow);
  height: 100%;
  transition: transform 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.aqi-component-card:hover {
  transform: translateY(-5px);
}

.aqi-component-title {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  color: var(--weather-text-primary);
}

.aqi-component-value {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--weather-text-primary);
}

.aqi-component-desc {
  font-size: 0.85rem;
  color: var(--weather-text-secondary);
}

/* Weather Alerts */
.weather-alert {
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  border-radius: 10px;
  background-color: rgba(255, 243, 205, 0.1);
  border-left: 5px solid var(--weather-warning);
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.weather-alert:hover {
  transform: translateX(5px);
}

.weather-alert-title {
  font-weight: 700;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--weather-text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.weather-alert-time {
  font-size: 0.9rem;
  color: var(--weather-text-secondary);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.weather-alert-description {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--weather-text-primary);
}

/* Hourly Forecast */
.hourly-forecast-scroll {
  overflow-x: auto;
  padding: 1rem 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: var(--weather-primary) transparent;
}

.hourly-forecast-scroll::-webkit-scrollbar {
  height: 6px;
}

.hourly-forecast-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.hourly-forecast-scroll::-webkit-scrollbar-thumb {
  background-color: var(--weather-primary);
  border-radius: 20px;
}

.hourly-forecast-item {
  min-width: 100px;
  text-align: center;
  padding: 1rem 0.75rem;
  margin-right: 1rem;
  border-radius: 10px;
  background-color: var(--weather-dark-card);
  box-shadow: var(--weather-card-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.hourly-forecast-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

.hourly-forecast-time {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--weather-text-primary);
}

.hourly-forecast-temp {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0.5rem 0;
  color: var(--weather-text-primary);
}

.hourly-forecast-pop {
  font-size: 0.85rem;
  color: var(--weather-info);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.weather-icon-small {
  width: 40px;
  height: 40px;
  margin: 0.5rem 0;
}

/* Unit toggle switch */
.form-switch .form-check-input {
  width: 3em;
  height: 1.5em;
}

/* Weather Map */
.weather-map-container {
  height: 400px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--weather-card-shadow);
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.weather-map-controls {
  background-color: var(--weather-dark-card);
  padding: 0.75rem;
  border-radius: 10px;
  box-shadow: var(--weather-card-shadow);
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.weather-map-btn {
  padding: 0.5rem 1rem;
  border-radius: 5px;
  background-color: var(--weather-dark-lighter);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--weather-text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.weather-map-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.weather-map-btn.active {
  background-color: var(--weather-primary);
  color: white;
  border-color: var(--weather-primary);
}

/* Settings Panel */
.weather-settings {
  background-color: var(--weather-dark-card);
  border-radius: 10px;
  padding: 1.25rem;
  box-shadow: var(--weather-card-shadow);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.weather-settings-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--weather-text-primary);
}

.weather-settings-option {
  margin-bottom: 1rem;
  color: var(--weather-text-secondary);
}

/* Card headers */
.card-header {
  background-color: var(--weather-dark-lighter) !important;
  color: var(--weather-text-primary) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.card-body {
  background-color: var(--weather-dark-card);
}

.text-muted {
  color: var(--weather-text-secondary) !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .weather-header {
    padding: 1.5rem;
  }

  .weather-temp {
    font-size: 3.5rem;
  }

  .weather-location {
    font-size: 1.8rem;
  }

  .weather-map-container {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .weather-header {
    padding: 1.25rem;
  }

  .weather-temp {
    font-size: 3rem;
  }

  .weather-location {
    font-size: 1.6rem;
  }

  .forecast-card {
    margin-bottom: 1rem;
  }

  .aqi-gauge {
    width: 150px;
    height: 150px;
  }

  .aqi-value {
    font-size: 2.5rem;
  }

  .hourly-forecast-item {
    min-width: 90px;
  }

  .weather-map-container {
    height: 300px;
  }
}

@media (max-width: 576px) {
  .weather-header {
    padding: 1rem;
  }

  .weather-temp {
    font-size: 2.5rem;
  }

  .weather-location {
    font-size: 1.4rem;
  }

  .weather-icon {
    width: 60px;
    height: 60px;
  }

  .aqi-gauge {
    width: 120px;
    height: 120px;
    border-width: 8px;
  }

  .aqi-gauge::before {
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
  }

  .aqi-value {
    font-size: 2rem;
  }

  .aqi-label {
    font-size: 1rem;
  }

  .hourly-forecast-item {
    min-width: 80px;
    padding: 0.75rem 0.5rem;
  }

  .weather-map-container {
    height: 250px;
  }
}
