import express from 'express';
import multer from 'multer';
import path from 'path';
import { isAuthenticated } from '../middleware/auth.js';
import {
  addUpload,
  getUploads,
  getUploadById,
  getUploadsByUser,
  deleteUpload,
  likeUpload,
  addComment,
  deleteComment
} from '../services/firebaseService.js';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage(); // Store files in memory
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept images, videos, and PDFs
    const filetypes = /jpeg|jpg|png|gif|webp|mp4|webm|ogg|pdf/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only images, videos, and PDFs are allowed'));
  }
});

// API endpoint for dynamic content loading (temporarily remove auth for testing)
router.get('/api/content', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filter = req.query.filter || 'all';

    console.log('API content request:', { page, limit, filter });

    const result = await getUploads({ filter, page, limit: limit });

    console.log('API content response:', {
      uploadsCount: result.uploads ? result.uploads.length : 0,
      total: result.total,
      hasMore: result.hasMore
    });

    res.json({
      success: true,
      uploads: result.uploads || [],
      pagination: {
        page: result.page || page,
        limit: result.limit || limit,
        total: result.total || 0,
        hasMore: result.hasMore || false
      }
    });
  } catch (error) {
    console.error('Error getting content via API:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      uploads: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        hasMore: false
      }
    });
  }
});

// Get all uploads
router.get('/', async (req, res) => {
  try {
    const result = await getUploads({});
    const uploads = result.uploads || [];
    res.render('uploads/index', { uploads });
  } catch (error) {
    console.error('Error getting uploads:', error);
    res.render('uploads/index', {
      uploads: [],
      error: 'Error loading uploads: ' + error.message
    });
  }
});

// Get upload form
router.get('/new', isAuthenticated, (req, res) => {
  res.render('uploads/new');
});

// Get my uploads
router.get('/my-uploads', isAuthenticated, async (req, res) => {
  try {
    const uploads = await getUploadsByUser(req.user.uid);
    res.render('uploads/my-uploads', {
      uploads,
      user: req.user
    });
  } catch (error) {
    console.error('Error getting my uploads:', error);
    res.render('uploads/my-uploads', {
      uploads: [],
      user: req.user,
      error: 'Error loading your uploads: ' + error.message
    });
  }
});

// Create new upload with file
router.post('/', isAuthenticated, upload.single('file'), async (req, res) => {
  try {
    console.log('Upload request received:', {
      body: req.body,
      file: req.file ? 'File received' : 'No file'
    });

    // Extract form data
    const { title, description, category } = req.body;

    // Validate required fields
    if (!title || !description || !category) {
      throw new Error('Title, description, and category are required');
    }

    if (!req.file) {
      throw new Error('Please select a file to upload');
    }

    // Create a data URL from the file buffer
    const fileBuffer = req.file.buffer;
    const fileType = req.file.mimetype;
    const base64Data = fileBuffer.toString('base64');
    const dataUrl = `data:${fileType};base64,${base64Data}`;

    console.log('File converted to data URL');

    // Add upload to Firebase
    await addUpload({
      title,
      description,
      category,
      fileUrl: dataUrl,
      fileName: req.file.originalname,
      fileType: req.file.mimetype
    });

    console.log('Upload added successfully');

    // Redirect to dashboard
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Error creating upload:', error);
    res.render('uploads/new', {
      error: 'Error creating upload: ' + error.message,
      formData: req.body
    });
  }
});

// Get upload details
router.get('/:id', async (req, res) => {
  try {
    const upload = await getUploadById(req.params.id);

    if (!upload) {
      return res.status(404).render('error', {
        error: 'Upload not found',
        message: 'The content you are looking for does not exist.'
      });
    }

    res.render('uploads/show', { upload });
  } catch (error) {
    console.error('Error getting upload:', error);
    res.render('error', {
      error: 'Error loading content',
      message: error.message
    });
  }
});

// Delete upload
router.post('/:id/delete', isAuthenticated, async (req, res) => {
  try {
    await deleteUpload(req.params.id);

    // Redirect to dashboard instead of uploads page
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Error deleting upload:', error);

    // Handle Firebase specific errors
    let errorMessage = error.message;
    if (error.code === 'auth/requires-recent-login') {
      errorMessage = 'For security reasons, please log out and log back in before deleting content.';
    }

    res.render('error', {
      error: 'Error deleting content',
      message: errorMessage
    });
  }
});

// Test endpoint to verify API is working (no auth required)
router.get('/test-api', (req, res) => {
  console.log('=== TEST API ENDPOINT CALLED ===');
  console.log('Request method:', req.method);
  console.log('Request path:', req.path);
  console.log('User authenticated:', !!req.user);
  console.log('User ID:', req.user ? req.user.uid : 'No user');

  res.json({
    success: true,
    message: 'API is working!',
    user: req.user ? req.user.uid : 'No user',
    authenticated: !!req.user,
    timestamp: new Date().toISOString()
  });
});

// Like/unlike an upload
router.post('/:id/like', isAuthenticated, async (req, res) => {
  try {
    console.log('Like request received for upload ID:', req.params.id);
    console.log('User:', req.user ? req.user.uid : 'Not authenticated');

    // Call the likeUpload function with user context
    const updatedUpload = await likeUpload(req.params.id, req.user);

    console.log('Like operation completed. Updated upload:', updatedUpload ? 'Success' : 'Failed');

    // If the request is AJAX, return JSON
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      const upload = await getUploadById(req.params.id);

      if (!upload) {
        throw new Error('Upload not found after like operation');
      }

      const likesArray = Array.isArray(upload.likes) ? upload.likes : [];
      const response = {
        success: true,
        likeCount: upload.likeCount || likesArray.length || 0,
        liked: likesArray.includes(req.user.uid)
      };

      console.log('Sending response:', response);
      return res.json(response);
    }

    // Otherwise redirect back to the referrer or dashboard
    res.redirect(req.headers.referer || '/dashboard');
  } catch (error) {
    console.error('Error liking upload:', error);

    // If the request is AJAX, return JSON error
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(400).json({ success: false, error: error.message });
    }

    // Otherwise render error page
    res.render('error', {
      error: 'Error liking content',
      message: error.message
    });
  }
});

// Add a comment to an upload
router.post('/:id/comments', isAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    console.log('Comment request received for upload ID:', id);
    console.log('Comment text:', text);
    console.log('User:', req.user ? req.user.uid : 'Not authenticated');

    if (!text || !text.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Comment text is required'
      });
    }

    // Use the authenticated user
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'You must be logged in to comment'
      });
    }

    // Use the addComment function from firebaseService
    try {
      const updatedUpload = await addComment(id, text.trim(), user);
      console.log('Comment added successfully');

      // Get the latest comment from the updated upload
      const comments = updatedUpload.comments || [];
      const latestComment = comments[comments.length - 1];

      res.json({
        success: true,
        comment: latestComment,
        commentCount: updatedUpload.commentCount || comments.length,
        message: 'Comment added successfully'
      });

    } catch (error) {
      console.error('Error adding comment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to add comment: ' + error.message
      });
    }

  } catch (error) {
    console.error('Error in comment route:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add comment'
    });
  }
});

// Delete a comment
router.post('/:id/comments/:commentId/delete', isAuthenticated, async (req, res) => {
  try {
    await deleteComment(req.params.id, req.params.commentId);

    // If the request is AJAX, return JSON
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      const upload = await getUploadById(req.params.id);
      return res.json({
        success: true,
        comments: upload.comments || [],
        commentCount: upload.commentCount || 0
      });
    }

    // Otherwise redirect back to the referrer or dashboard
    res.redirect(req.headers.referer || '/dashboard');
  } catch (error) {
    console.error('Error deleting comment:', error);

    // If the request is AJAX, return JSON error
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(400).json({ success: false, error: error.message });
    }

    // Otherwise render error page
    res.render('error', {
      error: 'Error deleting comment',
      message: error.message
    });
  }
});

// Get share links for an upload
router.get('/:id/share', async (req, res) => {
  try {
    const upload = await getUploadById(req.params.id);

    if (!upload) {
      throw new Error('Content not found');
    }

    // Generate share links
    const shareUrl = `${req.protocol}://${req.get('host')}/uploads/${req.params.id}`;
    const shareTitle = upload.title;
    const shareDescription = upload.description;

    const shareLinks = {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareTitle)}`,
      whatsapp: `https://api.whatsapp.com/send?text=${encodeURIComponent(`${shareTitle} - ${shareUrl}`)}`,
      email: `mailto:?subject=${encodeURIComponent(shareTitle)}&body=${encodeURIComponent(`${shareDescription}\n\n${shareUrl}`)}`,
      copyLink: shareUrl
    };

    // If the request is AJAX, return JSON
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.json({ success: true, shareLinks });
    }

    // Otherwise render share page
    res.render('uploads/share', {
      upload,
      shareLinks
    });
  } catch (error) {
    console.error('Error generating share links:', error);

    // If the request is AJAX, return JSON error
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(400).json({ success: false, error: error.message });
    }

    // Otherwise render error page
    res.render('error', {
      error: 'Error sharing content',
      message: error.message
    });
  }
});

export default router;
