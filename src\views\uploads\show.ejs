<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/uploads">Community Content</a></li>
        <li class="breadcrumb-item active" aria-current="page"><%= upload ? upload.title : 'Content Details' %></li>
      </ol>
    </nav>
  </div>
</div>

<% if (!upload) { %>
  <div class="row">
    <div class="col-md-12">
      <div class="alert alert-danger" role="alert">
        Content not found or an error occurred.
      </div>
      <a href="/uploads" class="btn btn-primary">Back to Community Content</a>
    </div>
  </div>
<% } else { %>
  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <h1 class="mb-3"><%= upload.title %></h1>
          
          <div class="d-flex justify-content-between align-items-center mb-3">
            <span class="badge bg-secondary fs-6"><%= upload.category %></span>
            <small class="text-muted">
              Posted on <%= new Date(upload.createdAt).toLocaleDateString() %>
            </small>
          </div>
          
          <div class="content-preview mb-4">
            <% if (upload.fileUrl) { %>
              <% if (upload.fileUrl.endsWith('.jpg') || upload.fileUrl.endsWith('.jpeg') || upload.fileUrl.endsWith('.png') || upload.fileUrl.endsWith('.gif')) { %>
                <img src="<%= upload.fileUrl %>" class="img-fluid rounded" alt="<%= upload.title %>">
              <% } else if (upload.fileUrl.endsWith('.mp4') || upload.fileUrl.endsWith('.webm') || upload.fileUrl.endsWith('.ogg')) { %>
                <div class="ratio ratio-16x9">
                  <video controls>
                    <source src="<%= upload.fileUrl %>" type="video/mp4">
                    Your browser does not support the video tag.
                  </video>
                </div>
              <% } else if (upload.fileUrl.endsWith('.pdf')) { %>
                <div class="text-center py-5 bg-light rounded">
                  <i class="bi bi-file-pdf text-danger" style="font-size: 5rem;"></i>
                  <p class="mt-3">PDF Document</p>
                  <a href="<%= upload.fileUrl %>" class="btn btn-primary" target="_blank">
                    <i class="bi bi-eye"></i> View PDF
                  </a>
                </div>
              <% } else { %>
                <div class="text-center py-5 bg-light rounded">
                  <i class="bi bi-file-earmark text-primary" style="font-size: 5rem;"></i>
                  <p class="mt-3">File</p>
                  <a href="<%= upload.fileUrl %>" class="btn btn-primary" target="_blank">
                    <i class="bi bi-download"></i> Download File
                  </a>
                </div>
              <% } %>
            <% } %>
          </div>
          
          <h5>Description</h5>
          <p class="lead"><%= upload.description %></p>
          
          <div class="d-flex justify-content-between align-items-center mt-4">
            <a href="/uploads" class="btn btn-outline-secondary">
              <i class="bi bi-arrow-left"></i> Back to Community Content
            </a>
            
            <div>
              <a href="<%= upload.fileUrl %>" class="btn btn-primary" target="_blank">
                <i class="bi bi-download"></i> Download
              </a>
              
              <% if (typeof user !== 'undefined' && user && user.uid === upload.userId) { %>
                <button type="button" class="btn btn-danger ms-2" data-bs-toggle="modal" data-bs-target="#deleteUploadModal">
                  <i class="bi bi-trash"></i> Delete
                </button>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">About the Contributor</h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <div class="avatar-placeholder bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
              <i class="bi bi-person-circle text-success" style="font-size: 2.5rem;"></i>
            </div>
            <h5><%= upload.userName %></h5>
          </div>
          <p class="card-text">This user has shared their farming knowledge and resources with the community.</p>
        </div>
      </div>
      
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Share This Content</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button class="btn btn-outline-primary" id="copyLinkBtn">
              <i class="bi bi-link-45deg"></i> Copy Link
            </button>
            <button class="btn btn-outline-success" id="shareBtn">
              <i class="bi bi-share"></i> Share
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Upload Modal -->
  <% if (typeof user !== 'undefined' && user && user.uid === upload.userId) { %>
    <div class="modal fade" id="deleteUploadModal" tabindex="-1" aria-labelledby="deleteUploadModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="deleteUploadModalLabel">Confirm Delete</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete "<%= upload.title %>"? This action cannot be undone.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <form action="/uploads/<%= upload.id %>/delete" method="POST">
              <button type="submit" class="btn btn-danger">Delete</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  <% } %>
<% } %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Copy link functionality
    const copyLinkBtn = document.getElementById('copyLinkBtn');
    if (copyLinkBtn) {
      copyLinkBtn.addEventListener('click', function() {
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
          // Change button text temporarily
          const originalText = copyLinkBtn.innerHTML;
          copyLinkBtn.innerHTML = '<i class="bi bi-check-lg"></i> Copied!';
          setTimeout(() => {
            copyLinkBtn.innerHTML = originalText;
          }, 2000);
        });
      });
    }
    
    // Share functionality (if Web Share API is available)
    const shareBtn = document.getElementById('shareBtn');
    if (shareBtn) {
      if (navigator.share) {
        shareBtn.addEventListener('click', function() {
          navigator.share({
            title: '<%= upload ? upload.title : "Content" %>',
            text: '<%= upload ? upload.description : "Check out this content" %>',
            url: window.location.href
          })
          .catch(console.error);
        });
      } else {
        // If Web Share API is not available, hide the share button
        shareBtn.style.display = 'none';
      }
    }
  });
</script>
