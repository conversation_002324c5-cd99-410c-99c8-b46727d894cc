# Sustainable Farming Web Application

A web application for sustainable farming practices, built with Node.js and Firebase.

## Features

- User authentication (registration, login, profile management)
- Dashboard for accessing sustainable farming resources
- Resources section with farming tips and guides
- Weather widget for farmers
- Profile management with personal and farm information
- Responsive design for all devices

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- Firebase account

## Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/sustainable-farming.git
cd sustainable-farming
```

2. Install dependencies:
```
npm install
```

3. Set up Firebase:
   - See [Firebase Setup Instructions](FIREBASE_SETUP.md) for detailed steps
   - Create a Firebase project
   - Set up Authentication (Email/Password)
   - Create a Firestore database
   - Configure security rules
   - Get your Firebase configuration

4. Update Firebase configuration:
   - Open `src/config/firebase.js`
   - Replace the configuration with your own Firebase project details

## Running the Application

### Development Mode

```
npm run dev
```

This will start the server with nodemon, which automatically restarts when you make changes.

### Production Mode

```
npm start
```

The application will be available at http://localhost:3000

## Authentication Options

This application supports two authentication methods:

### 1. Firebase Authentication (Default)

The application is configured to use Firebase Authentication and Firestore for data storage. This provides:
- Secure user authentication
- Cloud-based data storage
- Data persistence across sessions and devices

To use Firebase Authentication, follow the setup instructions in [FIREBASE_SETUP.md](FIREBASE_SETUP.md).

### 2. Local Authentication (Development/Testing)

The application requires proper Firebase configuration for authentication and data storage.

## Project Structure

```
sustainable-farming/
├── public/                 # Static assets
│   ├── css/                # CSS files
│   └── js/                 # Client-side JavaScript
├── src/                    # Server-side code
│   ├── config/             # Configuration files
│   ├── middleware/         # Express middleware
│   ├── routes/             # Route handlers
│   ├── views/              # EJS templates
│   └── index.js            # Main application file
├── .env                    # Environment variables
├── package.json            # Project metadata and dependencies
└── README.md               # Project documentation
```

## License

This project is licensed under the ISC License.
