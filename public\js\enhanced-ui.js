// Enhanced UI Components and Interactions

class EnhancedUI {
  constructor() {
    this.init();
  }

  init() {
    this.initTooltips();
    this.initModals();
    this.initNotifications();
    this.initSmoothScrolling();
    this.initFormValidation();
    this.initLoadingStates();
    this.initAnimations();
    this.initAccessibility();
  }

  // Enhanced Tooltips
  initTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
      element.addEventListener('mouseenter', this.showTooltip.bind(this));
      element.addEventListener('mouseleave', this.hideTooltip.bind(this));
    });
  }

  showTooltip(event) {
    const element = event.target;
    const text = element.getAttribute('data-tooltip');
    
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-popup';
    tooltip.textContent = text;
    tooltip.style.cssText = `
      position: absolute;
      background: #333;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      z-index: 1000;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s ease;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    
    setTimeout(() => tooltip.style.opacity = '1', 10);
    element._tooltip = tooltip;
  }

  hideTooltip(event) {
    const element = event.target;
    if (element._tooltip) {
      element._tooltip.remove();
      delete element._tooltip;
    }
  }

  // Enhanced Modals
  initModals() {
    const modalTriggers = document.querySelectorAll('[data-modal]');
    modalTriggers.forEach(trigger => {
      trigger.addEventListener('click', this.openModal.bind(this));
    });

    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal-backdrop')) {
        this.closeModal();
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeModal();
      }
    });
  }

  openModal(event) {
    event.preventDefault();
    const modalId = event.target.getAttribute('data-modal');
    const modal = document.getElementById(modalId);
    
    if (modal) {
      modal.style.display = 'flex';
      modal.style.opacity = '0';
      modal.style.transform = 'scale(0.9)';
      
      setTimeout(() => {
        modal.style.opacity = '1';
        modal.style.transform = 'scale(1)';
      }, 10);
      
      // Focus management for accessibility
      const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }
  }

  closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
      modal.style.opacity = '0';
      modal.style.transform = 'scale(0.9)';
      setTimeout(() => {
        modal.style.display = 'none';
      }, 200);
    });
  }

  // Enhanced Notifications
  initNotifications() {
    this.notificationContainer = this.createNotificationContainer();
  }

  createNotificationContainer() {
    const container = document.createElement('div');
    container.id = 'notification-container';
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-width: 400px;
    `;
    document.body.appendChild(container);
    return container;
  }

  showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    const colors = {
      success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
      error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
      warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
      info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
    };
    
    const color = colors[type] || colors.info;
    
    notification.style.cssText = `
      background: ${color.bg};
      border: 1px solid ${color.border};
      color: ${color.text};
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transform: translateX(100%);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    `;
    
    notification.innerHTML = `
      <div style="display: flex; align-items: center; gap: 12px;">
        <i class="bi bi-${this.getNotificationIcon(type)}" style="font-size: 18px;"></i>
        <span style="flex: 1;">${message}</span>
        <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: inherit;">×</button>
      </div>
    `;
    
    this.notificationContainer.appendChild(notification);
    
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 10);
    
    if (duration > 0) {
      setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
      }, duration);
    }
    
    notification.addEventListener('click', () => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    });
  }

  getNotificationIcon(type) {
    const icons = {
      success: 'check-circle-fill',
      error: 'exclamation-triangle-fill',
      warning: 'exclamation-triangle-fill',
      info: 'info-circle-fill'
    };
    return icons[type] || icons.info;
  }

  // Smooth Scrolling
  initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }

  // Enhanced Form Validation
  initFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', this.validateForm.bind(this));
      
      const inputs = form.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.addEventListener('blur', () => this.validateField(input));
        input.addEventListener('input', () => this.clearFieldError(input));
      });
    });
  }

  validateForm(event) {
    const form = event.target;
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;

    inputs.forEach(input => {
      if (!this.validateField(input)) {
        isValid = false;
      }
    });

    if (!isValid) {
      event.preventDefault();
      this.showNotification('Please fill in all required fields correctly.', 'error');
    }
  }

  validateField(input) {
    const value = input.value.trim();
    const type = input.type;
    let isValid = true;
    let message = '';

    // Required field validation
    if (input.hasAttribute('required') && !value) {
      isValid = false;
      message = 'This field is required.';
    }

    // Email validation
    if (type === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      isValid = false;
      message = 'Please enter a valid email address.';
    }

    // Password validation
    if (type === 'password' && value && value.length < 6) {
      isValid = false;
      message = 'Password must be at least 6 characters long.';
    }

    this.showFieldError(input, isValid ? '' : message);
    return isValid;
  }

  showFieldError(input, message) {
    this.clearFieldError(input);
    
    if (message) {
      input.style.borderColor = '#dc3545';
      
      const errorDiv = document.createElement('div');
      errorDiv.className = 'field-error';
      errorDiv.style.cssText = `
        color: #dc3545;
        font-size: 12px;
        margin-top: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
      `;
      errorDiv.innerHTML = `<i class="bi bi-exclamation-circle"></i> ${message}`;
      
      input.parentNode.appendChild(errorDiv);
    }
  }

  clearFieldError(input) {
    input.style.borderColor = '';
    const errorDiv = input.parentNode.querySelector('.field-error');
    if (errorDiv) {
      errorDiv.remove();
    }
  }

  // Loading States
  initLoadingStates() {
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('btn-loading')) {
        this.showButtonLoading(e.target);
      }
    });
  }

  showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = `
      <span class="spinner-enhanced"></span>
      Loading...
    `;
    
    button._originalText = originalText;
  }

  hideButtonLoading(button) {
    button.disabled = false;
    button.innerHTML = button._originalText || button.innerHTML;
  }

  // Animations
  initAnimations() {
    this.observeElements();
  }

  observeElements() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
      observer.observe(el);
    });
  }

  // Accessibility Enhancements
  initAccessibility() {
    // Skip to main content link
    this.addSkipLink();
    
    // Keyboard navigation for custom components
    this.initKeyboardNavigation();
    
    // ARIA live regions for dynamic content
    this.initAriaLiveRegions();
  }

  addSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: white;
      padding: 8px;
      text-decoration: none;
      z-index: 10000;
      border-radius: 4px;
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  initKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Custom keyboard handlers can be added here
      if (e.key === 'Enter' && e.target.classList.contains('clickable')) {
        e.target.click();
      }
    });
  }

  initAriaLiveRegions() {
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.id = 'live-region';
    document.body.appendChild(liveRegion);
  }

  announceToScreenReader(message) {
    const liveRegion = document.getElementById('live-region');
    if (liveRegion) {
      liveRegion.textContent = message;
    }
  }
}

// Initialize Enhanced UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.enhancedUI = new EnhancedUI();
});

// Utility functions for global use
window.showNotification = (message, type, duration) => {
  if (window.enhancedUI) {
    window.enhancedUI.showNotification(message, type, duration);
  }
};

window.showLoading = (button) => {
  if (window.enhancedUI) {
    window.enhancedUI.showButtonLoading(button);
  }
};

window.hideLoading = (button) => {
  if (window.enhancedUI) {
    window.enhancedUI.hideButtonLoading(button);
  }
};
