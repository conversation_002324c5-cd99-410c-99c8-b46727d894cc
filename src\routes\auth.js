import express from 'express';
import {
  registerUser,
  loginUser,
  logoutUser,
  getCurrentUser,
  updateUserProfile,
  changeUserPassword,
  getUploads
} from '../services/firebaseService.js';

const router = express.Router();

// Register page route
router.get('/register', async (req, res) => {
  try {
    // Get community uploads to display on the registration page
    const uploads = await getUploads();

    res.render('auth/register', {
      uploads: uploads,
      showDashboard: true
    });
  } catch (error) {
    console.error('Error loading uploads for register page:', error);
    res.render('auth/register', {
      uploads: [],
      showDashboard: true,
      error: 'Error loading community content'
    });
  }
});

// Login page route
router.get('/login', async (req, res) => {
  try {
    const registered = req.query.registered === 'true';
    const message = req.query.message || '';

    // Get community uploads to display on the login page
    const result = await getUploads({});
    const uploads = result.uploads || [];

    res.render('auth/login', {
      registered: registered,
      success: registered ? 'Registration successful! Please log in with your new account.' : undefined,
      message: message,
      uploads: uploads,
      showDashboard: true
    });
  } catch (error) {
    console.error('Error loading uploads for login page:', error);
    res.render('auth/login', {
      registered: req.query.registered === 'true',
      success: req.query.registered === 'true' ? 'Registration successful! Please log in with your new account.' : undefined,
      message: req.query.message || '',
      uploads: [],
      showDashboard: true,
      error: 'Error loading community content'
    });
  }
});

// Register user API
router.post('/register', async (req, res) => {
  try {
    const {
      email,
      password,
      confirmPassword,
      firstName,
      lastName,
      location,
      farmName,
      termsAgreement
    } = req.body;

    // Validate form data
    if (password !== confirmPassword) {
      throw new Error('Passwords do not match');
    }

    if (!termsAgreement) {
      throw new Error('You must agree to the Terms and Conditions');
    }

    // Create user with Firebase
    await registerUser({
      email,
      password,
      firstName,
      lastName,
      displayName: `${firstName} ${lastName}`,
      location,
      farmName: farmName || ''
    });

    // Redirect to login page with success message
    res.redirect('/auth/login?registered=true');
  } catch (error) {
    console.error('Error registering user:', error);

    // Handle Firebase specific errors
    let errorMessage = error.message;
    if (error.code === 'auth/email-already-in-use') {
      errorMessage = 'This email is already in use. Please use a different email or try logging in.';
    } else if (error.code === 'auth/weak-password') {
      errorMessage = 'Password is too weak. Please use a stronger password.';
    }

    try {
      // Get community uploads to display on the registration page
      const result = await getUploads({});
      const uploads = result.uploads || [];

      res.render('auth/register', {
        error: errorMessage,
        formData: req.body, // Pass form data back to pre-fill the form
        uploads: uploads,
        showDashboard: true
      });
    } catch (uploadError) {
      console.error('Error loading uploads for register error page:', uploadError);
      res.render('auth/register', {
        error: errorMessage,
        formData: req.body,
        uploads: [],
        showDashboard: true
      });
    }
  }
});

// Login user API
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Sign in user with Firebase
    await loginUser(email, password);

    // Redirect to dashboard
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Error logging in:', error);

    // Handle Firebase specific errors
    let errorMessage = error.message;
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      errorMessage = 'Invalid email or password. Please try again.';
    } else if (error.code === 'auth/too-many-requests') {
      errorMessage = 'Too many failed login attempts. Please try again later or reset your password.';
    }

    try {
      // Get community uploads to display on the login page
      const result = await getUploads({});
      const uploads = result.uploads || [];

      res.render('auth/login', {
        error: errorMessage,
        uploads: uploads,
        showDashboard: true
      });
    } catch (uploadError) {
      console.error('Error loading uploads for login error page:', uploadError);
      res.render('auth/login', {
        error: errorMessage,
        uploads: [],
        showDashboard: true
      });
    }
  }
});

// Logout route
router.get('/logout', async (req, res) => {
  try {
    // Sign out user with Firebase
    await logoutUser();

    // Redirect to home page
    res.redirect('/');
  } catch (error) {
    console.error('Error logging out:', error);
    res.redirect('/');
  }
});

// Forgot password page route
router.get('/forgot-password', (req, res) => {
  res.render('auth/forgot-password');
});

// Forgot password API
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    // Import the sendPasswordResetEmail function from Firebase Auth
    const { sendPasswordResetEmail } = await import('firebase/auth');
    const { auth } = await import('../config/firebase.js');

    // Send password reset email
    await sendPasswordResetEmail(auth, email);

    // Render the forgot password page with success message
    res.render('auth/forgot-password', {
      success: 'If an account exists with this email, a password reset link will be sent. Check your inbox for further instructions.'
    });
  } catch (error) {
    console.error('Error processing forgot password request:', error);

    // Don't reveal if the email exists or not for security reasons
    res.render('auth/forgot-password', {
      success: 'If an account exists with this email, a password reset link will be sent. Check your inbox for further instructions.'
    });
  }
});

export default router;
