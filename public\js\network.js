document.addEventListener('DOMContentLoaded', function() {
  // Initialize post creation functionality
  initPostCreation();
  
  // Initialize post interactions (like, comment, share)
  initPostInteractions();
  
  // Initialize connection request functionality
  initConnectionRequests();
  
  // Load more posts when scrolling
  initInfiniteScroll();
});

// Post Creation
function initPostCreation() {
  const postText = document.getElementById('post-text');
  const createPostBtn = document.getElementById('create-post-btn');
  const uploadImageBtn = document.getElementById('upload-image-btn');
  const postImageInput = document.getElementById('post-image');
  const imagePreviewContainer = document.getElementById('image-preview-container');
  const imagePreview = document.getElementById('image-preview');
  const removeImageBtn = document.getElementById('remove-image-btn');
  
  // Enable/disable post button based on text content
  if (postText) {
    postText.addEventListener('input', function() {
      createPostBtn.disabled = postText.value.trim() === '';
    });
  }
  
  // Image upload functionality
  if (uploadImageBtn && postImageInput) {
    uploadImageBtn.addEventListener('click', function() {
      postImageInput.click();
    });
    
    postImageInput.addEventListener('change', function() {
      if (postImageInput.files && postImageInput.files[0]) {
        const file = postImageInput.files[0];
        const reader = new FileReader();
        
        reader.onload = function(e) {
          imagePreview.src = e.target.result;
          imagePreviewContainer.classList.remove('d-none');
          createPostBtn.disabled = false;
        };
        
        reader.readAsDataURL(file);
      }
    });
    
    // Remove image preview
    if (removeImageBtn) {
      removeImageBtn.addEventListener('click', function() {
        imagePreviewContainer.classList.add('d-none');
        imagePreview.src = '';
        postImageInput.value = '';
        createPostBtn.disabled = postText.value.trim() === '';
      });
    }
  }
  
  // Create post submission
  if (createPostBtn) {
    createPostBtn.addEventListener('click', async function() {
      createPostBtn.disabled = true;
      createPostBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Posting...';
      
      try {
        let imageURL = null;
        
        // Upload image if selected
        if (postImageInput.files && postImageInput.files[0]) {
          const formData = new FormData();
          formData.append('image', postImageInput.files[0]);
          
          const imageResponse = await fetch('/network/posts/image', {
            method: 'POST',
            body: formData
          });
          
          const imageResult = await imageResponse.json();
          
          if (!imageResult.success) {
            throw new Error(imageResult.error || 'Failed to upload image');
          }
          
          imageURL = imageResult.imageURL;
        }
        
        // Create the post
        const postData = {
          text: postText.value.trim(),
          type: 'TEXT',
          imageURL: imageURL
        };
        
        const response = await fetch('/network/posts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(postData)
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to create post');
        }
        
        // Reset form
        postText.value = '';
        imagePreviewContainer.classList.add('d-none');
        imagePreview.src = '';
        postImageInput.value = '';
        
        // Reload the page to show the new post
        window.location.reload();
      } catch (error) {
        console.error('Error creating post:', error);
        alert('Error creating post: ' + error.message);
      } finally {
        createPostBtn.disabled = false;
        createPostBtn.innerHTML = 'Post';
      }
    });
  }
}

// Post Interactions (like, comment, share)
function initPostInteractions() {
  // Like functionality
  document.querySelectorAll('.like-btn').forEach(button => {
    button.addEventListener('click', async function() {
      const postId = this.dataset.postId;
      const likeIcon = this.querySelector('i');
      const likeCount = this.querySelector('.like-count');
      
      try {
        const response = await fetch(`/network/posts/${postId}/like`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to like post');
        }
        
        // Update UI
        if (result.liked) {
          likeIcon.classList.remove('bi-hand-thumbs-up');
          likeIcon.classList.add('bi-hand-thumbs-up-fill', 'text-success');
          likeCount.textContent = parseInt(likeCount.textContent) + 1;
        } else {
          likeIcon.classList.remove('bi-hand-thumbs-up-fill', 'text-success');
          likeIcon.classList.add('bi-hand-thumbs-up');
          likeCount.textContent = parseInt(likeCount.textContent) - 1;
        }
      } catch (error) {
        console.error('Error liking post:', error);
        alert('Error liking post: ' + error.message);
      }
    });
  });
  
  // Comment functionality
  document.querySelectorAll('.comment-btn').forEach(button => {
    button.addEventListener('click', async function() {
      const postId = this.dataset.postId;
      const postCard = this.closest('.post-card');
      const commentsSection = postCard.querySelector('.comments-section');
      const commentsContainer = postCard.querySelector('.comments-container');
      
      // Toggle comments section
      commentsSection.classList.toggle('d-none');
      
      // If opening comments section and no comments loaded yet, load them
      if (!commentsSection.classList.contains('d-none') && !commentsContainer.innerHTML.trim()) {
        commentsContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-success" role="status"></div></div>';
        
        try {
          const response = await fetch(`/network/posts/${postId}/comments`);
          const result = await response.json();
          
          if (!result.success) {
            throw new Error(result.error || 'Failed to load comments');
          }
          
          // Render comments
          if (result.comments.length > 0) {
            commentsContainer.innerHTML = result.comments.map(comment => `
              <div class="comment mb-3">
                <div class="d-flex">
                  <div class="me-2">
                    ${comment.author.photoURL 
                      ? `<img src="${comment.author.photoURL}" class="rounded-circle" style="width: 32px; height: 32px; object-fit: cover;">`
                      : `<div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                          <i class="bi bi-person-fill text-secondary" style="font-size: 0.8rem;"></i>
                        </div>`
                    }
                  </div>
                  <div>
                    <div class="bg-light p-2 rounded">
                      <h6 class="mb-0 fs-6">${comment.author.displayName || 'User'}</h6>
                      <p class="mb-0">${comment.text}</p>
                    </div>
                    <small class="text-muted">${new Date(comment.createdAt).toLocaleString()}</small>
                  </div>
                </div>
              </div>
            `).join('');
          } else {
            commentsContainer.innerHTML = '<p class="text-muted text-center">No comments yet. Be the first to comment!</p>';
          }
        } catch (error) {
          console.error('Error loading comments:', error);
          commentsContainer.innerHTML = '<p class="text-danger text-center">Error loading comments. Please try again.</p>';
        }
      }
    });
  });
  
  // Add comment functionality
  document.querySelectorAll('.add-comment-btn').forEach(button => {
    button.addEventListener('click', async function() {
      const postId = this.dataset.postId;
      const postCard = this.closest('.post-card');
      const commentInput = postCard.querySelector('.comment-input');
      const commentText = commentInput.value.trim();
      const commentsContainer = postCard.querySelector('.comments-container');
      const commentCount = postCard.querySelector('.comment-count');
      
      if (!commentText) return;
      
      button.disabled = true;
      
      try {
        const response = await fetch(`/network/posts/${postId}/comments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ text: commentText })
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to add comment');
        }
        
        // Clear input
        commentInput.value = '';
        
        // Update comment count
        commentCount.textContent = parseInt(commentCount.textContent) + 1;
        
        // Reload comments
        const commentsResponse = await fetch(`/network/posts/${postId}/comments`);
        const commentsResult = await commentsResponse.json();
        
        if (!commentsResult.success) {
          throw new Error(commentsResult.error || 'Failed to reload comments');
        }
        
        // Render comments
        commentsContainer.innerHTML = commentsResult.comments.map(comment => `
          <div class="comment mb-3">
            <div class="d-flex">
              <div class="me-2">
                ${comment.author.photoURL 
                  ? `<img src="${comment.author.photoURL}" class="rounded-circle" style="width: 32px; height: 32px; object-fit: cover;">`
                  : `<div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                      <i class="bi bi-person-fill text-secondary" style="font-size: 0.8rem;"></i>
                    </div>`
                }
              </div>
              <div>
                <div class="bg-light p-2 rounded">
                  <h6 class="mb-0 fs-6">${comment.author.displayName || 'User'}</h6>
                  <p class="mb-0">${comment.text}</p>
                </div>
                <small class="text-muted">${new Date(comment.createdAt).toLocaleString()}</small>
              </div>
            </div>
          </div>
        `).join('');
      } catch (error) {
        console.error('Error adding comment:', error);
        alert('Error adding comment: ' + error.message);
      } finally {
        button.disabled = false;
      }
    });
  });
  
  // Share functionality
  const shareModal = new bootstrap.Modal(document.getElementById('sharePostModal'));
  let currentPostId = null;
  
  document.querySelectorAll('.share-btn').forEach(button => {
    button.addEventListener('click', function() {
      currentPostId = this.dataset.postId;
      const postCard = this.closest('.post-card');
      const postText = postCard.querySelector('.card-text').textContent;
      const postAuthor = postCard.querySelector('.card-header h6 a').textContent;
      
      // Set original post preview
      document.querySelector('.original-post-preview').innerHTML = `
        <div class="card-body">
          <h6 class="card-subtitle mb-2 text-muted">Originally posted by ${postAuthor}</h6>
          <p class="card-text">${postText}</p>
        </div>
      `;
      
      // Clear share text
      document.getElementById('share-text').value = '';
      
      // Show modal
      shareModal.show();
    });
  });
  
  // Confirm share button
  document.getElementById('confirm-share-btn').addEventListener('click', async function() {
    if (!currentPostId) return;
    
    const shareText = document.getElementById('share-text').value;
    this.disabled = true;
    this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sharing...';
    
    try {
      const response = await fetch(`/network/posts/${currentPostId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: shareText })
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to share post');
      }
      
      // Update share count
      const shareCount = document.querySelector(`.share-btn[data-post-id="${currentPostId}"] .share-count`);
      shareCount.textContent = parseInt(shareCount.textContent) + 1;
      
      // Hide modal
      shareModal.hide();
      
      // Show success message
      alert('Post shared successfully!');
      
      // Reload the page to show the shared post
      window.location.reload();
    } catch (error) {
      console.error('Error sharing post:', error);
      alert('Error sharing post: ' + error.message);
    } finally {
      this.disabled = false;
      this.innerHTML = 'Share';
    }
  });
}

// Connection Requests
function initConnectionRequests() {
  // Send connection request
  document.querySelectorAll('.send-request').forEach(button => {
    button.addEventListener('click', async function() {
      const recipientId = this.dataset.id;
      this.disabled = true;
      this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
      
      try {
        const response = await fetch('/network/connections/request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ recipientId })
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to send connection request');
        }
        
        // Update button
        this.innerHTML = 'Request Sent';
        this.classList.remove('btn-success');
        this.classList.add('btn-outline-secondary');
        this.disabled = true;
      } catch (error) {
        console.error('Error sending connection request:', error);
        alert('Error sending connection request: ' + error.message);
        this.disabled = false;
        this.innerHTML = '<i class="bi bi-person-plus-fill me-1"></i> Connect';
      }
    });
  });
  
  // Accept connection request
  document.querySelectorAll('.accept-request').forEach(button => {
    button.addEventListener('click', async function() {
      const connectionId = this.dataset.id;
      this.disabled = true;
      
      try {
        const response = await fetch('/network/connections/accept', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ connectionId })
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to accept connection request');
        }
        
        // Reload the page
        window.location.reload();
      } catch (error) {
        console.error('Error accepting connection request:', error);
        alert('Error accepting connection request: ' + error.message);
        this.disabled = false;
      }
    });
  });
  
  // Reject connection request
  document.querySelectorAll('.reject-request').forEach(button => {
    button.addEventListener('click', async function() {
      const connectionId = this.dataset.id;
      this.disabled = true;
      
      try {
        const response = await fetch('/network/connections/reject', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ connectionId })
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to reject connection request');
        }
        
        // Reload the page
        window.location.reload();
      } catch (error) {
        console.error('Error rejecting connection request:', error);
        alert('Error rejecting connection request: ' + error.message);
        this.disabled = false;
      }
    });
  });
  
  // Remove connection
  document.querySelectorAll('.remove-connection').forEach(button => {
    button.addEventListener('click', async function() {
      if (!confirm('Are you sure you want to remove this connection?')) return;
      
      const connectionId = this.dataset.id;
      this.disabled = true;
      
      try {
        const response = await fetch('/network/connections/remove', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ connectionId })
        });
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to remove connection');
        }
        
        // Reload the page
        window.location.reload();
      } catch (error) {
        console.error('Error removing connection:', error);
        alert('Error removing connection: ' + error.message);
        this.disabled = false;
      }
    });
  });
}

// Infinite Scroll for Posts
function initInfiniteScroll() {
  const loadMoreBtn = document.getElementById('load-more-btn');
  let lastVisible = null;
  let loading = false;
  
  if (loadMoreBtn) {
    loadMoreBtn.addEventListener('click', async function() {
      if (loading) return;
      
      loading = true;
      loadMoreBtn.disabled = true;
      loadMoreBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
      
      try {
        const response = await fetch(`/network/feed?lastVisible=${lastVisible}`);
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to load more posts');
        }
        
        // Update last visible
        lastVisible = result.lastVisible;
        
        // Append posts
        const postsContainer = document.getElementById('posts-container');
        
        if (result.posts.length > 0) {
          // Append posts to container
          // This would be a complex HTML structure similar to the EJS template
          // For brevity, this is simplified
          alert('More posts loaded!');
        } else {
          loadMoreBtn.innerHTML = 'No More Posts';
          loadMoreBtn.disabled = true;
        }
      } catch (error) {
        console.error('Error loading more posts:', error);
        alert('Error loading more posts: ' + error.message);
      } finally {
        loading = false;
        loadMoreBtn.disabled = false;
        loadMoreBtn.innerHTML = 'Load More';
      }
    });
  }
}
