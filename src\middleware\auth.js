import { auth } from '../config/initFirebase.js';
import { getUserData, isAuthenticated as checkAuth } from '../services/firebaseService.js';

// Middleware to check if user is authenticated
export const isAuthenticated = (req, res, next) => {
  // Check if user is authenticated using Firebase Auth
  const user = auth.currentUser;

  if (user) {
    // User is signed in with Firebase Auth
    req.user = user;
    next();
  } else {
    // No user is signed in, redirect to login with a message
    res.redirect('/auth/login?message=Please log in to access this page');
  }
};

// Middleware to attach current user to response locals
export const attachCurrentUser = async (req, res, next) => {
  // Get current user from Firebase Auth
  let user = auth.currentUser;

  // Add the current URL to res.locals for use in templates
  res.locals.originalUrl = req.originalUrl;

  if (user) {
    try {
      // Get additional user data from Firestore
      const userData = await getUserData(user.uid);

      // Merge auth user with additional data
      const mergedUser = {
        ...user,
        ...userData
      };

      // User is signed in
      res.locals.user = mergedUser;
      res.locals.isAuthenticated = true;

      // Also attach to request for convenience
      req.user = mergedUser;
      req.userData = userData || {};
    } catch (error) {
      console.error('Error getting user data:', error);
      // Still set the basic user info even if getting additional data fails
      res.locals.user = user;
      res.locals.isAuthenticated = true;
      req.user = user;
      req.userData = {};
    }
  } else {
    // No user is signed in
    res.locals.user = null;
    res.locals.isAuthenticated = false;
    req.userData = {};
  }

  next();
};

// Middleware to check if user is admin and attach admin status
export const attachAdminStatus = async (req, res, next) => {
  // Add the current URL to res.locals for use in templates
  res.locals.originalUrl = req.originalUrl;

  // Default admin status
  res.locals.isAdmin = false;

  // Get current user from Firebase Auth
  let user = auth.currentUser;

  if (user) {
    try {
      // Get additional user data from Firestore
      const userData = await getUserData(user.uid);

      // Check if user is admin
      if (userData && userData.isAdmin) {
        res.locals.isAdmin = true;
        req.isAdmin = true;
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
    }
  }

  next();
};

// Middleware to check if user is admin (requires authentication first)
export const isAdmin = async (req, res, next) => {
  try {
    // Check if user is authenticated first
    if (!req.user) {
      return res.redirect('/auth/login?message=Please log in to access this page');
    }

    // Get user data to check admin status
    const userData = await getUserData(req.user.uid);

    if (userData && userData.isAdmin) {
      req.isAdmin = true;
      next();
    } else {
      // User is not admin, redirect with error
      res.status(403).render('error', {
        error: 'Access Denied',
        message: 'You do not have permission to access this page. Admin privileges required.',
        user: req.user
      });
    }
  } catch (error) {
    console.error('Error checking admin status:', error);
    res.status(500).render('error', {
      error: 'Server Error',
      message: 'Error checking admin permissions: ' + error.message,
      user: req.user
    });
  }
};
