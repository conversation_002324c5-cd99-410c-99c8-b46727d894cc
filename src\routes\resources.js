import express from 'express';
import { db } from '../config/initFirebase.js';
import { collection, getDocs, getDoc, doc, query, orderBy, limit, where } from 'firebase/firestore';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// Get all resources
router.get('/', async (req, res) => {
  try {
    const resourcesSnapshot = await getDocs(
      query(collection(db, 'resources'), orderBy('createdAt', 'desc'))
    );

    const resources = [];
    resourcesSnapshot.forEach((doc) => {
      resources.push({
        id: doc.id,
        ...doc.data()
      });
    });

    res.render('resources/index', { resources });
  } catch (error) {
    console.error('Error getting resources:', error);
    res.render('resources/index', {
      resources: [],
      error: 'Error loading resources: ' + error.message
    });
  }
});

// Get a single resource
router.get('/:id', async (req, res) => {
  try {
    const resourceDoc = await getDoc(doc(db, 'resources', req.params.id));

    if (!resourceDoc.exists()) {
      return res.status(404).render('error', {
        error: 'Resource not found',
        message: 'The resource you are looking for does not exist.'
      });
    }

    const resource = {
      id: resourceDoc.id,
      ...resourceDoc.data()
    };

    res.render('resources/show', { resource });
  } catch (error) {
    console.error('Error getting resource:', error);
    res.render('error', {
      error: 'Error loading resource',
      message: error.message
    });
  }
});

// Get resources by category
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;

    const resourcesSnapshot = await getDocs(
      query(
        collection(db, 'resources'),
        where('category', '==', category),
        orderBy('createdAt', 'desc')
      )
    );

    const resources = [];
    resourcesSnapshot.forEach((doc) => {
      resources.push({
        id: doc.id,
        ...doc.data()
      });
    });

    res.render('resources/category', {
      resources,
      category
    });
  } catch (error) {
    console.error('Error getting resources by category:', error);
    res.render('resources/category', {
      resources: [],
      category: req.params.category,
      error: 'Error loading resources: ' + error.message
    });
  }
});

export default router;
