<!-- AI Chart Bot Page -->
<%
// Set the current path for active navigation highlighting
const currentPath = originalUrl || '';
%>

<!-- Main Content Area with Facebook-style Layout -->
<div class="fb-container">
  <div class="fb-main-content">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
      <a href="/dashboard" class="fb-menu-item <%= currentPath === '/dashboard' ? 'active' : '' %>">
        <i class="bi bi-house-door-fill"></i>
        <span>Home</span>
      </a>

      <a href="/profile" class="fb-menu-item <%= currentPath.includes('/profile') ? 'active' : '' %>">
        <i class="bi bi-person-fill"></i>
        <span>Profile</span>
      </a>

      <a href="/network" class="fb-menu-item <%= currentPath.includes('/network') ? 'active' : '' %>">
        <i class="bi bi-people-fill"></i>
        <span>Farmer Network</span>
      </a>

      <a href="/messaging" class="fb-menu-item <%= currentPath.includes('/messaging') ? 'active' : '' %>">
        <i class="bi bi-chat-dots-fill"></i>
        <span>Messages</span>
      </a>

      <div class="fb-menu-divider"></div>

      <a href="/courses" class="fb-menu-item <%= currentPath === '/courses' ? 'active' : '' %>">
        <i class="bi bi-book-fill"></i>
        <span>Courses</span>
      </a>

      <a href="/courses/my-courses" class="fb-menu-item <%= currentPath.includes('/courses/my-courses') ? 'active' : '' %>">
        <i class="bi bi-journal-check"></i>
        <span>My Courses</span>
      </a>

      <a href="/resources" class="fb-menu-item <%= currentPath.includes('/resources') ? 'active' : '' %>">
        <i class="bi bi-file-earmark-text"></i>
        <span>Resources</span>
      </a>

      <div class="fb-menu-divider"></div>

      <a href="/uploads/new" class="fb-menu-item <%= currentPath.includes('/uploads/new') ? 'active' : '' %>">
        <i class="bi bi-cloud-upload"></i>
        <span>Upload Content</span>
      </a>

      <a href="/uploads/my-uploads" class="fb-menu-item <%= currentPath.includes('/uploads/my-uploads') ? 'active' : '' %>">
        <i class="bi bi-collection"></i>
        <span>My Uploads</span>
      </a>

      <a href="/weather" class="fb-menu-item <%= currentPath.includes('/weather') ? 'active' : '' %>">
        <i class="bi bi-cloud-sun"></i>
        <span>Weather</span>
      </a>

      <a href="/transport" class="fb-menu-item <%= currentPath.includes('/transport') ? 'active' : '' %>">
        <i class="bi bi-truck"></i>
        <span>Transport</span>
      </a>

      <a href="/chart-bot" class="fb-menu-item <%= currentPath.includes('/chart-bot') ? 'active' : '' %>">
        <i class="bi bi-robot"></i>
        <span>AI Chart Bot</span>
      </a>

      <div class="fb-menu-divider"></div>

      <a href="/market-trends" class="fb-menu-item <%= currentPath.includes('/market-trends') ? 'active' : '' %>">
        <i class="bi bi-graph-up"></i>
        <span>Market Trends</span>
      </a>
    </div>

    <!-- Main Content -->
    <div class="fb-content">
      <!-- Page Header -->
      <div class="fb-content-header">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h1 class="fb-page-title">
              <i class="bi bi-robot me-2"></i>
              AI Chart Bot
            </h1>
            <p class="fb-page-subtitle">Your intelligent agricultural assistant for data visualization and insights</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-success" id="clear-history-btn">
              <i class="bi bi-trash"></i> Clear History
            </button>
            <button class="btn btn-success" id="show-charts-btn">
              <i class="bi bi-collection"></i> My Charts
            </button>
          </div>
        </div>
      </div>

      <!-- Error Display -->
      <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-warning mb-4">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <%= error %>
        </div>
      <% } %>

      <!-- Chart Bot Interface -->
      <div class="row">
        <!-- Chat Interface -->
        <div class="col-lg-8">
          <div class="fb-card">
            <div class="fb-card-header">
              <h5 class="mb-0">
                <i class="bi bi-chat-dots me-2"></i>
                Chat with AI Assistant
              </h5>
              <div class="chart-bot-status" id="bot-status">
                <span class="badge bg-secondary">Checking...</span>
              </div>
            </div>
            <div class="fb-card-body">
              <!-- Chat Messages Container -->
              <div id="chat-messages" class="chat-messages-container">
                <!-- Welcome Message -->
                <div class="chat-message bot-message">
                  <div class="message-avatar">
                    <i class="bi bi-robot"></i>
                  </div>
                  <div class="message-content">
                    <div class="message-text">
                      Hello! I'm your AI agricultural assistant. I can help you with:
                      <ul class="mt-2 mb-0">
                        <li>Crop disease identification and treatment</li>
                        <li>Pest management strategies</li>
                        <li>Soil health and fertilizer recommendations</li>
                        <li>Irrigation and water management</li>
                        <li>Market trends and data visualization</li>
                      </ul>
                      Ask me anything about farming and agriculture!
                    </div>
                    <div class="message-time">Just now</div>
                  </div>
                </div>

                <!-- Load Previous Chat History -->
                <% if (chatHistory && chatHistory.length > 0) { %>
                  <% chatHistory.forEach(chat => { %>
                    <div class="chat-message user-message">
                      <div class="message-avatar">
                        <% if (user && user.photoURL) { %>
                          <img src="<%= user.photoURL %>" alt="<%= user.displayName %>">
                        <% } else { %>
                          <i class="bi bi-person-fill"></i>
                        <% } %>
                      </div>
                      <div class="message-content">
                        <div class="message-text"><%= chat.userMessage %></div>
                        <div class="message-time"><%= new Date(chat.timestamp?.toDate ? chat.timestamp.toDate() : chat.timestamp).toLocaleString() %></div>
                      </div>
                    </div>

                    <div class="chat-message bot-message">
                      <div class="message-avatar">
                        <i class="bi bi-robot"></i>
                      </div>
                      <div class="message-content">
                        <div class="message-text"><%= chat.botResponse %></div>
                        <% if (chat.sources && chat.sources.length > 0) { %>
                          <div class="message-sources">
                            <small class="text-muted">Sources:</small>
                            <% chat.sources.forEach(source => { %>
                              <span class="badge bg-light text-dark ms-1"><%= source.title || 'Agricultural Knowledge' %></span>
                            <% }); %>
                          </div>
                        <% } %>
                        <div class="message-time"><%= new Date(chat.timestamp?.toDate ? chat.timestamp.toDate() : chat.timestamp).toLocaleString() %></div>
                      </div>
                    </div>
                  <% }); %>
                <% } %>
              </div>

              <!-- Chat Input -->
              <div class="chat-input-container">
                <div class="input-group">
                  <input type="text"
                         id="chat-input"
                         class="form-control"
                         placeholder="Ask me about farming, crops, diseases, or request data visualizations...">
                  <button class="btn btn-success" id="send-button">
                    <i class="bi bi-send"></i>
                  </button>
                </div>
                <div class="chat-suggestions mt-2">
                  <small class="text-muted">Try asking:</small>
                  <button class="btn btn-sm btn-outline-secondary ms-1" data-suggestion="How do I treat tomato blight?">
                    Tomato blight treatment
                  </button>
                  <button class="btn btn-sm btn-outline-secondary ms-1" data-suggestion="Show me corn price trends">
                    Corn price trends
                  </button>
                  <button class="btn btn-sm btn-outline-secondary ms-1" data-suggestion="Best fertilizers for vegetables">
                    Fertilizer recommendations
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar with Charts and Tools -->
        <div class="col-lg-4">
          <!-- Quick Actions -->
          <div class="fb-card mb-4">
            <div class="fb-card-header">
              <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="fb-card-body">
              <div class="d-grid gap-2">
                <button class="btn btn-outline-primary btn-sm" id="generate-chart-btn">
                  <i class="bi bi-bar-chart"></i> Generate Sample Chart
                </button>
                <button class="btn btn-outline-info btn-sm" id="market-data-btn">
                  <i class="bi bi-graph-up"></i> View Market Data
                </button>
                <button class="btn btn-outline-warning btn-sm" id="export-history-btn">
                  <i class="bi bi-download"></i> Export Chat History
                </button>
              </div>
            </div>
          </div>

          <!-- Saved Charts -->
          <div class="fb-card">
            <div class="fb-card-header">
              <h6 class="mb-0">My Saved Charts</h6>
            </div>
            <div class="fb-card-body">
              <div id="saved-charts-list">
                <% if (userCharts && userCharts.length > 0) { %>
                  <% userCharts.slice(0, 3).forEach(chart => { %>
                    <div class="saved-chart-item" data-chart-id="<%= chart.id %>">
                      <div class="d-flex justify-content-between align-items-center">
                        <div>
                          <h6 class="mb-1"><%= chart.title %></h6>
                          <small class="text-muted"><%= new Date(chart.createdAt?.toDate ? chart.createdAt.toDate() : chart.createdAt).toLocaleDateString() %></small>
                        </div>
                        <div class="btn-group btn-group-sm">
                          <button class="btn btn-outline-primary load-chart-btn" data-chart-id="<%= chart.id %>">
                            <i class="bi bi-eye"></i>
                          </button>
                          <button class="btn btn-outline-danger delete-chart-btn" data-chart-id="<%= chart.id %>">
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  <% }); %>
                  <% if (userCharts.length > 3) { %>
                    <div class="text-center mt-2">
                      <button class="btn btn-sm btn-outline-secondary" id="show-all-charts-btn">
                        View All (<%= userCharts.length %> charts)
                      </button>
                    </div>
                  <% } %>
                <% } else { %>
                  <p class="text-muted text-center">No saved charts yet. Start chatting to create visualizations!</p>
                <% } %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart Modal -->
<div class="modal fade" id="chartModal" tabindex="-1">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Chart Visualization</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="chart-container" style="height: 400px;">
          <!-- Chart will be rendered here -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-success" id="save-chart-btn">
          <i class="bi bi-save"></i> Save Chart
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Include the Facebook-style footer -->
<%- include('../partials/facebook-footer') %>

<!-- Chart Bot specific scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/toast-notifications.js"></script>
<script src="/js/chart-bot.js"></script>

<script>
  // Initialize chart-bot when page loads
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Chart Bot page DOM loaded');

    // Function to initialize with retry mechanism
    function tryInitialize(attempts = 0) {
      if (typeof initializeChartBot === 'function') {
        console.log('Initializing Chart Bot...');
        initializeChartBot();
        return;
      }

      if (attempts < 10) {
        console.log(`Chart Bot functions not ready, retrying... (attempt ${attempts + 1})`);
        setTimeout(() => tryInitialize(attempts + 1), 200);
      } else {
        console.error('Chart Bot initialization failed after 10 attempts');
        console.log('Available functions:', Object.keys(window).filter(key =>
          key.includes('Chart') || key.includes('send') || key.includes('show') || key.includes('handle')
        ));

        // Fallback: try to set up basic functionality manually
        setupBasicFunctionality();
      }
    }

    // Fallback function setup
    function setupBasicFunctionality() {
      console.log('Setting up basic functionality as fallback...');

      // Basic send message functionality
      const sendButton = document.getElementById('send-button');
      const chatInput = document.getElementById('chat-input');

      if (sendButton && chatInput) {
        sendButton.addEventListener('click', function() {
          const message = chatInput.value.trim();
          if (message) {
            console.log('Sending message:', message);
            alert('Chart Bot functions are not fully loaded. Please refresh the page.');
          }
        });

        chatInput.addEventListener('keypress', function(event) {
          if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendButton.click();
          }
        });
      }
    }

    // Start initialization
    tryInitialize();
  });
</script>
