<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sustainable Farming</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/footer.css">
  <link rel="stylesheet" href="/css/weather-widget.css">
  <link rel="stylesheet" href="/css/fixed-header.css">
  <link rel="stylesheet" href="/css/facebook-profile.css">
  <link rel="stylesheet" href="/css/facebook-dashboard.css">
  <link rel="stylesheet" href="/css/network.css">
  <link rel="stylesheet" href="/css/messaging.css">
  <link rel="stylesheet" href="/css/weather.css">
</head>
<body class="fb-body<% if (typeof bodyClass !== 'undefined') { %> <%= bodyClass %><% } %>">
  <%
    // Set current path for header navigation
    const currentPath = typeof originalUrl !== 'undefined' ? originalUrl : '';

    // Include the Facebook-style header
  %>
  <%- include('./partials/facebook-header', { currentPath: currentPath }) %>

  <!-- The body content will be inserted here -->
  <%- body %>

  <footer class="text-center py-3" style="background-color: var(--theme-primary, #4CAF50); color: var(--theme-text-light, white);">
    <div class="container">
      <p>Sustainable Farming &copy; 2025</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/facebook-dashboard.js"></script>
  <script src="/js/weather-widget.js"></script>
</body>
</html>
