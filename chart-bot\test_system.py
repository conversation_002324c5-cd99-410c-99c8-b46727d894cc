#!/usr/bin/env python3
"""
Test script for the Agricultural Chatbot RAG system
"""

import sys
import time
import requests
import json
from typing import Dict, Any

def test_api_connection(base_url: str = "http://localhost:8000") -> bool:
    """Test if the API is running"""
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API Health: {health_data['status']} - {health_data['message']}")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running with: python app.py")
        return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_chat_functionality(base_url: str = "http://localhost:8000") -> bool:
    """Test the chat functionality"""
    test_questions = [
        "What is crop rotation?",
        "How do I treat tomato blight disease?",
        "What are the best methods for controlling aphids?",
        "How do I test soil pH?"
    ]
    
    print("\n🧪 Testing Chat Functionality:")
    print("=" * 50)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. Testing question: '{question}'")
        
        try:
            response = requests.post(
                f"{base_url}/chat",
                json={"query": question, "max_sources": 3},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data['status'] == 'success':
                    print(f"✅ Response received ({len(data['response'])} characters)")
                    print(f"📚 Sources found: {len(data['sources'])}")
                    
                    # Show first 100 characters of response
                    preview = data['response'][:100] + "..." if len(data['response']) > 100 else data['response']
                    print(f"💬 Preview: {preview}")
                    
                    # Show sources
                    if data['sources']:
                        print("📖 Sources:")
                        for j, source in enumerate(data['sources'][:2], 1):  # Show first 2 sources
                            print(f"   {j}. {source['title']} (Score: {source['relevance_score']:.3f})")
                else:
                    print(f"❌ Chat failed: {data.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing question: {e}")
            return False
        
        # Small delay between requests
        time.sleep(1)
    
    return True

def test_sample_questions(base_url: str = "http://localhost:8000") -> bool:
    """Test the sample questions endpoint"""
    try:
        response = requests.get(f"{base_url}/sample-questions", timeout=10)
        if response.status_code == 200:
            data = response.json()
            questions = data.get('sample_questions', [])
            print(f"✅ Sample questions endpoint: {len(questions)} questions available")
            return True
        else:
            print(f"❌ Sample questions endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing sample questions: {e}")
        return False

def test_categories(base_url: str = "http://localhost:8000") -> bool:
    """Test the categories endpoint"""
    try:
        response = requests.get(f"{base_url}/categories", timeout=10)
        if response.status_code == 200:
            data = response.json()
            categories = data.get('categories', [])
            print(f"✅ Categories endpoint: {len(categories)} categories available")
            print(f"📂 Categories: {', '.join(categories[:5])}{'...' if len(categories) > 5 else ''}")
            return True
        else:
            print(f"❌ Categories endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing categories: {e}")
        return False

def main():
    """Run all tests"""
    print("🌱 Agricultural Chatbot RAG System Test")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test 1: API Connection
    print("\n1. Testing API Connection...")
    if not test_api_connection(base_url):
        print("\n❌ API connection failed. Please start the server first:")
        print("   python app.py")
        sys.exit(1)
    
    # Test 2: Sample Questions
    print("\n2. Testing Sample Questions Endpoint...")
    test_sample_questions(base_url)
    
    # Test 3: Categories
    print("\n3. Testing Categories Endpoint...")
    test_categories(base_url)
    
    # Test 4: Chat Functionality
    if not test_chat_functionality(base_url):
        print("\n❌ Chat functionality tests failed")
        sys.exit(1)
    
    # Summary
    print("\n" + "=" * 50)
    print("🎉 All tests passed successfully!")
    print("\n📋 Next steps:")
    print("1. Start the Streamlit interface: streamlit run streamlit_app.py")
    print("2. Open your browser to http://localhost:8501")
    print("3. Start chatting with your agricultural advisor!")
    print("\n💡 Try asking questions like:")
    print("   - 'How do I treat tomato blight disease?'")
    print("   - 'What are the best organic fertilizers?'")
    print("   - 'How can I improve my soil health?'")

if __name__ == "__main__":
    main()
