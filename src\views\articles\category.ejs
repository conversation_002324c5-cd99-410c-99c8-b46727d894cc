<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/articles">Articles</a></li>
        <li class="breadcrumb-item active" aria-current="page"><%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4"><%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %> Articles</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
    
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <a href="/articles/user/my-articles" class="btn btn-outline-success">
          <i class="bi bi-list-check"></i> My Articles
        </a>
        <a href="/articles/new" class="btn btn-success ms-2">
          <i class="bi bi-plus-circle"></i> Write New Article
        </a>
      </div>
      
      <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
          Filter by Category
        </button>
        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
          <li><a class="dropdown-item" href="/articles">All Categories</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="/articles/category/sustainable-practices">Sustainable Practices</a></li>
          <li><a class="dropdown-item" href="/articles/category/pest-control">Pest Control</a></li>
          <li><a class="dropdown-item" href="/articles/category/soil-health">Soil Health</a></li>
          <li><a class="dropdown-item" href="/articles/category/water-conservation">Water Conservation</a></li>
          <li><a class="dropdown-item" href="/articles/category/crop-rotation">Crop Rotation</a></li>
          <li><a class="dropdown-item" href="/articles/category/other">Other</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <% if (articles && articles.length > 0) { %>
    <% articles.forEach(article => { %>
      <div class="col-md-6 mb-4">
        <div class="card h-100 shadow-sm">
          <div class="row g-0">
            <div class="col-md-4">
              <% if (article.imageUrl) { %>
                <img src="<%= article.imageUrl %>" class="img-fluid rounded-start h-100" alt="<%= article.title %>" style="object-fit: cover;">
              <% } else { %>
                <div class="bg-light d-flex align-items-center justify-content-center h-100 rounded-start">
                  <i class="bi bi-file-text text-muted" style="font-size: 3rem;"></i>
                </div>
              <% } %>
            </div>
            <div class="col-md-8">
              <div class="card-body d-flex flex-column h-100">
                <h5 class="card-title"><%= article.title %></h5>
                <p class="card-text text-muted small">
                  <span class="ms-2"><i class="bi bi-calendar"></i> <%= new Date(article.createdAt).toLocaleDateString() %></span>
                </p>
                <p class="card-text flex-grow-1"><%= article.summary %></p>
                <div class="d-flex justify-content-between align-items-center mt-2">
                  <small class="text-muted">
                    By <%= article.userName %>
                  </small>
                  <a href="/articles/<%= article.id %>" class="btn btn-sm btn-outline-success">Read More</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <div class="col-md-12">
      <div class="alert alert-info" role="alert">
        No articles available in this category yet. Be the first to share your knowledge!
      </div>
    </div>
  <% } %>
</div>

<div class="row mt-4">
  <div class="col-md-12">
    <a href="/articles" class="btn btn-outline-secondary">
      <i class="bi bi-arrow-left"></i> Back to All Articles
    </a>
  </div>
</div>
