/* Facebook-like Content Styles */

.news-feed {
  width: 100%;
  margin: 0 auto;
}

.news-feed-item {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.news-feed-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Sticky navigation */
.sticky-top {
  position: sticky;
  top: 20px;
  z-index: 100;
}

.card-title {
  font-weight: 600;
  color: #2c3e50;
}

.card-text {
  color: #34495e;
}

/* Interaction icons styling */
.interaction-icons {
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 0;
}

.interaction-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.interaction-count {
  position: absolute;
  top: -15px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #6c757d;
}

.interaction-icon {
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 5px;
}

.interaction-icon:hover {
  transform: scale(1.2);
}

.like-icon.active {
  color: #e74c3c;
}

/* Loading indicator */
.loading-indicator {
  text-align: center;
  padding: 2rem 0;
  display: none;
}

.loading-indicator.active {
  display: block;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .news-feed {
    max-width: 100%;
  }
}
