/**
 * Toast Notification System
 * A simple system for showing toast notifications
 */

class ToastNotification {
  constructor() {
    this.container = null;
    this.defaultDuration = 5000; // 5 seconds
    this.initialize();
  }

  /**
   * Initialize the toast container
   */
  initialize() {
    // Create container if it doesn't exist
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.className = 'toast-container';
      document.body.appendChild(this.container);
    }
  }

  /**
   * Show a toast notification
   * @param {Object} options - Toast options
   * @param {string} options.title - Toast title
   * @param {string} options.message - Toast message
   * @param {string} options.type - Toast type (success, error, info, warning)
   * @param {number} options.duration - Duration in milliseconds
   * @param {boolean} options.closable - Whether the toast can be closed
   * @param {Function} options.onClose - Callback when toast is closed
   */
  show(options) {
    const {
      title = '',
      message = '',
      type = 'info',
      duration = this.defaultDuration,
      closable = true,
      onClose = null
    } = options;

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    // Set icon based on type
    let icon = '';
    switch (type) {
      case 'success':
        icon = 'bi-check-circle-fill';
        break;
      case 'error':
        icon = 'bi-exclamation-circle-fill';
        break;
      case 'warning':
        icon = 'bi-exclamation-triangle-fill';
        break;
      case 'info':
      default:
        icon = 'bi-info-circle-fill';
        break;
    }

    // Create toast content
    toast.innerHTML = `
      <div class="toast-icon">
        <i class="bi ${icon}"></i>
      </div>
      <div class="toast-content">
        ${title ? `<div class="toast-title">${title}</div>` : ''}
        ${message ? `<p class="toast-message">${message}</p>` : ''}
      </div>
      ${closable ? `<button class="toast-close"><i class="bi bi-x"></i></button>` : ''}
      <div class="toast-progress">
        <div class="toast-progress-bar"></div>
      </div>
    `;

    // Add to container
    this.container.appendChild(toast);

    // Animate progress bar
    const progressBar = toast.querySelector('.toast-progress-bar');
    progressBar.style.animation = `progress ${duration / 1000}s linear forwards`;

    // Show toast with animation
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    // Set up close button
    if (closable) {
      const closeButton = toast.querySelector('.toast-close');
      closeButton.addEventListener('click', () => {
        this.close(toast, onClose);
      });
    }

    // Auto close after duration
    const timeoutId = setTimeout(() => {
      this.close(toast, onClose);
    }, duration);

    // Store timeout ID on the toast element
    toast.dataset.timeoutId = timeoutId;

    // Return the toast element
    return toast;
  }

  /**
   * Close a toast notification
   * @param {HTMLElement} toast - Toast element
   * @param {Function} callback - Callback function
   */
  close(toast, callback = null) {
    // Clear timeout
    if (toast.dataset.timeoutId) {
      clearTimeout(parseInt(toast.dataset.timeoutId));
    }

    // Hide toast
    toast.classList.add('hide');
    toast.classList.remove('show');

    // Remove after animation
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
      if (typeof callback === 'function') {
        callback();
      }
    }, 300);
  }

  /**
   * Show a success toast
   */
  success(message, title = 'Success', options = {}) {
    return this.show({
      title,
      message,
      type: 'success',
      ...options
    });
  }

  /**
   * Show an error toast
   */
  error(message, title = 'Error', options = {}) {
    return this.show({
      title,
      message,
      type: 'error',
      ...options
    });
  }

  /**
   * Show an info toast
   */
  info(message, title = 'Information', options = {}) {
    return this.show({
      title,
      message,
      type: 'info',
      ...options
    });
  }

  /**
   * Show a warning toast
   */
  warning(message, title = 'Warning', options = {}) {
    return this.show({
      title,
      message,
      type: 'warning',
      ...options
    });
  }
}

// Create global toast instance
const toast = new ToastNotification();
