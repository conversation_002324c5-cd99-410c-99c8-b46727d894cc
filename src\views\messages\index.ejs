<!-- Header with title -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1>Messages</h1>
      <% if (user && user.displayName) { %>
        <div class="greeting-container" style="text-align: right;">
          <div id="greeting-text">
            <span class="time-greeting">Good day</span><span class="me-2">,</span>
            <span class="fw-bold"><%= user.displayName %></span>
          </div>
        </div>
      <% } %>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
  </div>
</div>

<!-- Main content with left navigation and messages -->
<div class="row">
  <!-- Left Side Navigation -->
  <div class="col-md-3 mb-4">
    <div class="dashboard-nav">
      <ul class="nav flex-column">
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/dashboard">
            <i class="bi bi-house-door me-2"></i> Dashboard
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses">
            <i class="bi bi-book me-2"></i> Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/resources">
            <i class="bi bi-file-earmark-text me-2"></i> Resources
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses/my-courses">
            <i class="bi bi-journal-check me-2"></i> My Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/profile">
            <i class="bi bi-person-circle me-2"></i> Profile
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/new">
            <i class="bi bi-cloud-upload me-2"></i> Upload Content
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/my-uploads">
            <i class="bi bi-collection me-2"></i> My Uploads
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/weather">
            <i class="bi bi-cloud-sun me-2"></i> Weather
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/connections">
            <i class="bi bi-people-fill me-2"></i> Farmer Network
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link active" href="/messages">
            <i class="bi bi-chat-dots-fill me-2"></i> Messages
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/auth/logout">
            <i class="bi bi-box-arrow-right me-2"></i> Logout
          </a>
        </li>
      </ul>
    </div>
  </div>

    <!-- Main content -->
    <div class="col-md-9">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">Messages</h4>
          <a href="/connections" class="btn btn-light btn-sm">
            <i class="bi bi-people-fill me-1"></i> My Connections
          </a>
        </div>
        <div class="card-body p-0">
          <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger m-3" role="alert">
              <%= error %>
            </div>
          <% } %>

          <div class="row g-0">
            <!-- Conversations list -->
            <div class="col-md-4 border-end">
              <div class="conversations-header p-3 border-bottom">
                <h5 class="mb-0">Conversations</h5>
              </div>

              <div class="conversations-list">
                <% if (!conversations || conversations.length === 0) { %>
                  <div class="text-center py-5">
                    <i class="bi bi-chat-dots text-muted" style="font-size: 2rem;"></i>
                    <p class="mt-3 text-muted">No conversations yet</p>
                    <a href="/connections" class="btn btn-success btn-sm mt-2">
                      <i class="bi bi-people-fill me-1"></i> Find Connections
                    </a>
                  </div>
                <% } else { %>
                  <% conversations.forEach(conversation => { %>
                    <a href="/messages/conversation/<%= conversation.otherUser.uid %>" class="conversation-item d-flex align-items-center p-3 border-bottom text-decoration-none text-dark">
                      <div class="me-3 position-relative">
                        <% if (conversation.otherUser.photoURL) { %>
                          <img src="<%= conversation.otherUser.photoURL %>" alt="<%= conversation.otherUser.displayName %>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                        <% } else { %>
                          <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="bi bi-person-fill text-secondary" style="font-size: 1.5rem;"></i>
                          </div>
                        <% } %>

                        <!-- Online indicator (placeholder) -->
                        <span class="position-absolute bottom-0 end-0 bg-success rounded-circle" style="width: 12px; height: 12px; border: 2px solid white;"></span>
                      </div>
                      <div class="flex-grow-1 min-width-0">
                        <div class="d-flex justify-content-between align-items-center">
                          <h6 class="mb-0 text-truncate"><%= conversation.otherUser.displayName %></h6>
                          <small class="text-muted ms-2">
                            <%= conversation.lastMessageAt ? new Date(conversation.lastMessageAt.seconds * 1000).toLocaleDateString() : 'New' %>
                          </small>
                        </div>
                        <p class="mb-0 small text-truncate text-muted">
                          <% if (conversation.lastMessage && conversation.lastMessage.text) { %>
                            <% if (conversation.lastMessage.senderId === user.uid) { %>
                              <span class="text-muted">You: </span>
                            <% } %>
                            <%= conversation.lastMessage.text %>
                          <% } else { %>
                            Start a conversation
                          <% } %>
                        </p>
                      </div>
                    </a>
                  <% }); %>
                <% } %>
              </div>
            </div>

            <!-- Message content area -->
            <div class="col-md-8">
              <div class="text-center py-5">
                <i class="bi bi-chat-square-text text-muted" style="font-size: 4rem;"></i>
                <h5 class="mt-3">Select a conversation</h5>
                <p class="text-muted">Choose a conversation from the list or start a new one</p>
                <a href="/connections" class="btn btn-success mt-2">
                  <i class="bi bi-person-plus-fill me-1"></i> Connect with Farmers
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize encryption
    fetch('/messages/init-encryption')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.newKeysGenerated) {
          console.log('New encryption keys generated successfully');
        }
      })
      .catch(error => {
        console.error('Error initializing encryption:', error);
      });
  });
</script>

<link rel="stylesheet" href="/css/messages.css">
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize time-based greeting
    updateGreeting();

    // Debug information
    console.log('Messages page loaded');
    console.log('User:', <%= JSON.stringify(user || {}) %>);
    console.log('User data:', <%= JSON.stringify(userData || {}) %>);
    console.log('Conversations:', <%= JSON.stringify(conversations || []) %>);

    // Initialize encryption
    console.log('Initializing encryption...');
    fetch('/messages/init-encryption')
      .then(response => response.json())
      .then(data => {
        console.log('Encryption initialization response:', data);
        if (data.success && data.newKeysGenerated) {
          console.log('New encryption keys generated successfully');
        }
      })
      .catch(error => {
        console.error('Error initializing encryption:', error);
      });
  });

  function updateGreeting() {
    const greetingElement = document.querySelector('.time-greeting');
    if (!greetingElement) return;

    const hour = new Date().getHours();
    let greeting = 'Good day';

    if (hour < 12) {
      greeting = 'Good morning';
    } else if (hour < 18) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }

    greetingElement.textContent = greeting;
  }
</script>
