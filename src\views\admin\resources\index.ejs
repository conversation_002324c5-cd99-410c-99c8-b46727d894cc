<div class="container">
  <div class="row">
    <div class="col-md-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Manage Resources</h1>
        <a href="/admin/resources/new" class="btn btn-success">
          <i class="bi bi-plus-circle"></i> Add New Resource
        </a>
      </div>
      
      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>
      
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h4 class="mb-0">Resources</h4>
        </div>
        <div class="card-body">
          <% if (resources && resources.length > 0) { %>
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Category</th>
                    <th>Created</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% resources.forEach(resource => { %>
                    <tr>
                      <td><%= resource.title %></td>
                      <td><span class="badge bg-secondary"><%= resource.category %></span></td>
                      <td><%= resource.createdAt ? new Date(resource.createdAt.seconds * 1000).toLocaleDateString() : 'N/A' %></td>
                      <td>
                        <div class="form-check form-switch">
                          <input class="form-check-input toggle-published" 
                                type="checkbox" 
                                id="publishedSwitch-<%= resource.id %>" 
                                <%= resource.isPublished ? 'checked' : '' %>
                                data-resource-id="<%= resource.id %>">
                          <label class="form-check-label" for="publishedSwitch-<%= resource.id %>">
                            <span class="badge bg-<%= resource.isPublished ? 'success' : 'secondary' %>">
                              <%= resource.isPublished ? 'Published' : 'Draft' %>
                            </span>
                          </label>
                        </div>
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <a href="/resources/<%= resource.id %>" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="bi bi-eye"></i>
                          </a>
                          <a href="/admin/resources/<%= resource.id %>/edit" class="btn btn-sm btn-outline-success">
                            <i class="bi bi-pencil"></i>
                          </a>
                          <button type="button" class="btn btn-sm btn-outline-danger delete-resource" data-resource-id="<%= resource.id %>" data-resource-title="<%= resource.title %>">
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          <% } else { %>
            <div class="alert alert-info" role="alert">
              No resources found. Click the "Add New Resource" button to create your first resource.
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteResourceModal" tabindex="-1" aria-labelledby="deleteResourceModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="deleteResourceModalLabel">Confirm Delete</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the resource "<span id="resourceTitle"></span>"?</p>
        <p class="text-danger"><strong>This action cannot be undone.</strong></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form id="deleteResourceForm" method="POST">
          <button type="submit" class="btn btn-danger">Delete Resource</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle delete button clicks
    const deleteButtons = document.querySelectorAll('.delete-resource');
    const deleteForm = document.getElementById('deleteResourceForm');
    const resourceTitleSpan = document.getElementById('resourceTitle');
    
    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const resourceId = this.getAttribute('data-resource-id');
        const resourceTitle = this.getAttribute('data-resource-title');
        
        deleteForm.action = `/admin/resources/${resourceId}/delete`;
        resourceTitleSpan.textContent = resourceTitle;
        
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteResourceModal'));
        deleteModal.show();
      });
    });
    
    // Handle toggle published status
    const toggleSwitches = document.querySelectorAll('.toggle-published');
    
    toggleSwitches.forEach(toggle => {
      toggle.addEventListener('change', function() {
        const resourceId = this.getAttribute('data-resource-id');
        const isChecked = this.checked;
        
        // Update the label immediately for better UX
        const label = this.nextElementSibling.querySelector('.badge');
        if (isChecked) {
          label.classList.remove('bg-secondary');
          label.classList.add('bg-success');
          label.textContent = 'Published';
        } else {
          label.classList.remove('bg-success');
          label.classList.add('bg-secondary');
          label.textContent = 'Draft';
        }
        
        // Send request to server
        fetch(`/admin/resources/${resourceId}/toggle-published`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (!data.success) {
            // If there was an error, revert the UI
            this.checked = !isChecked;
            if (!isChecked) {
              label.classList.remove('bg-secondary');
              label.classList.add('bg-success');
              label.textContent = 'Published';
            } else {
              label.classList.remove('bg-success');
              label.classList.add('bg-secondary');
              label.textContent = 'Draft';
            }
            alert('Error toggling published status: ' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          // Revert the UI on error
          this.checked = !isChecked;
          if (!isChecked) {
            label.classList.remove('bg-secondary');
            label.classList.add('bg-success');
            label.textContent = 'Published';
          } else {
            label.classList.remove('bg-success');
            label.classList.add('bg-secondary');
            label.textContent = 'Draft';
          }
          alert('Error toggling published status. Please try again.');
        });
      });
    });
  });
</script>
