/**
 * Mobile-friendly image upload helper
 * Allows users to:
 * 1. Enter an image URL directly
 * 2. Take a photo with their device camera (mobile)
 * 3. Select an image from their device
 */
document.addEventListener('DOMContentLoaded', function() {
  // Find all image upload containers
  const imageUploadContainers = document.querySelectorAll('.image-upload-container');
  
  imageUploadContainers.forEach(container => {
    const urlInput = container.querySelector('.image-url-input');
    const previewContainer = container.querySelector('.image-preview-container');
    const previewImage = container.querySelector('.image-preview');
    const takePhotoBtn = container.querySelector('.take-photo-btn');
    const selectImageBtn = container.querySelector('.select-image-btn');
    const fileInput = container.querySelector('.file-input');
    const clearBtn = container.querySelector('.clear-image-btn');
    
    // Function to update the preview
    const updatePreview = (url) => {
      if (url && url.trim() !== '') {
        previewImage.src = url;
        previewContainer.classList.remove('d-none');
        clearBtn.classList.remove('d-none');
      } else {
        previewContainer.classList.add('d-none');
        clearBtn.classList.add('d-none');
      }
    };
    
    // Initialize preview if URL exists
    if (urlInput.value) {
      updatePreview(urlInput.value);
    }
    
    // Update preview when URL changes
    urlInput.addEventListener('input', function() {
      updatePreview(this.value);
    });
    
    // Clear image
    clearBtn.addEventListener('click', function() {
      urlInput.value = '';
      previewContainer.classList.add('d-none');
      clearBtn.classList.add('d-none');
    });
    
    // Handle file selection
    if (fileInput) {
      fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
          const reader = new FileReader();
          
          reader.onload = function(e) {
            // For demo purposes, we'll just set the image URL to the data URL
            // In a real app, you would upload this to a server and get back a URL
            urlInput.value = e.target.result;
            updatePreview(e.target.result);
          };
          
          reader.readAsDataURL(this.files[0]);
        }
      });
      
      // Trigger file input when select image button is clicked
      if (selectImageBtn) {
        selectImageBtn.addEventListener('click', function() {
          fileInput.click();
        });
      }
      
      // Handle take photo button (mobile devices)
      if (takePhotoBtn) {
        // Only show the take photo button if the device has a camera
        if ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices) {
          takePhotoBtn.classList.remove('d-none');
          
          takePhotoBtn.addEventListener('click', function() {
            // Set the accept attribute to only accept images from camera
            fileInput.setAttribute('capture', 'environment');
            fileInput.click();
          });
        } else {
          takePhotoBtn.classList.add('d-none');
        }
      }
    }
  });
});
