/* WhatsApp Web Style Messaging */

:root {
  /* WhatsApp Colors - Using app theme variables where appropriate */
  --wa-teal: var(--theme-primary, #4CAF50);
  --wa-light-green: #dcf8c6;
  --wa-outgoing-background: #d9fdd3;
  --wa-incoming-background: #ffffff;
  --wa-chat-background: #efeae2;
  --wa-panel-background: var(--theme-background, #f0f2f5);
  --wa-header-background: var(--theme-background, #f0f2f5);
  --wa-border-color: #d1d7db;
  --wa-icon-color: #54656f;
  --wa-text-primary: var(--theme-text-dark, #333333);
  --wa-text-secondary: var(--theme-text-muted, #666666);
  --wa-hover-background: var(--theme-hover, rgba(76, 175, 80, 0.1));
  --wa-active-background: rgba(76, 175, 80, 0.15);
  --wa-notification-background: var(--theme-primary, #4CAF50);
  --wa-message-time: #8696a0;
  --wa-chat-pattern: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhL1ZVBboJAFIZnwD2Jey/gBdp4Aj2C9gjeQHsEvUFP0BvoCVpv0H2XkBTO0P9lHiQgKEGT/skXmHn/e+9/M8DTYrHQURSdiKIVHu5WHMcn+kTr9boTBMGJ7zMiRjUoiiIvTdM3KPAgW3xUVdWJ7/vEt5HQv/A877xer6dw9QzFfFjNIAhOWZYNKdz3fZPWtu0jiuNYwzCtgiC4wHmTpqqqZ9d1I8uyhF3XNSzLEsD3QRiGMVzfibPZTMPIc6qqEsuyRJZlgpBpmgLOHQjOc7gMGYvH45FGlmWJPM8FTdOErusCjiVJIpIkEXEci/F4LKbTqYYHF7ZtC8dxxGg0EsPhUAyHQ+E4jrAsS1iWJVzXNXzfN+FUTKdTD3gYhoKQpmli7zgOGY1arVbPeZ4PKKC/mWh7sVjIVV3XA1VVA0mSAlEUA1mWA0VRgiRJAkL6/b6GgzudTgdE+b7/jPdEkRWUZbnD+IWqbt5EFEUCzh2ML7B/w7yN+CfBjB5w/gJ3D5sYCgLHV8EFcQQjEuyfcL6ZwLXl7CUMjWgFYqEsy3K73W6Dh7vdbvdBn2j1C5KSJRCLn8wgAAAAAElFTkSuQmCC");
}

/* Main Layout */
.messaging-container {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background-color: var(--wa-panel-background);
  position: relative;
}

/* Conversation List Panel */
.conversation-panel {
  height: 100vh;
  border-right: 3px solid var(--theme-primary, #4CAF50); /* Theme green border */
  background-color: #f0f2f5; /* Light gray background */
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
  z-index: 10; /* Ensure it's above the chat panel */
  position: relative;
}

.conversation-panel-header {
  padding: 10px 16px;
  background-color: var(--theme-primary, #4CAF50); /* Theme green */
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid var(--wa-border-color);
  color: var(--theme-text-light, white);
}

.conversation-panel-header .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.conversation-panel-header .header-icons {
  display: flex;
  gap: 24px;
}

.conversation-panel-header .header-icon {
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.9;
  transition: all 0.2s ease;
}

.conversation-panel-header .header-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

.search-container {
  padding: 8px 12px;
  background-color: var(--wa-panel-background);
}

.search-box {
  background-color: white;
  border-radius: 8px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  border: 1px solid var(--wa-border-color);
}

.search-box input {
  border: none;
  outline: none;
  width: 100%;
  margin-left: 8px;
  font-size: 14px;
}

.search-box .search-icon {
  color: var(--wa-icon-color);
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  background-color: white;
  border-top: 1px solid var(--wa-border-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) inset;
}

.conversation-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--wa-border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden; /* For ripple effect */
}

.conversation-item:hover {
  background-color: var(--wa-hover-background);
}

.conversation-item.active {
  background-color: #e9edef; /* Slightly different from hover */
  border-left: 4px solid var(--theme-primary, #4CAF50); /* Theme green accent */
  padding-left: 12px; /* Adjust padding to account for border */
}

.conversation-avatar {
  width: 49px;
  height: 49px;
  border-radius: 50%;
  margin-right: 15px;
  flex-shrink: 0;
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.conversation-name {
  font-weight: 500;
  color: var(--wa-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 12px;
  color: var(--wa-text-secondary);
  white-space: nowrap;
}

.conversation-message {
  display: flex;
  align-items: center;
  color: var(--wa-text-secondary);
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-message-status {
  margin-right: 4px;
  color: var(--wa-icon-color);
}

.conversation-message-preview {
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-unread {
  background-color: var(--wa-notification-background);
  color: white;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

/* Chat Panel */
.chat-panel {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #e5ded8; /* Slightly darker than default WhatsApp background */
  background-image: var(--wa-chat-pattern);
  background-repeat: repeat;
  position: relative;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05) inset; /* Inner shadow for depth */
  border-left: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border */
}

.chat-header {
  padding: 10px 16px;
  background-color: var(--theme-primary, #4CAF50); /* Theme green */
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid var(--wa-border-color);
  z-index: 10;
  color: var(--theme-text-light, white);
}

.chat-header-info {
  display: flex;
  align-items: center;
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 15px;
}

.chat-name {
  font-weight: 500;
  color: white;
}

.chat-status {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.chat-header-actions {
  display: flex;
  gap: 24px;
}

.chat-header-icon {
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.9;
  transition: all 0.2s ease;
}

.chat-header-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.message {
  max-width: 65%;
  margin-bottom: 8px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.message.outgoing {
  align-self: flex-end;
}

.message.incoming {
  align-self: flex-start;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  position: relative;
}

.message.outgoing .message-bubble {
  background-color: var(--wa-outgoing-background);
  border-top-right-radius: 0;
}

.message.incoming .message-bubble {
  background-color: var(--wa-incoming-background);
  border-top-left-radius: 0;
}

.message-text {
  font-size: 14px;
  line-height: 19px;
  color: var(--wa-text-primary);
  word-wrap: break-word;
}

.message-time {
  font-size: 11px;
  color: var(--wa-message-time);
  margin-left: 4px;
  display: inline-block;
  margin-top: 2px;
  align-self: flex-end;
}

.message-status {
  margin-left: 4px;
  font-size: 14px;
}

.message-status.sent {
  color: #a5a5a5;
}

.message-status.delivered {
  color: #a5a5a5;
}

.message-status.read {
  color: #53bdeb;
}

.chat-footer {
  padding: 10px 16px;
  background-color: #f0f2f5; /* Light gray background */
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.08);
}

.chat-footer-icon {
  color: var(--wa-icon-color);
  font-size: 1.5rem;
  cursor: pointer;
}

.message-input-container {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  padding: 9px 12px;
  display: flex;
  align-items: center;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 15px;
  min-height: 20px;
  max-height: 100px;
  overflow-y: auto;
}

.message-input::placeholder {
  color: var(--wa-text-secondary);
}

.send-button {
  background-color: transparent;
  border: none;
  color: var(--wa-teal);
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 8px;
}

/* Empty Chat State */
.empty-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--wa-panel-background);
  padding: 20px;
  text-align: center;
}

.empty-chat-icon {
  width: 250px;
  height: 250px;
  margin-bottom: 40px;
  opacity: 0.5;
}

.empty-chat-title {
  font-size: 32px;
  font-weight: 300;
  color: var(--wa-text-primary);
  margin-bottom: 16px;
}

.empty-chat-subtitle {
  font-size: 14px;
  color: var(--wa-text-secondary);
  max-width: 500px;
  line-height: 20px;
  margin-bottom: 32px;
}

/* New Message Modal */
.new-message-modal .modal-content {
  border-radius: 3px;
  border: none;
}

.new-message-modal .modal-header {
  background-color: var(--theme-primary, #4CAF50);
  color: var(--theme-text-light, white);
  border-bottom: none;
  padding: 20px 24px;
}

.new-message-modal .modal-title {
  font-size: 19px;
  font-weight: 500;
}

.new-message-modal .modal-body {
  padding: 20px 24px;
}

.contact-search {
  margin-bottom: 20px;
}

.contact-list {
  max-height: 400px;
  overflow-y: auto;
}

.contact-item {
  padding: 12px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--wa-border-color);
  cursor: pointer;
  transition: background-color 0.2s;
}

.contact-item:hover {
  background-color: var(--wa-hover-background);
}

.contact-avatar {
  width: 49px;
  height: 49px;
  border-radius: 50%;
  margin-right: 15px;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-weight: 500;
  color: var(--wa-text-primary);
}

.contact-status {
  font-size: 13px;
  color: var(--wa-text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

@keyframes blink {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.message {
  animation: fadeIn 0.3s ease-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-out forwards;
}

/* Ripple Effect */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

/* Typing Indicator */
.typing-dots {
  display: flex;
  align-items: center;
  height: 20px;
}

.typing-dots span {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--wa-text-secondary);
  margin-right: 4px;
  animation: blink 1.4s infinite both;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

/* Emoji Picker */
.emoji-picker {
  position: absolute;
  bottom: 70px;
  left: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  z-index: 100;
  opacity: 0;
  transform: translateY(10px);
  pointer-events: none;
  transition: all 0.2s ease;
}

.emoji-picker.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.emoji-picker span {
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.emoji-picker span:hover {
  background-color: var(--wa-hover-background);
}

/* Send Button Animation */
.send-button {
  transform: scale(0.8);
  opacity: 0.5;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.send-button.active {
  transform: scale(1);
  opacity: 1;
}

/* Scroll Shadow Effect */
.messages-container.scrolled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 15px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
  pointer-events: none;
  z-index: 5;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .conversation-panel {
    position: absolute;
    width: 100%;
    z-index: 100;
    height: 100vh;
    transform: translateX(0);
    transition: transform 0.3s ease;
  }

  .conversation-panel.hidden {
    transform: translateX(-100%);
  }

  .chat-panel {
    width: 100%;
  }

  .chat-back-button {
    display: block !important;
  }
}
