<%- include('../partials/header') %>

<!-- Agricultural-themed Network CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="linkedin-container">
  <div class="container">
    <div class="row">
      <!-- Left Sidebar -->
      <div class="col-lg-3">
        <!-- User Profile Card -->
        <div class="linkedin-card profile-card">
          <div class="profile-background"></div>
          <% if (userData && userData.photoURL) { %>
            <img src="<%= userData.photoURL %>" class="profile-photo">
          <% } else { %>
            <div class="profile-photo d-flex align-items-center justify-content-center">
              <i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>
            </div>
          <% } %>
          <h5 class="profile-name"><%= user.displayName || 'User' %></h5>
          <p class="profile-headline">Sustainable Farmer</p>
          <a href="/network/profile/<%= user.uid %>" class="linkedin-btn linkedin-btn-outline w-100">View Profile</a>
        </div>

        <!-- Manage My Network -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">Manage my network</h5>
          </div>
          <div class="p-0">
            <a href="/network/connections" class="connection-card text-decoration-none active">
              <i class="bi bi-people-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-primary);">Connections</span>
                <span class="linkedin-badge linkedin-badge-primary">
                  <%= connections.length %>
                </span>
              </div>
            </a>

            <a href="/network/search" class="connection-card text-decoration-none">
              <i class="bi bi-person-plus-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Find Farmers</span>
              </div>
            </a>

            <a href="/network" class="connection-card text-decoration-none">
              <i class="bi bi-house-door-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Home</span>
              </div>
            </a>

            <a href="/messaging" class="connection-card text-decoration-none">
              <i class="bi bi-chat-dots-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Messages</span>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="col-lg-9">
        <!-- Connection Requests -->
        <div class="linkedin-card mb-4">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">Invitations</h5>
          </div>
          <div class="linkedin-tabs">
            <div class="linkedin-tab active" id="received-tab">Received</div>
            <div class="linkedin-tab" id="sent-tab">Sent</div>
          </div>

          <!-- Received Requests Tab Content -->
          <div id="received-requests" class="p-0">
            <% if (pendingRequests && pendingRequests.length > 0) { %>
              <%
                const incomingRequests = pendingRequests.filter(request => !request.isRequester);
                if (incomingRequests.length > 0) {
              %>
                <% incomingRequests.forEach(request => { %>
                  <div class="connection-card" data-connection-id="<%= request.id %>">
                    <% if (request.otherUser.photoURL) { %>
                      <img src="<%= request.otherUser.photoURL %>" class="connection-photo" alt="Profile photo">
                    <% } else { %>
                      <div class="connection-photo d-flex align-items-center justify-content-center">
                        <i class="bi bi-person-fill text-secondary"></i>
                      </div>
                    <% } %>
                    <div class="connection-info">
                      <h6 class="connection-name"><%= request.otherUser.displayName || 'User' %></h6>
                      <p class="connection-headline"><%= request.otherUser.headline || 'Sustainable Farmer' %></p>
                      <div class="connection-actions">
                        <button class="linkedin-btn linkedin-btn-primary accept-request-btn" data-id="<%= request.id %>">Accept</button>
                        <button class="linkedin-btn linkedin-btn-outline reject-request-btn" data-id="<%= request.id %>">Ignore</button>
                      </div>
                    </div>
                  </div>
                <% }); %>
              <% } else { %>
                <div class="p-4 text-center text-muted">No pending invitations</div>
              <% } %>
            <% } else { %>
              <div class="p-4 text-center text-muted">No pending invitations</div>
            <% } %>
          </div>

          <!-- Sent Requests Tab Content (Hidden by default) -->
          <div id="sent-requests" class="p-0" style="display: none;">
            <% if (pendingRequests && pendingRequests.length > 0) { %>
              <%
                const outgoingRequests = pendingRequests.filter(request => request.isRequester);
                if (outgoingRequests.length > 0) {
              %>
                <% outgoingRequests.forEach(request => { %>
                  <div class="connection-card">
                    <% if (request.otherUser.photoURL) { %>
                      <img src="<%= request.otherUser.photoURL %>" class="connection-photo" alt="Profile photo">
                    <% } else { %>
                      <div class="connection-photo d-flex align-items-center justify-content-center">
                        <i class="bi bi-person-fill text-secondary"></i>
                      </div>
                    <% } %>
                    <div class="connection-info">
                      <h6 class="connection-name"><%= request.otherUser.displayName || 'User' %></h6>
                      <p class="connection-headline"><%= request.otherUser.headline || 'Sustainable Farmer' %></p>
                      <div class="connection-actions">
                        <span class="linkedin-badge">Pending</span>
                        <button class="linkedin-btn linkedin-btn-text withdraw-request-btn" data-id="<%= request.id %>">Withdraw</button>
                      </div>
                    </div>
                  </div>
                <% }); %>
              <% } else { %>
                <div class="p-4 text-center text-muted">No sent invitations</div>
              <% } %>
            <% } else { %>
              <div class="p-4 text-center text-muted">No sent invitations</div>
            <% } %>
          </div>
        </div>

        <!-- My Connections -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">My Connections (<%= connections.length %>)</h5>
            <% if (connections && connections.length > 0) { %>
              <div class="d-flex align-items-center">
                <div class="input-group">
                  <input type="text" class="form-control form-control-sm" id="connection-search" placeholder="Search by name">
                  <button class="btn btn-sm btn-outline-secondary" type="button">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
              </div>
            <% } %>
          </div>
          <div class="p-0">
            <% if (connections && connections.length > 0) { %>
              <% connections.forEach(connection => { %>
                <div class="connection-card">
                  <% if (connection.otherUser.photoURL) { %>
                    <img src="<%= connection.otherUser.photoURL %>" class="connection-photo" alt="Profile photo">
                  <% } else { %>
                    <div class="connection-photo d-flex align-items-center justify-content-center">
                      <i class="bi bi-person-fill text-secondary"></i>
                    </div>
                  <% } %>
                  <div class="connection-info">
                    <h6 class="connection-name"><%= connection.otherUser.displayName || 'User' %></h6>
                    <p class="connection-headline"><%= connection.otherUser.headline || 'Sustainable Farmer' %></p>
                    <% if (connection.otherUser.location) { %>
                      <div class="connection-location">
                        <i class="bi bi-geo-alt-fill"></i> <%= connection.otherUser.location %>
                      </div>
                    <% } %>
                    <div class="connection-actions">
                      <a href="/network/profile/<%= connection.otherUser.uid %>" class="linkedin-btn linkedin-btn-outline">View Profile</a>
                      <a href="/messaging/<%= connection.otherUser.uid %>" class="linkedin-btn linkedin-btn-primary">Message</a>
                    </div>
                  </div>
                </div>
              <% }); %>
            <% } else { %>
              <div class="text-center py-5">
                <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3">No Connections Yet</h5>
                <p class="text-muted">Start connecting with other farmers to grow your network.</p>
                <a href="/network/search" class="linkedin-btn linkedin-btn-primary">Find Farmers</a>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/toast-notifications.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const receivedTab = document.getElementById('received-tab');
    const sentTab = document.getElementById('sent-tab');
    const receivedRequests = document.getElementById('received-requests');
    const sentRequests = document.getElementById('sent-requests');

    receivedTab.addEventListener('click', function() {
      receivedTab.classList.add('active');
      sentTab.classList.remove('active');
      receivedRequests.style.display = 'block';
      sentRequests.style.display = 'none';
    });

    sentTab.addEventListener('click', function() {
      sentTab.classList.add('active');
      receivedTab.classList.remove('active');
      sentRequests.style.display = 'block';
      receivedRequests.style.display = 'none';
    });

    // Connection search functionality
    const searchInput = document.getElementById('connection-search');
    if (searchInput) {
      searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        const connectionCards = document.querySelectorAll('.linkedin-card:last-child .connection-card');

        connectionCards.forEach(card => {
          const name = card.querySelector('.connection-name').textContent.toLowerCase();
          const headline = card.querySelector('.connection-headline').textContent.toLowerCase();

          if (name.includes(searchTerm) || headline.includes(searchTerm)) {
            card.style.display = 'flex';
          } else {
            card.style.display = 'none';
          }
        });
      });
    }

    // Accept request button functionality
    document.addEventListener('click', function(event) {
      if (event.target.classList.contains('accept-request-btn')) {
        const connectionId = event.target.getAttribute('data-id');
        acceptConnectionRequest(connectionId, event.target);
      }

      if (event.target.classList.contains('reject-request-btn')) {
        const connectionId = event.target.getAttribute('data-id');
        rejectConnectionRequest(connectionId, event.target);
      }

      if (event.target.classList.contains('withdraw-request-btn')) {
        const connectionId = event.target.getAttribute('data-id');
        withdrawConnectionRequest(connectionId, event.target);
      }
    });
  });

  // Function to accept a connection request
  function acceptConnectionRequest(connectionId, buttonElement) {
    // Disable buttons to prevent multiple clicks
    const parentCard = buttonElement.closest('.connection-card');
    const buttons = parentCard.querySelectorAll('button');
    buttons.forEach(btn => btn.disabled = true);

    // Show loading state
    buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Accepting...';

    // Get user name if available
    let userName = 'this user';
    const nameElement = parentCard.querySelector('.connection-name');
    if (nameElement) {
      userName = nameElement.textContent;
    }

    fetch('/network/api/connections/accept', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update the UI
        parentCard.innerHTML = `
          <div class="p-3 text-center text-success">
            <i class="bi bi-check-circle-fill me-2"></i>
            Connection accepted!
          </div>
        `;

        // Show success toast notification
        toast.success(
          `You are now connected with ${userName}. You can now message each other and view each other's content.`,
          'Connection Accepted',
          { duration: 5000 }
        );

        // Fade out and remove after a delay
        setTimeout(() => {
          parentCard.style.opacity = '0';
          parentCard.style.transition = 'opacity 0.5s';
          setTimeout(() => {
            parentCard.remove();

            // Reload the page after a short delay to update the connections list
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }, 500);
        }, 1000);
      } else {
        // Re-enable buttons
        buttons.forEach(btn => btn.disabled = false);

        // Reset button text
        buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> Accept';

        // Show error toast
        toast.error(
          data.message || 'Failed to accept connection. Please try again.',
          'Error',
          { duration: 5000 }
        );
      }
    })
    .catch(error => {
      console.error('Error accepting connection request:', error);

      // Re-enable buttons
      buttons.forEach(btn => btn.disabled = false);

      // Reset button text
      buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> Accept';

      // Show error toast
      toast.error(
        'An error occurred while accepting the connection. Please try again.',
        'Error',
        { duration: 5000 }
      );
    });
  }

  // Function to reject a connection request
  function rejectConnectionRequest(connectionId, buttonElement) {
    // Disable buttons to prevent multiple clicks
    const parentCard = buttonElement.closest('.connection-card');
    const buttons = parentCard.querySelectorAll('button');
    buttons.forEach(btn => btn.disabled = true);

    // Show loading state
    buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Ignoring...';

    // Get user name if available
    let userName = 'this user';
    const nameElement = parentCard.querySelector('.connection-name');
    if (nameElement) {
      userName = nameElement.textContent;
    }

    fetch('/network/api/connections/reject', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show info toast notification
        toast.info(
          `You've ignored the invitation from ${userName}.`,
          'Invitation Ignored',
          { duration: 3000 }
        );

        // Fade out and remove
        parentCard.style.opacity = '0';
        parentCard.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          parentCard.remove();
        }, 500);
      } else {
        // Re-enable buttons
        buttons.forEach(btn => btn.disabled = false);

        // Reset button text
        buttonElement.innerHTML = '<i class="bi bi-x-lg me-1"></i> Ignore';

        // Show error toast
        toast.error(
          data.message || 'Failed to ignore invitation. Please try again.',
          'Error',
          { duration: 5000 }
        );
      }
    })
    .catch(error => {
      console.error('Error rejecting connection request:', error);

      // Re-enable buttons
      buttons.forEach(btn => btn.disabled = false);

      // Reset button text
      buttonElement.innerHTML = '<i class="bi bi-x-lg me-1"></i> Ignore';

      // Show error toast
      toast.error(
        'An error occurred while ignoring the invitation. Please try again.',
        'Error',
        { duration: 5000 }
      );
    });
  }

  // Function to withdraw a connection request
  function withdrawConnectionRequest(connectionId, buttonElement) {
    // Disable button to prevent multiple clicks
    buttonElement.disabled = true;

    // Show loading state
    const originalButtonText = buttonElement.innerHTML;
    buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Withdrawing...';

    // Get user name if available
    let userName = 'this user';
    const parentCard = buttonElement.closest('.connection-card');
    if (parentCard) {
      const nameElement = parentCard.querySelector('.connection-name');
      if (nameElement) {
        userName = nameElement.textContent;
      }
    }

    fetch('/network/api/connections/reject', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show info toast notification
        toast.info(
          `Your invitation to ${userName} has been withdrawn.`,
          'Invitation Withdrawn',
          { duration: 3000 }
        );

        // Fade out and remove the card
        parentCard.style.opacity = '0';
        parentCard.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          parentCard.remove();
        }, 500);
      } else {
        // Reset button
        buttonElement.innerHTML = originalButtonText;
        buttonElement.disabled = false;

        // Show error toast
        toast.error(
          data.message || 'Failed to withdraw invitation. Please try again.',
          'Error',
          { duration: 5000 }
        );
      }
    })
    .catch(error => {
      console.error('Error withdrawing connection request:', error);

      // Reset button
      buttonElement.innerHTML = originalButtonText;
      buttonElement.disabled = false;

      // Show error toast
      toast.error(
        'An error occurred while withdrawing the invitation. Please try again.',
        'Error',
        { duration: 5000 }
      );
    });
  }
</script>

<%- include('../partials/footer') %>
