/**
 * Export Service
 * Handles exporting market data to various formats
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Export market data to CSV format
 * @param {Array} data - Market data to export
 * @param {Object} options - Export options
 * @returns {string} CSV data
 */
export function exportToCSV(data, options = {}) {
  try {
    // Process data based on options
    const processedData = processDataForExport(data, options);

    // Define fields for CSV
    const fields = getFieldsForExport(processedData, options);

    // Convert to CSV manually
    let csv = '';

    // Add header row
    csv += fields.map(field => `"${field.label || field}"`).join(',') + '\n';

    // Add data rows
    processedData.forEach(item => {
      const row = fields.map(field => {
        const value = item[field.value || field];
        // Handle strings with commas by wrapping in quotes
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value}"`;
        }
        return value;
      }).join(',');

      csv += row + '\n';
    });

    return csv;
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    throw new Error('Failed to export data to CSV');
  }
}

/**
 * Export market data to JSON format
 * @param {Array} data - Market data to export
 * @param {Object} options - Export options
 * @returns {string} JSON data
 */
export function exportToJSON(data, options = {}) {
  try {
    // Process data based on options
    const processedData = processDataForExport(data, options);

    // Add metadata if requested
    if (options.includeMetadata) {
      const metadata = {
        exportDate: new Date().toISOString(),
        dataSource: 'Sustainable Farming Market Trends',
        filters: options.filters || {},
        recordCount: processedData.length
      };

      return JSON.stringify({
        metadata,
        data: processedData
      }, null, 2);
    }

    // Otherwise just return the data
    return JSON.stringify(processedData, null, 2);
  } catch (error) {
    console.error('Error exporting to JSON:', error);
    throw new Error('Failed to export data to JSON');
  }
}

/**
 * Export market data to Excel format (simplified version)
 * @param {Array} data - Market data to export
 * @param {Object} options - Export options
 * @returns {string} CSV data (as Excel alternative)
 */
export async function exportToExcel(data, options = {}) {
  try {
    // For now, just return CSV as Excel is not available
    return exportToCSV(data, options);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw new Error('Failed to export data to Excel');
  }
}

/**
 * Process data for export based on options
 * @param {Array} data - Market data to export
 * @param {Object} options - Export options
 * @returns {Array} Processed data
 */
function processDataForExport(data, options = {}) {
  // Clone data to avoid modifying the original
  let processedData = JSON.parse(JSON.stringify(data));

  // If chart data only, extract just the time series data
  if (options.chartDataOnly) {
    const chartData = [];

    processedData.forEach(item => {
      if (!item.timeSeriesData) return;

      item.timeSeriesData.forEach(point => {
        chartData.push({
          crop: item.crop,
          location: item.location,
          date: point.date,
          price: point.price
        });
      });
    });

    return chartData;
  }

  // Otherwise, flatten the time series data for each item
  return processedData.map(item => {
    // Extract the latest price point
    const latestPoint = item.timeSeriesData && item.timeSeriesData.length > 0
      ? item.timeSeriesData[item.timeSeriesData.length - 1]
      : { date: '', price: 0 };

    // Create a flattened object
    return {
      crop: item.crop,
      location: item.location,
      currentPrice: item.currentPrice,
      priceChange: item.priceChange,
      lastUpdated: item.lastUpdated,
      latestDate: latestPoint.date,
      timeSeriesData: JSON.stringify(item.timeSeriesData)
    };
  });
}

/**
 * Get fields for export based on data and options
 * @param {Array} data - Processed data for export
 * @param {Object} options - Export options
 * @returns {Array} Fields for export
 */
function getFieldsForExport(data, options = {}) {
  if (data.length === 0) {
    return [];
  }

  // If chart data only, use these fields
  if (options.chartDataOnly) {
    return [
      { label: 'Crop', value: 'crop' },
      { label: 'Location', value: 'location' },
      { label: 'Date', value: 'date' },
      { label: 'Price', value: 'price' }
    ];
  }

  // Otherwise, use all fields except timeSeriesData (which is stringified)
  return [
    { label: 'Crop', value: 'crop' },
    { label: 'Location', value: 'location' },
    { label: 'Current Price', value: 'currentPrice' },
    { label: 'Price Change (%)', value: 'priceChange' },
    { label: 'Last Updated', value: 'lastUpdated' },
    { label: 'Latest Date', value: 'latestDate' },
    { label: 'Time Series Data', value: 'timeSeriesData' }
  ];
}
