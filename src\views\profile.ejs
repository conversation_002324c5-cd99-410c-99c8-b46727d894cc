<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card">
      <div class="card-header text-white" style="background-color: var(--theme-primary, #4CAF50);">
        <div class="d-flex align-items-center">
          <% if (typeof userData !== 'undefined' && userData.photoURL) { %>
            <img src="<%= userData.photoURL %>" alt="Profile Picture" class="rounded-circle me-2" style="width: 48px; height: 48px; object-fit: cover;">
          <% } else { %>
            <i class="bi bi-person-circle fs-2 me-2"></i>
          <% } %>
          <h3 class="mb-0"><span class="time-greeting">Welcome</span> to Your Profile</h3>
        </div>
      </div>
      <div class="card-body">
        <% if (typeof error !== 'undefined') { %>
          <div class="alert alert-danger" role="alert">
            <%= error %>
          </div>
        <% } %>

        <% if (typeof success !== 'undefined') { %>
          <div class="alert alert-success" role="alert">
            <%= success %>
          </div>
        <% } %>

        <!-- Profile Picture Section -->
        <div class="mb-4">
          <h4 class="mb-3">Profile Picture</h4>
          <div class="row align-items-center">
            <div class="col-md-4 text-center mb-3 mb-md-0">
              <div class="profile-picture-container">
                <% if (typeof userData !== 'undefined' && userData.photoURL) { %>
                  <img src="<%= userData.photoURL %>" alt="Profile Picture" class="img-fluid rounded-circle profile-picture" style="width: 150px; height: 150px; object-fit: cover;">
                <% } else { %>
                  <div class="profile-picture-placeholder rounded-circle d-flex align-items-center justify-content-center bg-light" style="width: 150px; height: 150px; margin: 0 auto;">
                    <i class="bi bi-person-circle text-muted" style="font-size: 5rem;"></i>
                  </div>
                <% } %>
              </div>
            </div>
            <div class="col-md-8">
              <form action="/profile/upload-photo" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                  <label for="profilePicture" class="form-label">Upload a new profile picture</label>
                  <input class="form-control" type="file" id="profilePicture" name="profilePicture" accept="image/*">
                  <div class="form-text">Recommended size: 300x300 pixels. Maximum file size: 2MB.</div>
                </div>
                <button type="submit" class="btn btn-outline" style="color: var(--theme-primary, #4CAF50); border-color: var(--theme-primary, #4CAF50);">
                  <i class="bi bi-cloud-upload me-1"></i> Upload Picture
                </button>
              </form>
            </div>
          </div>
        </div>

        <hr class="my-4">

        <!-- Profile Information Form -->
        <h4 class="mb-3">Profile Information</h4>
        <form action="/profile/update" method="POST">
          <div class="mb-3">
            <label for="email" class="form-label">Email address</label>
            <input type="email" class="form-control" id="email" name="email" value="<%= user ? user.email : '' %>" readonly>
            <div class="form-text">Email cannot be changed.</div>
          </div>

          <div class="mb-3">
            <label for="displayName" class="form-label">Display Name</label>
            <input type="text" class="form-control" id="displayName" name="displayName" value="<%= user && user.displayName ? user.displayName : '' %>">
          </div>

          <div class="mb-3">
            <label for="farmName" class="form-label">Farm Name</label>
            <input type="text" class="form-control" id="farmName" name="farmName" value="<%= typeof userData !== 'undefined' && userData.farmName ? userData.farmName : '' %>">
          </div>

          <div class="mb-3">
            <label for="location" class="form-label">Location</label>
            <input type="text" class="form-control" id="location" name="location" value="<%= typeof userData !== 'undefined' && userData.location ? userData.location : '' %>">
          </div>

          <div class="mb-3">
            <label for="bio" class="form-label">Bio</label>
            <textarea class="form-control" id="bio" name="bio" rows="3"><%= typeof userData !== 'undefined' && userData.bio ? userData.bio : '' %></textarea>
          </div>

          <div class="d-grid">
            <button type="submit" class="btn" style="background-color: var(--theme-primary, #4CAF50); color: white;">Update Profile</button>
          </div>
        </form>

        <hr class="my-4">

        <h4>Change Password</h4>
        <form action="/profile/change-password" method="POST">
          <div class="mb-3">
            <label for="currentPassword" class="form-label">Current Password</label>
            <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
          </div>

          <div class="mb-3">
            <label for="newPassword" class="form-label">New Password</label>
            <input type="password" class="form-control" id="newPassword" name="newPassword" required>
          </div>

          <div class="mb-3">
            <label for="confirmNewPassword" class="form-label">Confirm New Password</label>
            <input type="password" class="form-control" id="confirmNewPassword" name="confirmNewPassword" required>
          </div>

          <div class="d-grid">
            <button type="submit" class="btn btn-outline" style="color: var(--theme-primary, #4CAF50); border-color: var(--theme-primary, #4CAF50);">Change Password</button>
          </div>
        </form>

        <hr class="my-4">

        <h4>Manage Your Content</h4>
        <div class="d-grid gap-2 mb-4">
          <a href="/uploads/my-uploads" class="btn" style="background-color: var(--theme-primary, #4CAF50); color: white;">
            <i class="bi bi-collection me-2"></i> My Uploads
          </a>
        </div>

        <hr class="my-4">

        <div class="d-grid">
          <a href="/auth/logout" class="btn btn-danger">
            <i class="bi bi-box-arrow-right me-2"></i> Logout
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
