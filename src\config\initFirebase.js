// This script initializes Firebase and ensures it's properly configured
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
  authDomain: "sustainablefarming-bf265.firebaseapp.com",
  projectId: "sustainablefarming-bf265",
  storageBucket: "sustainablefarming-bf265.appspot.com",
  messagingSenderId: "89904373415",
  appId: "1:89904373415:web:2b8bbc14c7802554cac582",
  measurementId: "G-LVMEESYTKJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services with production settings
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

console.log('Firebase initialized with production configuration');

// Function to test Firebase Auth
const testFirebaseAuth = async () => {
  try {
    if (auth) {
      console.log('Firebase Auth is initialized and ready');
      return true;
    } else {
      throw new Error('Firebase Auth is not initialized');
    }
  } catch (error) {
    console.error('Firebase Auth test failed:', error);
    return false;
  }
};

// Function to test Firestore
const testFirestore = async () => {
  try {
    // Simple test to check if Firestore is accessible
    if (db) {
      console.log('Firestore is initialized and ready');
      return true;
    } else {
      throw new Error('Firestore is not initialized');
    }
  } catch (error) {
    console.error('Firestore test failed:', error);
    return false;
  }
};

// Function to test Storage
const testStorage = async () => {
  try {
    if (storage) {
      console.log('Firebase Storage is initialized and ready');
      return true;
    } else {
      throw new Error('Firebase Storage is not initialized');
    }
  } catch (error) {
    console.error('Storage test failed:', error);
    return false;
  }
};

// Initialize and test Firebase services
const initializeFirebase = async () => {
  console.log('Initializing Firebase...');

  const authWorking = await testFirebaseAuth();
  const firestoreWorking = await testFirestore();
  const storageWorking = await testStorage();

  if (authWorking && firestoreWorking && storageWorking) {
    console.log('All Firebase services are working correctly');
  } else {
    console.warn('Some Firebase services are not working correctly. Using fallback mechanisms.');
  }

  return { authWorking, firestoreWorking, storageWorking };
};

// Export Firebase services and initialization function
export { app, auth, db, storage, initializeFirebase };
