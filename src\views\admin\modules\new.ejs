<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/admin">Admin Dashboard</a></li>
          <li class="breadcrumb-item"><a href="/admin/courses">Courses</a></li>
          <li class="breadcrumb-item"><a href="/admin/courses/<%= course.id %>/modules">Modules</a></li>
          <li class="breadcrumb-item active" aria-current="page">New Module</li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-12">
      <h1>Add New Module to <%= course.title %></h1>
    </div>
  </div>

  <% if (typeof error !== 'undefined') { %>
    <div class="row">
      <div class="col-md-12">
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Module Details</h5>
        </div>
        <div class="card-body">
          <form action="/admin/courses/<%= course.id %>/modules" method="POST" id="moduleForm">
            <div class="mb-3">
              <label for="title" class="form-label">Module Title <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="title" name="title" required
                     value="<%= typeof formData !== 'undefined' ? formData.title : '' %>">
            </div>
            
            <div class="mb-3">
              <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
              <textarea class="form-control" id="description" name="description" rows="3" required><%= typeof formData !== 'undefined' ? formData.description : '' %></textarea>
              <div class="form-text">Provide a brief description of what this module covers.</div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="duration" class="form-label">Duration (minutes) <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="duration" name="duration" min="1" required
                       value="<%= typeof formData !== 'undefined' ? formData.duration : '' %>">
                <div class="form-text">Estimated time to complete this module.</div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="order" class="form-label">Order <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="order" name="order" min="1" required
                       value="<%= typeof formData !== 'undefined' ? formData.order : (course.moduleCount + 1) %>">
                <div class="form-text">Position of this module in the course.</div>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="videoUrl" class="form-label">Video URL</label>
              <input type="url" class="form-control" id="videoUrl" name="videoUrl" placeholder="https://www.youtube.com/embed/..."
                     value="<%= typeof formData !== 'undefined' ? formData.videoUrl : '' %>">
              <div class="form-text">Optional. YouTube or Vimeo embed URL for this module.</div>
            </div>
            
            <div class="mb-3">
              <label for="content" class="form-label">Module Content <span class="text-danger">*</span></label>
              <textarea class="form-control" id="content" name="content" rows="15" required><%= typeof formData !== 'undefined' ? formData.content : '' %></textarea>
              <div class="form-text">The main content of the module. You can use HTML for formatting.</div>
            </div>
            
            <div class="mb-3">
              <label class="form-label">Resources</label>
              <div id="resourcesContainer">
                <!-- Resources will be added here dynamically -->
                <% if (typeof formData !== 'undefined' && formData.resourceNames) { %>
                  <% if (Array.isArray(formData.resourceNames)) { %>
                    <% for (let i = 0; i < formData.resourceNames.length; i++) { %>
                      <div class="card mb-2 resource-item">
                        <div class="card-body">
                          <div class="row">
                            <div class="col-md-6 mb-2">
                              <label class="form-label">Resource Name <span class="text-danger">*</span></label>
                              <input type="text" class="form-control" name="resourceNames[]" required value="<%= formData.resourceNames[i] %>">
                            </div>
                            <div class="col-md-6 mb-2">
                              <label class="form-label">Resource Type</label>
                              <select class="form-select" name="resourceTypes[]">
                                <option value="pdf" <%= formData.resourceTypes[i] === 'pdf' ? 'selected' : '' %>>PDF</option>
                                <option value="doc" <%= formData.resourceTypes[i] === 'doc' ? 'selected' : '' %>>Document</option>
                                <option value="xls" <%= formData.resourceTypes[i] === 'xls' ? 'selected' : '' %>>Spreadsheet</option>
                                <option value="link" <%= formData.resourceTypes[i] === 'link' ? 'selected' : '' %>>External Link</option>
                              </select>
                            </div>
                            <div class="col-md-12 mb-2">
                              <label class="form-label">Resource URL <span class="text-danger">*</span></label>
                              <input type="url" class="form-control" name="resourceUrls[]" required value="<%= formData.resourceUrls[i] %>">
                            </div>
                            <div class="col-md-12 mb-2">
                              <label class="form-label">Description</label>
                              <input type="text" class="form-control" name="resourceDescriptions[]" value="<%= formData.resourceDescriptions[i] || '' %>">
                            </div>
                            <div class="col-md-12 text-end">
                              <button type="button" class="btn btn-sm btn-outline-danger remove-resource">
                                <i class="bi bi-trash"></i> Remove
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    <% } %>
                  <% } else { %>
                    <div class="card mb-2 resource-item">
                      <div class="card-body">
                        <div class="row">
                          <div class="col-md-6 mb-2">
                            <label class="form-label">Resource Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="resourceNames[]" required value="<%= formData.resourceNames %>">
                          </div>
                          <div class="col-md-6 mb-2">
                            <label class="form-label">Resource Type</label>
                            <select class="form-select" name="resourceTypes[]">
                              <option value="pdf" <%= formData.resourceTypes === 'pdf' ? 'selected' : '' %>>PDF</option>
                              <option value="doc" <%= formData.resourceTypes === 'doc' ? 'selected' : '' %>>Document</option>
                              <option value="xls" <%= formData.resourceTypes === 'xls' ? 'selected' : '' %>>Spreadsheet</option>
                              <option value="link" <%= formData.resourceTypes === 'link' ? 'selected' : '' %>>External Link</option>
                            </select>
                          </div>
                          <div class="col-md-12 mb-2">
                            <label class="form-label">Resource URL <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" name="resourceUrls[]" required value="<%= formData.resourceUrls %>">
                          </div>
                          <div class="col-md-12 mb-2">
                            <label class="form-label">Description</label>
                            <input type="text" class="form-control" name="resourceDescriptions[]" value="<%= formData.resourceDescriptions || '' %>">
                          </div>
                          <div class="col-md-12 text-end">
                            <button type="button" class="btn btn-sm btn-outline-danger remove-resource">
                              <i class="bi bi-trash"></i> Remove
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% } %>
                <% } %>
              </div>
              
              <button type="button" class="btn btn-outline-success" id="addResourceBtn">
                <i class="bi bi-plus-circle"></i> Add Resource
              </button>
            </div>
            
            <div class="mb-3">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch" id="isPublished" name="isPublished"
                       <%= typeof formData !== 'undefined' && formData.isPublished === 'on' ? 'checked' : '' %>>
                <label class="form-check-label" for="isPublished">Publish module immediately</label>
              </div>
              <div class="form-text">If unchecked, the module will be saved as a draft.</div>
            </div>
            
            <div class="d-flex justify-content-between">
              <a href="/admin/courses/<%= course.id %>/modules" class="btn btn-outline-secondary">Cancel</a>
              <button type="submit" class="btn btn-success">Create Module</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Information</h5>
        </div>
        <div class="card-body">
          <% if (course.imageUrl) { %>
            <img src="<%= course.imageUrl %>" alt="<%= course.title %>" class="img-fluid rounded mb-3">
          <% } %>
          
          <h6><%= course.title %></h6>
          <p class="text-muted small">
            <span class="badge bg-<%= course.level === 'beginner' ? 'success' : (course.level === 'intermediate' ? 'warning' : 'danger') %>">
              <%= course.level.charAt(0).toUpperCase() + course.level.slice(1) %>
            </span>
            <span class="badge bg-secondary ms-1"><%= course.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></span>
          </p>
          
          <p class="small"><%= course.description %></p>
          
          <div class="d-grid">
            <a href="/admin/courses/<%= course.id %>/modules" class="btn btn-outline-primary">
              <i class="bi bi-arrow-left"></i> Back to Modules
            </a>
          </div>
        </div>
      </div>
      
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h5 class="mb-0">Tips for Creating Modules</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item">
              <i class="bi bi-lightbulb text-warning me-2"></i>
              Keep modules focused on a single topic
            </li>
            <li class="list-group-item">
              <i class="bi bi-clock text-success me-2"></i>
              Aim for 30-60 minutes of content per module
            </li>
            <li class="list-group-item">
              <i class="bi bi-file-earmark-text text-primary me-2"></i>
              Include downloadable resources for practice
            </li>
            <li class="list-group-item">
              <i class="bi bi-list-check text-success me-2"></i>
              Structure content with headings and lists
            </li>
            <li class="list-group-item">
              <i class="bi bi-image text-info me-2"></i>
              Use images and videos to illustrate concepts
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Resource Template (hidden) -->
<template id="resourceTemplate">
  <div class="card mb-2 resource-item">
    <div class="card-body">
      <div class="row">
        <div class="col-md-6 mb-2">
          <label class="form-label">Resource Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control" name="resourceNames[]" required>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Resource Type</label>
          <select class="form-select" name="resourceTypes[]">
            <option value="pdf">PDF</option>
            <option value="doc">Document</option>
            <option value="xls">Spreadsheet</option>
            <option value="link">External Link</option>
          </select>
        </div>
        <div class="col-md-12 mb-2">
          <label class="form-label">Resource URL <span class="text-danger">*</span></label>
          <input type="url" class="form-control" name="resourceUrls[]" required>
        </div>
        <div class="col-md-12 mb-2">
          <label class="form-label">Description</label>
          <input type="text" class="form-control" name="resourceDescriptions[]">
        </div>
        <div class="col-md-12 text-end">
          <button type="button" class="btn btn-sm btn-outline-danger remove-resource">
            <i class="bi bi-trash"></i> Remove
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const addResourceBtn = document.getElementById('addResourceBtn');
    const resourcesContainer = document.getElementById('resourcesContainer');
    const resourceTemplate = document.getElementById('resourceTemplate');
    
    // Add resource
    addResourceBtn.addEventListener('click', function() {
      const resourceItem = document.importNode(resourceTemplate.content, true);
      resourcesContainer.appendChild(resourceItem);
      
      // Add event listener to the new remove button
      const removeBtn = resourcesContainer.lastElementChild.querySelector('.remove-resource');
      removeBtn.addEventListener('click', function() {
        this.closest('.resource-item').remove();
      });
    });
    
    // Add event listeners to existing remove buttons
    document.querySelectorAll('.remove-resource').forEach(button => {
      button.addEventListener('click', function() {
        this.closest('.resource-item').remove();
      });
    });
    
    // Initialize rich text editor for content
    if (typeof ClassicEditor !== 'undefined') {
      ClassicEditor
        .create(document.getElementById('content'))
        .catch(error => {
          console.error('Error initializing editor:', error);
        });
    }
  });
</script>
