<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">My Uploads</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4">My Uploads</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
    
    <% if (typeof success !== 'undefined') { %>
      <div class="alert alert-success" role="alert">
        <%= success %>
      </div>
    <% } %>
    
    <div class="mb-4">
      <a href="/uploads/new" class="btn btn-success">
        <i class="bi bi-cloud-upload"></i> Upload New Content
      </a>
    </div>
  </div>
</div>

<div class="row">
  <% if (uploads && uploads.length > 0) { %>
    <% uploads.forEach(upload => { %>
      <div class="col-md-4 mb-4 upload-item" data-category="<%= upload.category %>">
        <div class="card h-100 shadow-sm">
          <% if (upload.fileUrl) { %>
            <%
              // Determine file type from either the fileType property or the URL extension
              let isImage = false;
              let isVideo = false;
              let isPdf = false;

              if (upload.fileType) {
                // If we have a fileType property, use it
                isImage = upload.fileType.startsWith('image/');
                isVideo = upload.fileType.startsWith('video/');
                isPdf = upload.fileType === 'application/pdf';
              } else {
                // Otherwise try to determine from URL
                const url = upload.fileUrl.toLowerCase();
                isImage = url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.webp') || url.includes('data:image/');
                isVideo = url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg') || url.includes('data:video/');
                isPdf = url.endsWith('.pdf') || url.includes('data:application/pdf');
              }
            %>

            <% if (isImage) { %>
              <img src="<%= upload.fileUrl %>" class="card-img-top" alt="<%= upload.title %>" style="height: 200px; object-fit: cover;">
            <% } else if (isVideo) { %>
              <div class="card-img-top bg-dark d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-film text-light" style="font-size: 3rem;"></i>
              </div>
            <% } else if (isPdf) { %>
              <div class="card-img-top bg-danger d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-file-pdf text-light" style="font-size: 3rem;"></i>
              </div>
            <% } else { %>
              <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-file-earmark text-light" style="font-size: 3rem;"></i>
              </div>
            <% } %>
          <% } else { %>
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
              <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
            </div>
          <% } %>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
              <h5 class="card-title"><%= upload.title %></h5>
              <span class="badge bg-secondary"><%= upload.category %></span>
            </div>
            <p class="card-text"><%= upload.description %></p>
          </div>
          <div class="card-footer bg-white">
            <div class="d-flex justify-content-between align-items-center">
              <small class="text-muted">
                Posted on <%= new Date(upload.createdAt).toLocaleDateString() %>
              </small>
              <div>
                <a href="<%= upload.fileUrl %>" class="btn btn-sm btn-outline-primary" target="_blank">
                  <i class="bi bi-eye"></i> View
                </a>
                <form action="/uploads/<%= upload.id %>/delete" method="POST" class="d-inline">
                  <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this content? This action cannot be undone.')">
                    <i class="bi bi-trash"></i> Delete
                  </button>
                </form>
              </div>
            </div>
            
            <!-- Stats display -->
            <div class="d-flex justify-content-between mt-3">
              <div class="text-center">
                <div class="fw-bold"><%= upload.likeCount || 0 %></div>
                <small class="text-muted">Likes</small>
              </div>
              <div class="text-center">
                <div class="fw-bold"><%= upload.commentCount || 0 %></div>
                <small class="text-muted">Comments</small>
              </div>
              <div class="text-center">
                <div class="fw-bold">0</div>
                <small class="text-muted">Shares</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <div class="col-md-12">
      <div class="alert alert-info" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        You haven't uploaded any content yet. Click the "Upload New Content" button to get started.
      </div>
    </div>
  <% } %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Category filter functionality
    const categoryLinks = document.querySelectorAll('[data-category]');
    categoryLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const category = this.dataset.category;
        const uploadItems = document.querySelectorAll('.upload-item');
        
        uploadItems.forEach(item => {
          if (category === 'all' || item.dataset.category === category) {
            item.style.display = 'block';
          } else {
            item.style.display = 'none';
          }
        });
      });
    });
  });
</script>
