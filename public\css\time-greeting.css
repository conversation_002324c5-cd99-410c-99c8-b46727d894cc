/* Time-based greeting styles */

.time-greeting {
  display: inline-block;
  transition: all 0.3s ease;
  position: relative;
  font-size: 1.1rem !important;
  color: #198754 !important;
  font-weight: bold !important;
}

.time-greeting.updating {
  animation: greeting-update 1s ease;
}

@keyframes greeting-update {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  20% {
    opacity: 0;
    transform: translateY(-5px);
  }
  80% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Make sure the greeting is visible on mobile */
@media (max-width: 767.98px) {
  .time-greeting {
    display: inline-block !important;
    font-size: 1rem !important;
  }
}
