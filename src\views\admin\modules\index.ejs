<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/admin">Admin Dashboard</a></li>
          <li class="breadcrumb-item"><a href="/admin/courses">Courses</a></li>
          <li class="breadcrumb-item active" aria-current="page">Modules for <%= course.title %></li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-8">
      <h1>Modules for <%= course.title %></h1>
      <p class="text-muted">
        <span class="badge bg-<%= course.level === 'beginner' ? 'success' : (course.level === 'intermediate' ? 'warning' : 'danger') %>">
          <%= course.level.charAt(0).toUpperCase() + course.level.slice(1) %>
        </span>
        <span class="badge bg-secondary ms-2"><%= course.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></span>
        <span class="ms-2"><%= course.moduleCount || 0 %> modules</span>
        <span class="ms-2"><%= course.isPublished ? 'Published' : 'Draft' %></span>
      </p>
    </div>
    <div class="col-md-4 text-end">
      <a href="/admin/courses/<%= course.id %>/modules/new" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Add New Module
      </a>
    </div>
  </div>

  <% if (typeof error !== 'undefined') { %>
    <div class="row">
      <div class="col-md-12">
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Course Modules</h5>
            <div>
              <button class="btn btn-sm btn-outline-secondary" id="reorderBtn" style="display: none;">
                <i class="bi bi-save"></i> Save Order
              </button>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <% if (modules && modules.length > 0) { %>
            <div class="list-group list-group-flush" id="modulesList">
              <% modules.forEach((module, index) => { %>
                <div class="list-group-item module-item" data-module-id="<%= module.id %>" data-order="<%= module.order %>">
                  <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                      <div class="me-3 handle" style="cursor: grab;">
                        <i class="bi bi-grip-vertical text-muted"></i>
                      </div>
                      <div>
                        <h6 class="mb-0">Module <%= index + 1 %>: <%= module.title %></h6>
                        <small class="text-muted">
                          <i class="bi bi-clock"></i> <%= module.duration %> min
                          <% if (module.isPublished) { %>
                            <span class="badge bg-success ms-2">Published</span>
                          <% } else { %>
                            <span class="badge bg-secondary ms-2">Draft</span>
                          <% } %>
                        </small>
                      </div>
                    </div>
                    <div class="btn-group btn-group-sm">
                      <a href="/admin/courses/<%= course.id %>/modules/<%= module.id %>/edit" class="btn btn-outline-primary" title="Edit">
                        <i class="bi bi-pencil"></i>
                      </a>
                      <button type="button" class="btn btn-outline-success" title="Toggle Published" 
                              onclick="toggleModulePublished('<%= course.id %>', '<%= module.id %>', this)"
                              data-published="<%= module.isPublished ? 'true' : 'false' %>">
                        <i class="bi <%= module.isPublished ? 'bi-eye-slash' : 'bi-eye' %>"></i>
                      </button>
                      <button type="button" class="btn btn-outline-danger" title="Delete" 
                              onclick="confirmDeleteModule('<%= course.id %>', '<%= module.id %>', '<%= module.title %>')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              <% }); %>
            </div>
          <% } else { %>
            <div class="text-center py-5">
              <div class="text-muted">
                <i class="bi bi-collection fs-1 mb-3"></i>
                <h5>No Modules Yet</h5>
                <p>This course doesn't have any modules yet.</p>
                <a href="/admin/courses/<%= course.id %>/modules/new" class="btn btn-success">
                  <i class="bi bi-plus-circle"></i> Add First Module
                </a>
              </div>
            </div>
          <% } %>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Course Details</h5>
        </div>
        <div class="card-body">
          <% if (course.imageUrl) { %>
            <img src="<%= course.imageUrl %>" alt="<%= course.title %>" class="img-fluid rounded mb-3">
          <% } %>
          
          <h6>Description</h6>
          <p><%= course.description %></p>
          
          <div class="d-grid gap-2">
            <a href="/admin/courses/<%= course.id %>/edit" class="btn btn-outline-primary">
              <i class="bi bi-pencil"></i> Edit Course Details
            </a>
            <a href="/courses/<%= course.id %>" class="btn btn-outline-success" target="_blank">
              <i class="bi bi-eye"></i> Preview Course
            </a>
          </div>
        </div>
      </div>
      
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h5 class="mb-0">Module Management Tips</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item">
              <i class="bi bi-arrow-up-down text-success me-2"></i>
              Drag and drop modules to reorder them
            </li>
            <li class="list-group-item">
              <i class="bi bi-eye text-success me-2"></i>
              Toggle visibility to publish/unpublish modules
            </li>
            <li class="list-group-item">
              <i class="bi bi-pencil text-success me-2"></i>
              Edit modules to update content and resources
            </li>
            <li class="list-group-item">
              <i class="bi bi-plus-circle text-success me-2"></i>
              Add new modules to expand your course
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Module Modal -->
<div class="modal fade" id="deleteModuleModal" tabindex="-1" aria-labelledby="deleteModuleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteModuleModalLabel">Confirm Deletion</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the module "<span id="moduleTitle"></span>"?</p>
        <p class="text-danger">This action cannot be undone. All content and resources will be permanently deleted.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form id="deleteModuleForm" method="POST" action="">
          <button type="submit" class="btn btn-danger">Delete Module</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Sortable.js for drag-and-drop reordering
    const modulesList = document.getElementById('modulesList');
    
    if (modulesList) {
      const sortable = new Sortable(modulesList, {
        handle: '.handle',
        animation: 150,
        onStart: function() {
          document.getElementById('reorderBtn').style.display = 'inline-block';
        },
        onEnd: function() {
          // Update the order numbers
          const items = modulesList.querySelectorAll('.module-item');
          items.forEach((item, index) => {
            item.dataset.order = index + 1;
          });
        }
      });
      
      // Save reordering
      document.getElementById('reorderBtn').addEventListener('click', function() {
        const items = modulesList.querySelectorAll('.module-item');
        const moduleOrders = [];
        
        items.forEach((item, index) => {
          moduleOrders.push({
            moduleId: item.dataset.moduleId,
            order: index + 1
          });
        });
        
        // Send the new order to the server
        fetch(`/admin/courses/<%= course.id %>/modules/reorder`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ moduleOrders })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Hide the save button
            document.getElementById('reorderBtn').style.display = 'none';
            
            // Update the UI to reflect the new order
            items.forEach((item, index) => {
              const title = item.querySelector('h6');
              title.textContent = title.textContent.replace(/Module \d+:/, `Module ${index + 1}:`);
            });
            
            // Show success message
            alert('Module order saved successfully!');
          } else {
            alert('Error saving module order: ' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while saving the module order.');
        });
      });
    }
  });
  
  function confirmDeleteModule(courseId, moduleId, moduleTitle) {
    document.getElementById('moduleTitle').textContent = moduleTitle;
    document.getElementById('deleteModuleForm').action = `/admin/courses/${courseId}/modules/${moduleId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModuleModal'));
    modal.show();
  }
  
  function toggleModulePublished(courseId, moduleId, button) {
    const isPublished = button.dataset.published === 'true';
    const icon = button.querySelector('i');
    
    // Optimistic UI update
    button.dataset.published = isPublished ? 'false' : 'true';
    icon.className = `bi ${isPublished ? 'bi-eye' : 'bi-eye-slash'}`;
    
    // Update the badge
    const badge = button.closest('.module-item').querySelector('.badge');
    badge.className = `badge ${isPublished ? 'bg-secondary' : 'bg-success'} ms-2`;
    badge.textContent = isPublished ? 'Draft' : 'Published';
    
    // Send request to server
    fetch(`/admin/courses/${courseId}/modules/${moduleId}/toggle-published`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ isPublished: !isPublished })
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        // Revert UI if there was an error
        button.dataset.published = isPublished ? 'true' : 'false';
        icon.className = `bi ${isPublished ? 'bi-eye-slash' : 'bi-eye'}`;
        badge.className = `badge ${isPublished ? 'bg-success' : 'bg-secondary'} ms-2`;
        badge.textContent = isPublished ? 'Published' : 'Draft';
        alert('Error: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      // Revert UI on error
      button.dataset.published = isPublished ? 'true' : 'false';
      icon.className = `bi ${isPublished ? 'bi-eye-slash' : 'bi-eye'}`;
      badge.className = `badge ${isPublished ? 'bg-success' : 'bg-secondary'} ms-2`;
      badge.textContent = isPublished ? 'Published' : 'Draft';
      alert('An error occurred. Please try again.');
    });
  }
</script>
