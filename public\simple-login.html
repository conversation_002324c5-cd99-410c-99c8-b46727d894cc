<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0">🌱 Simple Login Test</h3>
                    </div>
                    <div class="card-body">
                        <div id="status" class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Initializing...
                        </div>
                        
                        <!-- Registration Form -->
                        <div class="mb-4">
                            <h5><i class="bi bi-person-plus"></i> Create Test Account</h5>
                            <form id="registerForm">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="regEmail" value="<EMAIL>" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" id="regPassword" value="test123456" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-person-plus"></i> Register
                                </button>
                            </form>
                        </div>
                        
                        <hr>
                        
                        <!-- Login Form -->
                        <div class="mb-4">
                            <h5><i class="bi bi-box-arrow-in-right"></i> Login</h5>
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="loginEmail" value="<EMAIL>" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" id="loginPassword" value="test123456" required>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-box-arrow-in-right"></i> Login
                                </button>
                            </form>
                        </div>
                        
                        <!-- User Info -->
                        <div id="userInfo" style="display: none;">
                            <hr>
                            <h5><i class="bi bi-person-check"></i> Logged In User</h5>
                            <div id="userDetails" class="alert alert-success"></div>
                            <button id="logoutBtn" class="btn btn-danger">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </button>
                            <a href="/dashboard" class="btn btn-primary ms-2">
                                <i class="bi bi-speedometer2"></i> Go to Dashboard
                            </a>
                        </div>
                        
                        <!-- Test Links -->
                        <div class="mt-4">
                            <h6>Test Links:</h6>
                            <a href="/auth/login" class="btn btn-outline-primary btn-sm me-2">Main Login</a>
                            <a href="/auth/register" class="btn btn-outline-secondary btn-sm me-2">Main Register</a>
                            <a href="/dashboard" class="btn btn-outline-success btn-sm">Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            createUserWithEmailAndPassword,
            signInWithEmailAndPassword,
            signOut,
            onAuthStateChanged,
            updateProfile
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
            authDomain: "sustainablefarming-bf265.firebaseapp.com",
            projectId: "sustainablefarming-bf265",
            storageBucket: "sustainablefarming-bf265.appspot.com",
            messagingSenderId: "89904373415",
            appId: "1:89904373415:web:2b8bbc14c7802554cac582"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        const status = document.getElementById('status');
        const userInfo = document.getElementById('userInfo');
        const userDetails = document.getElementById('userDetails');

        function updateStatus(message, type = 'info') {
            status.innerHTML = `<i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;
            status.className = `alert alert-${type}`;
        }

        // Initialize
        setTimeout(() => {
            updateStatus('Firebase initialized successfully!', 'success');
        }, 1000);

        // Auth state observer
        onAuthStateChanged(auth, (user) => {
            if (user) {
                userDetails.innerHTML = `
                    <strong>✅ Authentication Successful!</strong><br>
                    <strong>UID:</strong> ${user.uid}<br>
                    <strong>Email:</strong> ${user.email}<br>
                    <strong>Display Name:</strong> ${user.displayName || 'Not set'}<br>
                    <strong>Email Verified:</strong> ${user.emailVerified ? 'Yes' : 'No'}
                `;
                userInfo.style.display = 'block';
                updateStatus('User is logged in successfully!', 'success');
            } else {
                userInfo.style.display = 'none';
                updateStatus('No user logged in', 'warning');
            }
        });

        // Register form
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            
            try {
                updateStatus('Creating user account...', 'info');
                
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                
                // Update profile with display name
                await updateProfile(userCredential.user, {
                    displayName: 'Test User'
                });
                
                updateStatus('User account created successfully!', 'success');
            } catch (error) {
                console.error('Registration error:', error);
                updateStatus(`Registration failed: ${error.message}`, 'danger');
            }
        });

        // Login form
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                updateStatus('Logging in...', 'info');
                
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                
                updateStatus('Login successful!', 'success');
            } catch (error) {
                console.error('Login error:', error);
                updateStatus(`Login failed: ${error.message}`, 'danger');
            }
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', async () => {
            try {
                await signOut(auth);
                updateStatus('Logged out successfully!', 'info');
            } catch (error) {
                console.error('Logout error:', error);
                updateStatus(`Logout failed: ${error.message}`, 'danger');
            }
        });
    </script>
</body>
</html>
