// Main JavaScript file for client-side functionality

document.addEventListener('DOMContentLoaded', function() {

  // Form validation for registration
  // Get registration form if it exists on the page
  const registrationForm = document.querySelector('form[action="/auth/register"]');

  if (registrationForm) {
    registrationForm.addEventListener('submit', function(event) {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // Check if passwords match
      if (password !== confirmPassword) {
        event.preventDefault();

        // Create or update error message
        let errorDiv = document.querySelector('.alert-danger');
        if (!errorDiv) {
          errorDiv = document.createElement('div');
          errorDiv.className = 'alert alert-danger';
          errorDiv.setAttribute('role', 'alert');
          registrationForm.insertBefore(errorDiv, registrationForm.firstChild);
        }

        errorDiv.textContent = 'Passwords do not match!';

        // Scroll to the top of the form
        window.scrollTo(0, registrationForm.offsetTop - 20);
      }

      // Check password length
      if (password.length < 6) {
        event.preventDefault();

        // Create or update error message
        let errorDiv = document.querySelector('.alert-danger');
        if (!errorDiv) {
          errorDiv = document.createElement('div');
          errorDiv.className = 'alert alert-danger';
          errorDiv.setAttribute('role', 'alert');
          registrationForm.insertBefore(errorDiv, registrationForm.firstChild);
        }

        errorDiv.textContent = 'Password must be at least 6 characters long!';

        // Scroll to the top of the form
        window.scrollTo(0, registrationForm.offsetTop - 20);
      }
    });
  }

  // Add password visibility toggle
  const passwordFields = document.querySelectorAll('input[type="password"]');

  passwordFields.forEach(field => {
    // Create toggle button
    const toggleButton = document.createElement('button');
    toggleButton.type = 'button';
    toggleButton.className = 'btn btn-outline-secondary btn-sm position-absolute end-0 top-50 translate-middle-y me-2';
    toggleButton.innerHTML = '<i class="bi bi-eye"></i>';
    toggleButton.style.zIndex = '5';

    // Add toggle functionality
    toggleButton.addEventListener('click', function() {
      if (field.type === 'password') {
        field.type = 'text';
        this.innerHTML = '<i class="bi bi-eye-slash"></i>';
      } else {
        field.type = 'password';
        this.innerHTML = '<i class="bi bi-eye"></i>';
      }
    });

    // Add button to the DOM
    const parentDiv = field.parentElement;
    parentDiv.style.position = 'relative';
    parentDiv.appendChild(toggleButton);
  });
});


