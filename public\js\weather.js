// Weather functionality for Sustainable Farming application

document.addEventListener('DOMContentLoaded', function() {
  // DOM elements
  const citySearchInput = document.getElementById('citySearch');
  const searchCityBtn = document.getElementById('searchCityBtn');
  const getCurrentLocationBtn = document.getElementById('getCurrentLocationBtn');
  const currentWeatherContainer = document.getElementById('currentWeatherContainer');
  const forecastContainer = document.getElementById('forecastContainer');
  const weatherMapContainer = document.getElementById('weatherMapContainer');
  const farmingWeatherContainer = document.getElementById('farmingWeatherContainer');
  const weatherErrorAlert = document.getElementById('weatherErrorAlert');
  const weatherErrorMessage = document.getElementById('weatherErrorMessage');
  const forecastRow = document.getElementById('forecastRow');
  const farmingConditionsList = document.getElementById('farmingConditionsList');
  const farmingRecommendationsList = document.getElementById('farmingRecommendationsList');
  const loadingIndicator = document.createElement('div');

  // Create and add loading indicator
  loadingIndicator.className = 'weather-loading-indicator';
  loadingIndicator.innerHTML = `
    <div class="spinner-border text-success" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Loading weather data...</p>
  `;
  loadingIndicator.style.display = 'none';
  document.querySelector('.container').appendChild(loadingIndicator);

  // Weather elements
  const locationName = document.getElementById('locationName');
  const locationCountry = document.getElementById('locationCountry');
  const currentDate = document.getElementById('currentDate');
  const currentTemp = document.getElementById('currentTemp');
  const weatherDescription = document.getElementById('weatherDescription');
  const feelsLike = document.getElementById('feelsLike');
  const humidity = document.getElementById('humidity');
  const windSpeed = document.getElementById('windSpeed');
  const sunrise = document.getElementById('sunrise');
  const sunset = document.getElementById('sunset');

  // Weather map functionality disabled
  let map = null;
  let marker = null;

  // Initialize map - disabled
  window.initMap = function() {
    console.log('Google Maps functionality is disabled');
  };

  // Event listeners
  searchCityBtn.addEventListener('click', searchWeatherByCity);
  getCurrentLocationBtn.addEventListener('click', getWeatherByCurrentLocation);
  citySearchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchWeatherByCity();
    }
  });

  // Check for saved location or try to get current location on page load
  initializeWeather();

  // Search weather by city name
  async function searchWeatherByCity() {
    const city = citySearchInput.value.trim();

    if (!city) {
      showError('Please enter a city name');
      return;
    }

    try {
      hideError();
      showLoading();

      // Fetch current weather
      const currentWeatherResponse = await fetch(`/weather/api/city/current?city=${encodeURIComponent(city)}`);

      if (!currentWeatherResponse.ok) {
        const errorData = await currentWeatherResponse.json();
        throw new Error(errorData.error || 'Failed to fetch weather data');
      }

      const currentWeatherData = await currentWeatherResponse.json();

      // Fetch forecast
      const forecastResponse = await fetch(`/weather/api/city/forecast?city=${encodeURIComponent(city)}&days=5`);

      if (!forecastResponse.ok) {
        const errorData = await forecastResponse.json();
        throw new Error(errorData.error || 'Failed to fetch forecast data');
      }

      const forecastData = await forecastResponse.json();

      // Display weather data
      displayWeatherData(currentWeatherData, forecastData);

    } catch (error) {
      console.error('Error fetching weather data:', error);
      showError(error.message || 'Failed to fetch weather data. Please try again.');
    } finally {
      hideLoading();
    }
  }

  // Get weather by current location
  function getWeatherByCurrentLocation() {
    if (!navigator.geolocation) {
      showError('Geolocation is not supported by your browser');
      return;
    }

    hideError();
    showLoading();

    // Add loading indicator to the button
    const originalButtonText = getCurrentLocationBtn.innerHTML;
    getCurrentLocationBtn.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> Getting location...';
    getCurrentLocationBtn.disabled = true;

    // Options for geolocation
    const options = {
      enableHighAccuracy: true,  // Get high accuracy if available
      timeout: 10000,            // Time to wait before error (10 seconds)
      maximumAge: 0              // Don't use cached position
    };

    navigator.geolocation.getCurrentPosition(
      async position => {
        try {
          const { latitude, longitude } = position.coords;

          console.log(`Location obtained: Lat ${latitude}, Lon ${longitude}`);

          // Fetch current weather
          const currentWeatherResponse = await fetch(`/weather/api/current?lat=${latitude}&lon=${longitude}`);

          if (!currentWeatherResponse.ok) {
            const errorData = await currentWeatherResponse.json();
            throw new Error(errorData.error || 'Failed to fetch weather data');
          }

          const currentWeatherData = await currentWeatherResponse.json();

          // Fetch forecast
          const forecastResponse = await fetch(`/weather/api/forecast?lat=${latitude}&lon=${longitude}&days=5`);

          if (!forecastResponse.ok) {
            const errorData = await forecastResponse.json();
            throw new Error(errorData.error || 'Failed to fetch forecast data');
          }

          const forecastData = await forecastResponse.json();

          // Location obtained successfully

          // Display weather data
          displayWeatherData(currentWeatherData, forecastData);

        } catch (error) {
          console.error('Error fetching weather data:', error);
          showError(error.message || 'Failed to fetch weather data. Please try again.');
        } finally {
          hideLoading();
          getCurrentLocationBtn.innerHTML = originalButtonText;
          getCurrentLocationBtn.disabled = false;
        }
      },
      error => {
        hideLoading();
        getCurrentLocationBtn.innerHTML = originalButtonText;
        getCurrentLocationBtn.disabled = false;

        let errorMessage = 'Failed to get your location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'You denied the request for geolocation. Please enable location services in your browser settings.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable. Please try again or search by city name.';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get your location timed out. Please try again or search by city name.';
            break;
        }

        console.error('Geolocation error:', error);
        showError(errorMessage);
      },
      options
    );
  }

  // Display weather data
  function displayWeatherData(currentWeather, forecast) {
    if (!currentWeather) {
      showError('No weather data available');
      return;
    }

    // Display current weather
    locationName.textContent = currentWeather.location;
    locationCountry.textContent = currentWeather.country;
    currentDate.textContent = currentWeather.date;
    currentTemp.textContent = `${currentWeather.temperature}°C`;
    weatherDescription.textContent = currentWeather.description;
    feelsLike.textContent = currentWeather.feelsLike;
    humidity.textContent = `${currentWeather.humidity}%`;
    windSpeed.textContent = `${currentWeather.windSpeed} m/s`;
    sunrise.textContent = currentWeather.sunrise;
    sunset.textContent = currentWeather.sunset;

    // Show current weather container
    currentWeatherContainer.style.display = 'block';

    // Display forecast
    displayForecast(forecast);

    // Update map
    updateWeatherMap(currentWeather.location);

    // Display farming recommendations
    displayFarmingRecommendations(currentWeather, forecast);
  }

  // Display forecast data
  function displayForecast(forecastData) {
    if (!forecastData || forecastData.length === 0) {
      forecastContainer.style.display = 'none';
      return;
    }

    // Clear previous forecast
    forecastRow.innerHTML = '';

    // Add forecast cards
    forecastData.forEach(day => {
      const dayOfWeek = new Date(day.date).toLocaleDateString('en-US', { weekday: 'long' });

      const forecastCard = document.createElement('div');
      forecastCard.className = 'col-md-2 col-sm-4 col-6 mb-3';
      forecastCard.innerHTML = `
        <div class="card h-100 forecast-card text-center">
          <div class="card-body">
            <h5 class="forecast-day">${dayOfWeek}</h5>
            <p class="forecast-date">${day.date}</p>
            <img src="https://openweathermap.org/img/wn/${day.icon}@2x.png" alt="${day.description}" class="weather-icon mb-2">
            <p class="forecast-temp">${day.avgTemp}°C</p>
            <p class="forecast-min-max">${day.minTemp}°C / ${day.maxTemp}°C</p>
            <p class="forecast-description">${day.description}</p>
          </div>
        </div>
      `;

      forecastRow.appendChild(forecastCard);
    });

    // Show forecast container
    forecastContainer.style.display = 'block';
  }

  // Update weather map - disabled
  function updateWeatherMap(location) {
    console.log('Weather map update disabled for location:', location);
    // Keep the map container hidden
    if (weatherMapContainer) {
      weatherMapContainer.style.display = 'none';
    }
  }

  // Display farming recommendations based on weather
  function displayFarmingRecommendations(currentWeather, forecast) {
    if (!currentWeather) return;

    // Clear previous recommendations
    farmingConditionsList.innerHTML = '';
    farmingRecommendationsList.innerHTML = '';

    // Current conditions analysis
    const conditions = [];
    const recommendations = [];

    // Temperature analysis
    if (currentWeather.temperature < 5) {
      conditions.push({
        text: 'Cold temperatures may affect plant growth and development',
        type: 'unfavorable'
      });
      recommendations.push('Consider using cold frames or row covers to protect sensitive crops');
    } else if (currentWeather.temperature > 30) {
      conditions.push({
        text: 'High temperatures may cause heat stress in plants',
        type: 'unfavorable'
      });
      recommendations.push('Increase watering frequency and consider shade cloth for sensitive crops');
    } else {
      conditions.push({
        text: 'Temperature is within optimal range for most crops',
        type: 'favorable'
      });
    }

    // Humidity analysis
    if (currentWeather.humidity < 30) {
      conditions.push({
        text: 'Low humidity may increase water loss through evaporation',
        type: 'unfavorable'
      });
      recommendations.push('Consider mulching to retain soil moisture');
    } else if (currentWeather.humidity > 80) {
      conditions.push({
        text: 'High humidity may increase risk of fungal diseases',
        type: 'unfavorable'
      });
      recommendations.push('Ensure good air circulation around plants and avoid overhead watering');
    } else {
      conditions.push({
        text: 'Humidity is within optimal range for most crops',
        type: 'favorable'
      });
    }

    // Wind analysis
    if (currentWeather.windSpeed > 8) {
      conditions.push({
        text: 'Strong winds may damage plants and increase water loss',
        type: 'unfavorable'
      });
      recommendations.push('Consider windbreaks or temporary shelters for sensitive crops');
    } else {
      conditions.push({
        text: 'Wind speed is within acceptable range for most crops',
        type: 'favorable'
      });
    }

    // Weather description analysis
    const weatherDesc = currentWeather.description.toLowerCase();

    if (weatherDesc.includes('rain') || weatherDesc.includes('drizzle')) {
      conditions.push({
        text: 'Rainfall provides natural irrigation but may increase disease risk',
        type: 'neutral'
      });
      recommendations.push('Hold off on irrigation and avoid working in wet fields to prevent soil compaction');
    } else if (weatherDesc.includes('snow')) {
      conditions.push({
        text: 'Snow provides insulation but limits field access',
        type: 'neutral'
      });
      recommendations.push('Focus on indoor activities like planning and equipment maintenance');
    } else if (weatherDesc.includes('clear') || weatherDesc.includes('sun')) {
      conditions.push({
        text: 'Sunny conditions promote photosynthesis and plant growth',
        type: 'favorable'
      });
      recommendations.push('Good day for planting, harvesting, or other field activities');
    }

    // Forecast-based recommendations
    if (forecast && forecast.length > 0) {
      const tomorrowForecast = forecast[0];

      if (tomorrowForecast.description.toLowerCase().includes('rain')) {
        recommendations.push('Rain expected tomorrow - complete any dry-weather tasks today');
      }

      // Check for temperature trends
      const tempTrend = forecast.map(day => day.avgTemp);
      const isWarming = tempTrend.every((temp, i, arr) => i === 0 || temp >= arr[i - 1]);
      const isCooling = tempTrend.every((temp, i, arr) => i === 0 || temp <= arr[i - 1]);

      if (isWarming) {
        recommendations.push('Temperatures are trending warmer over the next few days - adjust irrigation accordingly');
      } else if (isCooling) {
        recommendations.push('Temperatures are trending cooler over the next few days - protect sensitive crops if needed');
      }
    }

    // Add general recommendation
    recommendations.push('Monitor soil moisture levels regularly and adjust irrigation as needed');

    // Display conditions
    conditions.forEach(condition => {
      const conditionItem = document.createElement('li');
      conditionItem.className = `list-group-item farming-condition condition-${condition.type}`;
      conditionItem.innerHTML = condition.text;
      farmingConditionsList.appendChild(conditionItem);
    });

    // Display recommendations
    recommendations.forEach(recommendation => {
      const recommendationItem = document.createElement('li');
      recommendationItem.className = 'list-group-item farming-recommendation';
      recommendationItem.innerHTML = recommendation;
      farmingRecommendationsList.appendChild(recommendationItem);
    });

    // Show farming weather container
    farmingWeatherContainer.style.display = 'block';
  }

  // Initialize weather on page load
  async function initializeWeather() {
    // Get weather for current location

    if (savedLocation && (new Date().getTime() - savedLocation.timestamp < thirtyMinutesInMs)) {
      // Use saved location if it's recent
      try {
        hideError();
        showLoading();

        console.log('Using saved location:', savedLocation);

        // Fetch current weather
        const currentWeatherResponse = await fetch(`/weather/api/current?lat=${savedLocation.lat}&lon=${savedLocation.lon}`);

        if (!currentWeatherResponse.ok) {
          throw new Error('Failed to fetch weather data');
        }

        const currentWeatherData = await currentWeatherResponse.json();

        // Fetch forecast
        const forecastResponse = await fetch(`/weather/api/forecast?lat=${savedLocation.lat}&lon=${savedLocation.lon}&days=5`);

        if (!forecastResponse.ok) {
          throw new Error('Failed to fetch forecast data');
        }

        const forecastData = await forecastResponse.json();

        // Display weather data
        displayWeatherData(currentWeatherData, forecastData);
      } catch (error) {
        console.error('Error using saved location:', error);
        // If there's an error, try to get current location
        getWeatherByCurrentLocation();
      } finally {
        hideLoading();
      }
    } else {
      // No recent saved location, try to get current location
      getWeatherByCurrentLocation();
    }
  }

  // Show loading state
  function showLoading() {
    loadingIndicator.style.display = 'flex';
  }

  // Hide loading state
  function hideLoading() {
    loadingIndicator.style.display = 'none';
  }

  // Show error message
  function showError(message) {
    weatherErrorMessage.textContent = message;
    weatherErrorAlert.style.display = 'block';

    // Hide containers
    currentWeatherContainer.style.display = 'none';
    forecastContainer.style.display = 'none';
    if (weatherMapContainer) {
      weatherMapContainer.style.display = 'none';
    }
    farmingWeatherContainer.style.display = 'none';

    // Scroll to error message
    weatherErrorAlert.scrollIntoView({ behavior: 'smooth' });
  }

  // Hide error message
  function hideError() {
    weatherErrorAlert.style.display = 'none';
  }
});
