<!-- Header with title -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1>Farmer Network</h1>
      <% if (user && user.displayName) { %>
        <div class="greeting-container" style="text-align: right;">
          <div id="greeting-text">
            <span class="time-greeting">Good day</span><span class="me-2">,</span>
            <span class="fw-bold"><%= user.displayName %></span>
          </div>
        </div>
      <% } %>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>

    <% if (typeof success !== 'undefined' && success) { %>
      <div class="alert alert-success" role="alert">
        <%= success %>
      </div>
    <% } %>
  </div>
</div>

<!-- Main content with left navigation and connections -->
<div class="row">
  <!-- Left Side Navigation -->
  <div class="col-md-3 mb-4">
    <div class="dashboard-nav">
      <ul class="nav flex-column">
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/dashboard">
            <i class="bi bi-house-door me-2"></i> Dashboard
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses">
            <i class="bi bi-book me-2"></i> Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/resources">
            <i class="bi bi-file-earmark-text me-2"></i> Resources
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses/my-courses">
            <i class="bi bi-journal-check me-2"></i> My Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/profile">
            <i class="bi bi-person-circle me-2"></i> Profile
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/new">
            <i class="bi bi-cloud-upload me-2"></i> Upload Content
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/my-uploads">
            <i class="bi bi-collection me-2"></i> My Uploads
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/weather">
            <i class="bi bi-cloud-sun me-2"></i> Weather
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link active" href="/connections">
            <i class="bi bi-people-fill me-2"></i> Farmer Network
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/messages">
            <i class="bi bi-chat-dots-fill me-2"></i> Messages
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/auth/logout">
            <i class="bi bi-box-arrow-right me-2"></i> Logout
          </a>
        </li>
      </ul>
    </div>
  </div>

    <!-- Main content -->
    <div class="col-md-9">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">Farmer Network</h4>
          <a href="/connections/find" class="btn btn-light btn-sm">
            <i class="bi bi-person-plus-fill me-1"></i> Find Farmers
          </a>
        </div>
        <div class="card-body">
          <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger" role="alert">
              <%= error %>
            </div>
          <% } %>

          <% if (typeof success !== 'undefined' && success) { %>
            <div class="alert alert-success" role="alert">
              <%= success %>
            </div>
          <% } %>

          <% if (!connections || connections.length === 0) { %>
            <div class="text-center py-5">
              <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No connections yet</h5>
              <p class="text-muted">Connect with other farmers to share knowledge and experiences</p>
              <a href="/connections/find" class="btn btn-success mt-2">
                <i class="bi bi-person-plus-fill me-1"></i> Find Farmers to Connect With
              </a>
            </div>
          <% } else { %>
            <!-- Connection requests section -->
            <%
              const pendingRequests = connections.filter(conn =>
                conn.status === 'pending' && conn.recipientId === user.uid
              );
            %>

            <% if (pendingRequests.length > 0) { %>
              <div class="mb-4">
                <h5 class="border-bottom pb-2">Connection Requests</h5>
                <div class="row">
                  <% pendingRequests.forEach(connection => { %>
                    <div class="col-md-6 mb-3">
                      <div class="card h-100">
                        <div class="card-body">
                          <div class="d-flex align-items-center">
                            <div class="me-3">
                              <% if (connection.otherUser.photoURL) { %>
                                <img src="<%= connection.otherUser.photoURL %>" alt="<%= connection.otherUser.displayName %>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                              <% } else { %>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                  <i class="bi bi-person-fill text-secondary" style="font-size: 1.5rem;"></i>
                                </div>
                              <% } %>
                            </div>
                            <div>
                              <h6 class="mb-0"><%= connection.otherUser.displayName %></h6>
                              <small class="text-muted">
                                <% if (connection.otherUser.farmName) { %>
                                  <%= connection.otherUser.farmName %>
                                <% } %>
                                <% if (connection.otherUser.location) { %>
                                  <% if (connection.otherUser.farmName) { %> • <% } %>
                                  <%= connection.otherUser.location %>
                                <% } %>
                              </small>
                            </div>
                          </div>
                          <div class="d-flex justify-content-between mt-3">
                            <form action="/connections/accept/<%= connection.id %>" method="POST" class="d-inline">
                              <button type="submit" class="btn btn-success btn-sm">
                                <i class="bi bi-check-lg me-1"></i> Accept
                              </button>
                            </form>
                            <form action="/connections/remove/<%= connection.id %>" method="POST" class="d-inline">
                              <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-x-lg me-1"></i> Decline
                              </button>
                            </form>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% }); %>
                </div>
              </div>
            <% } %>

            <!-- Connections section -->
            <%
              const acceptedConnections = connections.filter(conn => conn.status === 'accepted');
              const sentRequests = connections.filter(conn =>
                conn.status === 'pending' && conn.requesterId === user.uid
              );
            %>

            <h5 class="border-bottom pb-2">My Connections</h5>
            <div class="row">
              <% if (acceptedConnections.length === 0) { %>
                <div class="col-12">
                  <p class="text-muted">You don't have any active connections yet.</p>
                </div>
              <% } %>

              <% acceptedConnections.forEach(connection => { %>
                <div class="col-md-6 mb-3">
                  <div class="card h-100">
                    <div class="card-body">
                      <div class="d-flex align-items-center">
                        <div class="me-3">
                          <% if (connection.otherUser.photoURL) { %>
                            <img src="<%= connection.otherUser.photoURL %>" alt="<%= connection.otherUser.displayName %>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                          <% } else { %>
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                              <i class="bi bi-person-fill text-secondary" style="font-size: 1.5rem;"></i>
                            </div>
                          <% } %>
                        </div>
                        <div>
                          <h6 class="mb-0"><%= connection.otherUser.displayName %></h6>
                          <small class="text-muted">
                            <% if (connection.otherUser.farmName) { %>
                              <%= connection.otherUser.farmName %>
                            <% } %>
                            <% if (connection.otherUser.location) { %>
                              <% if (connection.otherUser.farmName) { %> • <% } %>
                              <%= connection.otherUser.location %>
                            <% } %>
                          </small>
                        </div>
                      </div>
                      <div class="d-flex justify-content-between mt-3">
                        <a href="/messages/conversation/<%= connection.otherUser.uid %>" class="btn btn-primary btn-sm">
                          <i class="bi bi-chat-dots-fill me-1"></i> Message
                        </a>
                        <a href="/connections/user/<%= connection.otherUser.uid %>" class="btn btn-outline-secondary btn-sm">
                          <i class="bi bi-person-fill me-1"></i> View Profile
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              <% }); %>
            </div>

            <!-- Pending sent requests section -->
            <% if (sentRequests.length > 0) { %>
              <h5 class="border-bottom pb-2 mt-4">Pending Requests</h5>
              <div class="row">
                <% sentRequests.forEach(connection => { %>
                  <div class="col-md-6 mb-3">
                    <div class="card h-100">
                      <div class="card-body">
                        <div class="d-flex align-items-center">
                          <div class="me-3">
                            <% if (connection.otherUser.photoURL) { %>
                              <img src="<%= connection.otherUser.photoURL %>" alt="<%= connection.otherUser.displayName %>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                            <% } else { %>
                              <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="bi bi-person-fill text-secondary" style="font-size: 1.5rem;"></i>
                              </div>
                            <% } %>
                          </div>
                          <div>
                            <h6 class="mb-0"><%= connection.otherUser.displayName %></h6>
                            <small class="text-muted">
                              <% if (connection.otherUser.farmName) { %>
                                <%= connection.otherUser.farmName %>
                              <% } %>
                              <% if (connection.otherUser.location) { %>
                                <% if (connection.otherUser.farmName) { %> • <% } %>
                                <%= connection.otherUser.location %>
                              <% } %>
                            </small>
                            <div class="mt-1">
                              <span class="badge bg-warning text-dark">
                                <i class="bi bi-hourglass-split me-1"></i> Pending
                              </span>
                            </div>
                          </div>
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                          <form action="/connections/remove/<%= connection.id %>" method="POST">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                              <i class="bi bi-x-lg me-1"></i> Cancel Request
                            </button>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                <% }); %>
              </div>
            <% } %>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<link rel="stylesheet" href="/css/connections.css">
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize time-based greeting
    updateGreeting();

    // Debug information
    console.log('Connections page loaded');
    console.log('User:', <%= JSON.stringify(user || {}) %>);
    console.log('User data:', <%= JSON.stringify(userData || {}) %>);
    console.log('Connections:', <%= JSON.stringify(connections || []) %>);
  });

  function updateGreeting() {
    const greetingElement = document.querySelector('.time-greeting');
    if (!greetingElement) return;

    const hour = new Date().getHours();
    let greeting = 'Good day';

    if (hour < 12) {
      greeting = 'Good morning';
    } else if (hour < 18) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }

    greetingElement.textContent = greeting;
  }
</script>
