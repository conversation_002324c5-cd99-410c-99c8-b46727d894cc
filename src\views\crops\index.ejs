<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4">Crops for Sale</h1>

    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>

    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <a href="/crops/user/my-crops" class="btn btn-outline" style="color: var(--theme-primary, #4CAF50); border-color: var(--theme-primary, #4CAF50);">
          <i class="bi bi-list-check"></i> My Crops
        </a>
        <a href="/crops/new" class="btn ms-2" style="background-color: var(--theme-primary, #4CAF50); color: white;">
          <i class="bi bi-plus-circle"></i> Add New Crop
        </a>
      </div>

      <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
          Filter by Category
        </button>
        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
          <li><a class="dropdown-item" href="/crops">All Categories</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="/crops/category/vegetables">Vegetables</a></li>
          <li><a class="dropdown-item" href="/crops/category/fruits">Fruits</a></li>
          <li><a class="dropdown-item" href="/crops/category/grains">Grains</a></li>
          <li><a class="dropdown-item" href="/crops/category/herbs">Herbs</a></li>
          <li><a class="dropdown-item" href="/crops/category/other">Other</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <% if (crops && crops.length > 0) { %>
    <% crops.forEach(crop => { %>
      <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
          <% if (crop.imageUrl) { %>
            <img src="<%= crop.imageUrl %>" class="card-img-top" alt="<%= crop.name %>" style="height: 200px; object-fit: cover;">
          <% } else { %>
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
              <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
            </div>
          <% } %>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
              <h5 class="card-title"><%= crop.name %></h5>
              <span class="badge" style="background-color: var(--theme-primary, #4CAF50);">$<%= crop.price.toFixed(2) %>/<%= crop.unit %></span>
            </div>
            <p class="card-text text-muted small">
              <i class="bi bi-geo-alt"></i> <%= crop.location %>
              <span class="mx-2">|</span>
              <i class="bi bi-tag"></i> <%= crop.category %>
            </p>
            <p class="card-text"><%= crop.description %></p>
            <p class="card-text">
              <small class="text-muted">
                Available: <%= crop.quantity %> <%= crop.unit %>(s)
              </small>
            </p>
          </div>
          <div class="card-footer bg-white border-top-0">
            <div class="d-flex justify-content-between align-items-center">
              <small class="text-muted">
                Posted by <%= crop.userName %>
              </small>
              <a href="/crops/<%= crop.id %>" class="btn btn-sm btn-outline" style="color: var(--theme-primary, #4CAF50); border-color: var(--theme-primary, #4CAF50);">View Details</a>
            </div>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <div class="col-md-12">
      <div class="text-center py-5">
        <div class="mb-4">
          <i class="bi bi-basket text-muted" style="font-size: 5rem;"></i>
        </div>
        <h3>No Crops Available Yet</h3>
        <p class="text-muted mb-4">Be the first to add a crop for sale in the marketplace!</p>
        <a href="/crops/new" class="btn btn-lg" style="background-color: var(--theme-primary, #4CAF50); color: white;">
          <i class="bi bi-cart-plus me-2"></i> Add Your First Crop
        </a>
      </div>
    </div>
  <% } %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript functionality here
  });
</script>
