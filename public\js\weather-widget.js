// Weather Widget Functionality

document.addEventListener('DOMContentLoaded', function() {
  console.log('Weather widget script loaded');

  const weatherWidget = document.getElementById('weather-widget');
  const weatherTemp = document.getElementById('weather-widget-temp');
  const weatherLocation = document.getElementById('weather-widget-location');
  const weatherIcon = document.querySelector('#weather-widget i');

  if (!weatherWidget || !weatherTemp || !weatherLocation) {
    console.error('Weather widget elements not found');
    return;
  }

  console.log('Weather widget elements found');

  // Add click event to navigate to weather page
  weatherWidget.addEventListener('click', function() {
    window.location.href = '/weather';
  });

  // Function to get current location
  function getCurrentLocation() {
    console.log('Getting current location');

    if (!navigator.geolocation) {
      console.error('Geolocation not supported');
      fetchWeatherByCity('London'); // Default fallback
      return;
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        console.log('Got geolocation:', position.coords);
        const { latitude, longitude } = position.coords;
        fetchWeatherData(latitude, longitude);
      },
      error => {
        console.error('Geolocation error:', error);
        // Fall back to default location
        fetchWeatherByCity('London');
      }
    );
  }

  // Function to fetch weather data by coordinates
  async function fetchWeatherData(lat, lon) {
    try {
      console.log(`Fetching weather data for coordinates: ${lat}, ${lon}`);
      const response = await fetch(`/weather/api/widget?lat=${lat}&lon=${lon}`);

      if (!response.ok) {
        throw new Error('Failed to fetch weather data');
      }

      const data = await response.json();
      console.log('Weather data received:', data);
      updateWeatherWidget(data);
    } catch (error) {
      console.error('Error fetching weather:', error);
      showWeatherError();
    }
  }

  // Function to fetch weather data by city
  async function fetchWeatherByCity(city) {
    try {
      console.log(`Fetching weather data for city: ${city}`);
      const response = await fetch(`/weather/api/widget/city?city=${encodeURIComponent(city)}`);

      if (!response.ok) {
        throw new Error('Failed to fetch weather data');
      }

      const data = await response.json();
      console.log('Weather data received:', data);
      updateWeatherWidget(data);
    } catch (error) {
      console.error('Error fetching weather:', error);
      showWeatherError();
    }
  }

  // Function to update weather widget with data
  function updateWeatherWidget(data) {
    if (!data) {
      console.error('No weather data received');
      showWeatherError();
      return;
    }

    console.log('Updating weather widget with data:', data);

    // Safely update temperature
    if (data.temperature !== undefined && data.temperature !== null) {
      weatherTemp.textContent = `${data.temperature}°C`;
    } else {
      weatherTemp.textContent = '--°C';
    }

    // Safely update location
    if (data.location) {
      weatherLocation.textContent = data.location;
    } else {
      weatherLocation.textContent = 'Unknown location';
    }

    // Update weather icon based on conditions
    if (weatherIcon && data.icon) {
      const iconClass = getWeatherIconClass(data.icon);
      weatherIcon.className = iconClass;
    }
  }

  // Function to get appropriate weather icon class
  function getWeatherIconClass(iconCode) {
    if (!iconCode) return 'bi bi-cloud fs-4';

    // Map OpenWeatherMap icon codes to Bootstrap icons
    const iconMap = {
      '01d': 'bi bi-sun fs-4', // clear sky day
      '01n': 'bi bi-moon fs-4', // clear sky night
      '02d': 'bi bi-cloud-sun fs-4', // few clouds day
      '02n': 'bi bi-cloud-moon fs-4', // few clouds night
      '03d': 'bi bi-cloud fs-4', // scattered clouds
      '03n': 'bi bi-cloud fs-4', // scattered clouds
      '04d': 'bi bi-clouds fs-4', // broken clouds
      '04n': 'bi bi-clouds fs-4', // broken clouds
      '09d': 'bi bi-cloud-drizzle fs-4', // shower rain
      '09n': 'bi bi-cloud-drizzle fs-4', // shower rain
      '10d': 'bi bi-cloud-rain fs-4', // rain day
      '10n': 'bi bi-cloud-rain fs-4', // rain night
      '11d': 'bi bi-cloud-lightning fs-4', // thunderstorm
      '11n': 'bi bi-cloud-lightning fs-4', // thunderstorm
      '13d': 'bi bi-snow fs-4', // snow
      '13n': 'bi bi-snow fs-4', // snow
      '50d': 'bi bi-cloud-fog fs-4', // mist
      '50n': 'bi bi-cloud-fog fs-4' // mist
    };

    return iconMap[iconCode] || 'bi bi-cloud fs-4';
  }

  // Simple error handling
  function showWeatherError() {
    console.error('Weather widget error');
    weatherTemp.textContent = '--°C';
    weatherLocation.textContent = 'Weather unavailable';

    // Show a subtle error indicator
    if (weatherIcon) {
      weatherIcon.className = 'bi bi-exclamation-triangle fs-4 text-warning';
    }
  }

  // Initialize weather widget
  getCurrentLocation();
});
