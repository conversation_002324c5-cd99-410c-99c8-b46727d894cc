/* PWA-Optimized Styles for Sustainable Farming */

/* Root Variables for PWA */
:root {
  --pwa-primary: #8BC34A;
  --pwa-primary-dark: #689F38;
  --pwa-secondary: #4CAF50;
  --pwa-accent: #FFC107;
  --pwa-background: #F5F5F5;
  --pwa-surface: #FFFFFF;
  --pwa-text: #212121;
  --pwa-text-secondary: #757575;
  --pwa-border: #E0E0E0;
  --pwa-shadow: rgba(0, 0, 0, 0.1);
  --pwa-radius: 12px;
  --pwa-spacing: 16px;
  --pwa-header-height: 56px;
  --pwa-bottom-nav-height: 60px;
}

/* PWA Base Styles */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  color: var(--pwa-text);
  background-color: var(--pwa-background);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* PWA Container */
.pwa-container {
  max-width: 100%;
  margin: 0 auto;
  padding: var(--pwa-spacing);
  min-height: 100vh;
}

/* PWA Header */
.pwa-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--pwa-header-height);
  background: var(--pwa-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--pwa-spacing);
  z-index: 1000;
  box-shadow: 0 2px 8px var(--pwa-shadow);
}

.pwa-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.pwa-header-actions {
  display: flex;
  gap: 8px;
}

.pwa-header-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.pwa-header-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* PWA Content */
.pwa-content {
  margin-top: var(--pwa-header-height);
  margin-bottom: var(--pwa-bottom-nav-height);
  padding: var(--pwa-spacing);
  min-height: calc(100vh - var(--pwa-header-height) - var(--pwa-bottom-nav-height));
}

/* PWA Cards */
.pwa-card {
  background: var(--pwa-surface);
  border-radius: var(--pwa-radius);
  box-shadow: 0 2px 8px var(--pwa-shadow);
  margin-bottom: var(--pwa-spacing);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.pwa-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--pwa-shadow);
}

.pwa-card-header {
  padding: var(--pwa-spacing);
  border-bottom: 1px solid var(--pwa-border);
}

.pwa-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--pwa-text);
}

.pwa-card-content {
  padding: var(--pwa-spacing);
}

.pwa-card-actions {
  padding: var(--pwa-spacing);
  border-top: 1px solid var(--pwa-border);
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* PWA Buttons */
.pwa-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--pwa-radius);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly minimum */
  gap: 8px;
}

.pwa-btn-primary {
  background: var(--pwa-primary);
  color: white;
}

.pwa-btn-primary:hover {
  background: var(--pwa-primary-dark);
  color: white;
}

.pwa-btn-secondary {
  background: var(--pwa-surface);
  color: var(--pwa-text);
  border: 1px solid var(--pwa-border);
}

.pwa-btn-secondary:hover {
  background: var(--pwa-background);
  color: var(--pwa-text);
}

.pwa-btn-icon {
  background: transparent;
  color: var(--pwa-text-secondary);
  padding: 8px;
  min-height: 40px;
  width: 40px;
  border-radius: 50%;
}

.pwa-btn-icon:hover {
  background: var(--pwa-background);
}

/* PWA Bottom Navigation */
.pwa-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--pwa-bottom-nav-height);
  background: var(--pwa-surface);
  border-top: 1px solid var(--pwa-border);
  display: flex;
  z-index: 1000;
}

.pwa-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--pwa-text-secondary);
  font-size: 0.75rem;
  padding: 8px 4px;
  transition: color 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.pwa-nav-item.active {
  color: var(--pwa-primary);
}

.pwa-nav-item:hover {
  color: var(--pwa-primary);
  text-decoration: none;
}

.pwa-nav-icon {
  font-size: 1.25rem;
  margin-bottom: 2px;
}

/* PWA Form Elements */
.pwa-form-group {
  margin-bottom: var(--pwa-spacing);
}

.pwa-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--pwa-text);
}

.pwa-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--pwa-border);
  border-radius: var(--pwa-radius);
  font-size: 1rem;
  background: var(--pwa-surface);
  color: var(--pwa-text);
  transition: border-color 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.pwa-input:focus {
  outline: none;
  border-color: var(--pwa-primary);
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

.pwa-textarea {
  resize: vertical;
  min-height: 100px;
}

/* PWA Lists */
.pwa-list {
  background: var(--pwa-surface);
  border-radius: var(--pwa-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px var(--pwa-shadow);
}

.pwa-list-item {
  padding: var(--pwa-spacing);
  border-bottom: 1px solid var(--pwa-border);
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--pwa-text);
  transition: background-color 0.2s ease;
  min-height: 56px; /* Touch-friendly */
}

.pwa-list-item:last-child {
  border-bottom: none;
}

.pwa-list-item:hover {
  background: var(--pwa-background);
  text-decoration: none;
  color: var(--pwa-text);
}

.pwa-list-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  background: var(--pwa-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

.pwa-list-content {
  flex: 1;
}

.pwa-list-title {
  font-weight: 600;
  margin: 0 0 4px 0;
}

.pwa-list-subtitle {
  color: var(--pwa-text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

/* PWA Responsive Design */
@media (max-width: 768px) {
  .pwa-container {
    padding: 8px;
  }
  
  .pwa-content {
    padding: 8px;
  }
  
  .pwa-card {
    margin-bottom: 8px;
  }
  
  .pwa-card-header,
  .pwa-card-content,
  .pwa-card-actions {
    padding: 12px;
  }
}

/* PWA Standalone Mode */
@media (display-mode: standalone) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  .pwa-header {
    padding-top: env(safe-area-inset-top);
    height: calc(var(--pwa-header-height) + env(safe-area-inset-top));
  }
  
  .pwa-content {
    margin-top: calc(var(--pwa-header-height) + env(safe-area-inset-top));
  }
  
  .pwa-bottom-nav {
    padding-bottom: env(safe-area-inset-bottom);
    height: calc(var(--pwa-bottom-nav-height) + env(safe-area-inset-bottom));
  }
}

/* PWA Animations */
@keyframes pwa-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pwa-fade-in {
  animation: pwa-fade-in 0.3s ease-out;
}

/* PWA Loading States */
.pwa-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--pwa-text-secondary);
}

.pwa-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--pwa-border);
  border-top: 2px solid var(--pwa-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
