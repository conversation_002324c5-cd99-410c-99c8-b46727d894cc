/**
 * Market Trends Analysis JavaScript
 * Handles advanced analysis features for the market trends page
 */

// DOM elements
const heatmapBtn = document.getElementById('heatmapBtn');
const analysisBtn = document.getElementById('analysisBtn');
const forecastBtn = document.getElementById('forecastBtn');
const heatmapCard = document.getElementById('heatmapCard');
const historicalAnalysisCard = document.getElementById('historicalAnalysisCard');
const forecastCard = document.getElementById('forecastCard');
const heatmapByLocationBtn = document.getElementById('heatmapByLocationBtn');
const heatmapByCropBtn = document.getElementById('heatmapByCropBtn');
const trendAnalysisBtn = document.getElementById('trendAnalysisBtn');
const seasonalAnalysisBtn = document.getElementById('seasonalAnalysisBtn');
const volatilityAnalysisBtn = document.getElementById('volatilityAnalysisBtn');
const shortTermForecastBtn = document.getElementById('shortTermForecastBtn');
const longTermForecastBtn = document.getElementById('longTermForecastBtn');
const exportDataBtn = document.getElementById('exportDataBtn');
const savePreferencesBtn = document.getElementById('savePreferencesBtn');

// Chart instances
let heatmapChart = null;
let historicalAnalysisChart = null;
let forecastChart = null;

// Initialize the analysis features
document.addEventListener('DOMContentLoaded', () => {
  // Set up event listeners for chart type buttons
  setupChartTypeButtons();

  // Set up event listeners for analysis type buttons
  setupAnalysisTypeButtons();

  // Set up event listeners for export functionality
  setupExportFunctionality();

  // Set up event listeners for user preferences
  setupPreferencesFunctionality();

  // Load user preferences
  loadUserPreferences();
});

// Set up event listeners for chart type buttons
function setupChartTypeButtons() {
  // Heatmap button
  heatmapBtn.addEventListener('click', () => {
    // Hide other charts
    document.getElementById('priceChart').parentElement.parentElement.classList.add('d-none');
    document.getElementById('marketComparisonChart').parentElement.parentElement.classList.add('d-none');
    historicalAnalysisCard.classList.add('d-none');
    forecastCard.classList.add('d-none');

    // Show heatmap
    heatmapCard.classList.remove('d-none');

    // Update active button
    setActiveButton(heatmapBtn);

    // Render heatmap
    renderHeatmap();
  });

  // Analysis button
  analysisBtn.addEventListener('click', () => {
    // Hide other charts
    document.getElementById('priceChart').parentElement.parentElement.classList.add('d-none');
    document.getElementById('marketComparisonChart').parentElement.parentElement.classList.add('d-none');
    heatmapCard.classList.add('d-none');
    forecastCard.classList.add('d-none');

    // Show historical analysis
    historicalAnalysisCard.classList.remove('d-none');

    // Update active button
    setActiveButton(analysisBtn);

    // Render historical analysis
    renderHistoricalAnalysis();
  });

  // Forecast button
  forecastBtn.addEventListener('click', () => {
    // Hide other charts
    document.getElementById('priceChart').parentElement.parentElement.classList.add('d-none');
    document.getElementById('marketComparisonChart').parentElement.parentElement.classList.add('d-none');
    heatmapCard.classList.add('d-none');
    historicalAnalysisCard.classList.add('d-none');

    // Show forecast
    forecastCard.classList.remove('d-none');

    // Update active button
    setActiveButton(forecastBtn);

    // Render forecast
    renderForecast();
  });

  // Line chart button
  lineChartBtn.addEventListener('click', () => {
    // Hide advanced charts
    heatmapCard.classList.add('d-none');
    historicalAnalysisCard.classList.add('d-none');
    forecastCard.classList.add('d-none');

    // Show basic charts
    document.getElementById('priceChart').parentElement.parentElement.classList.remove('d-none');
    document.getElementById('marketComparisonChart').parentElement.parentElement.classList.remove('d-none');

    // Update active button
    setActiveButton(lineChartBtn);

    // Update chart type
    chartType = 'line';
    renderPriceChart();
  });

  // Bar chart button
  barChartBtn.addEventListener('click', () => {
    // Hide advanced charts
    heatmapCard.classList.add('d-none');
    historicalAnalysisCard.classList.add('d-none');
    forecastCard.classList.add('d-none');

    // Show basic charts
    document.getElementById('priceChart').parentElement.parentElement.classList.remove('d-none');
    document.getElementById('marketComparisonChart').parentElement.parentElement.classList.remove('d-none');

    // Update active button
    setActiveButton(barChartBtn);

    // Update chart type
    chartType = 'bar';
    renderPriceChart();
  });
}

// Set up event listeners for analysis type buttons
function setupAnalysisTypeButtons() {
  // Heatmap by location button
  heatmapByLocationBtn.addEventListener('click', () => {
    setActiveButton(heatmapByLocationBtn, [heatmapByCropBtn]);
    renderHeatmap('location');
  });

  // Heatmap by crop button
  heatmapByCropBtn.addEventListener('click', () => {
    setActiveButton(heatmapByCropBtn, [heatmapByLocationBtn]);
    renderHeatmap('crop');
  });

  // Trend analysis button
  trendAnalysisBtn.addEventListener('click', () => {
    setActiveButton(trendAnalysisBtn, [seasonalAnalysisBtn, volatilityAnalysisBtn]);
    renderHistoricalAnalysis('trend');
  });

  // Seasonal analysis button
  seasonalAnalysisBtn.addEventListener('click', () => {
    setActiveButton(seasonalAnalysisBtn, [trendAnalysisBtn, volatilityAnalysisBtn]);
    renderHistoricalAnalysis('seasonal');
  });

  // Volatility analysis button
  volatilityAnalysisBtn.addEventListener('click', () => {
    setActiveButton(volatilityAnalysisBtn, [trendAnalysisBtn, seasonalAnalysisBtn]);
    renderHistoricalAnalysis('volatility');
  });

  // Short-term forecast button
  shortTermForecastBtn.addEventListener('click', () => {
    setActiveButton(shortTermForecastBtn, [longTermForecastBtn]);
    renderForecast('short');
  });

  // Long-term forecast button
  longTermForecastBtn.addEventListener('click', () => {
    setActiveButton(longTermForecastBtn, [shortTermForecastBtn]);
    renderForecast('long');
  });
}

// Set up event listeners for export functionality
function setupExportFunctionality() {
  // Export data button in modal
  document.getElementById('exportDataBtn').addEventListener('click', () => {
    const format = document.querySelector('input[name="exportFormat"]:checked').value;
    const fileName = document.getElementById('fileName').value || 'market-trends-export';
    const exportCurrentView = document.getElementById('exportCurrentView').checked;
    const exportAllData = document.getElementById('exportAllData').checked;
    const exportChartData = document.getElementById('exportChartData').checked;
    const includeMetadata = document.getElementById('includeMetadata').checked;
    const includeStatistics = document.getElementById('includeStatistics').checked;

    // Build query parameters
    const params = new URLSearchParams({
      crop: exportCurrentView ? selectedCrop : 'all',
      location: exportCurrentView ? selectedLocation : 'all',
      timeRange: selectedTimeRange,
      chartDataOnly: exportChartData,
      includeMetadata,
      includeStatistics
    });

    // Determine export URL based on format
    let exportUrl;
    switch (format) {
      case 'csv':
        exportUrl = `/market-trends/api/export/csv?${params.toString()}`;
        break;
      case 'json':
        exportUrl = `/market-trends/api/export/json?${params.toString()}`;
        break;
      case 'excel':
        exportUrl = `/market-trends/api/export/excel?${params.toString()}`;
        break;
      default:
        exportUrl = `/market-trends/api/export/csv?${params.toString()}`;
    }

    // Trigger download
    window.open(exportUrl, '_blank');

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
    modal.hide();
  });
}

// Set up event listeners for user preferences
function setupPreferencesFunctionality() {
  // Save preferences button
  savePreferencesBtn.addEventListener('click', async () => {
    const defaultCrop = document.getElementById('defaultCrop').value;
    const defaultLocation = document.getElementById('defaultLocation').value;
    const defaultTimeRange = document.getElementById('defaultTimeRange').value;
    const chartType = document.getElementById('defaultChartType').value;
    const theme = document.querySelector('input[name="theme"]:checked').value;
    const autoRefresh = document.getElementById('autoRefresh').checked;
    const refreshInterval = parseInt(document.getElementById('refreshInterval').value) || 5;

    // Save preferences to server
    try {
      const response = await fetch('/market-trends/api/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          defaultCrop,
          defaultLocation,
          defaultTimeRange,
          chartType,
          theme,
          autoRefresh,
          refreshInterval
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save preferences');
      }

      // Apply preferences
      applyPreferences({
        defaultCrop,
        defaultLocation,
        defaultTimeRange,
        chartType,
        theme,
        autoRefresh,
        refreshInterval
      });

      // Close the modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('preferencesModal'));
      modal.hide();

      // Show success message
      showToast('Preferences saved successfully');
    } catch (error) {
      console.error('Error saving preferences:', error);
      showToast('Failed to save preferences', 'error');
    }
  });
}

// Load user preferences
async function loadUserPreferences() {
  try {
    const response = await fetch('/market-trends/api/preferences');

    if (!response.ok) {
      throw new Error('Failed to load preferences');
    }

    const preferences = await response.json();

    // Populate preferences form
    document.getElementById('defaultCrop').value = preferences.defaultCrop || 'all';
    document.getElementById('defaultLocation').value = preferences.defaultLocation || 'all';
    document.getElementById('defaultTimeRange').value = preferences.defaultTimeRange || '7days';
    document.getElementById('defaultChartType').value = preferences.chartType || 'line';

    const themeRadio = document.querySelector(`input[name="theme"][value="${preferences.theme || 'light'}"]`);
    if (themeRadio) themeRadio.checked = true;

    document.getElementById('autoRefresh').checked = preferences.autoRefresh || false;
    document.getElementById('refreshInterval').value = preferences.refreshInterval || 5;

    // Apply preferences
    applyPreferences(preferences);
  } catch (error) {
    console.error('Error loading preferences:', error);
  }
}

// Apply user preferences
function applyPreferences(preferences) {
  // Apply default filters if no filters are selected
  if (cropSelect.value === 'all' && locationSelect.value === 'all' && timeRangeSelect.value === '7days') {
    cropSelect.value = preferences.defaultCrop || 'all';
    locationSelect.value = preferences.defaultLocation || 'all';
    timeRangeSelect.value = preferences.defaultTimeRange || '7days';

    // Update selected values
    selectedCrop = cropSelect.value;
    selectedLocation = locationSelect.value;
    selectedTimeRange = timeRangeSelect.value;

    // Reload market data
    loadMarketData();
  }

  // Apply chart type
  if (preferences.chartType) {
    switch (preferences.chartType) {
      case 'line':
        lineChartBtn.click();
        break;
      case 'bar':
        barChartBtn.click();
        break;
      case 'heatmap':
        heatmapBtn.click();
        break;
      case 'analysis':
        analysisBtn.click();
        break;
      case 'forecast':
        forecastBtn.click();
        break;
    }
  }

  // Apply theme
  if (preferences.theme) {
    applyTheme(preferences.theme);
  }

  // Apply auto-refresh
  if (preferences.autoRefresh) {
    startAutoRefresh(preferences.refreshInterval || 5);
  }
}

// Apply theme
function applyTheme(theme) {
  const body = document.body;

  // Remove existing theme classes
  body.classList.remove('theme-light', 'theme-dark');

  // Apply selected theme
  if (theme === 'dark') {
    body.classList.add('theme-dark');
  } else if (theme === 'light') {
    body.classList.add('theme-light');
  } else if (theme === 'system') {
    // Check system preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      body.classList.add('theme-dark');
    } else {
      body.classList.add('theme-light');
    }
  }
}

// Start auto-refresh
function startAutoRefresh(interval) {
  // Clear existing interval
  if (window.autoRefreshInterval) {
    clearInterval(window.autoRefreshInterval);
  }

  // Set new interval
  window.autoRefreshInterval = setInterval(() => {
    loadMarketData();
  }, interval * 60 * 1000); // Convert minutes to milliseconds
}

// Set active button
function setActiveButton(activeButton, buttonGroup = [lineChartBtn, barChartBtn, heatmapBtn, analysisBtn, forecastBtn]) {
  buttonGroup.forEach(button => {
    button.classList.remove('active');
  });

  activeButton.classList.add('active');
}

// Show toast message
function showToast(message, type = 'success') {
  // Create toast container if it doesn't exist
  let toastContainer = document.querySelector('.toast-container');

  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(toastContainer);
  }

  // Create toast element
  const toastEl = document.createElement('div');
  toastEl.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
  toastEl.setAttribute('role', 'alert');
  toastEl.setAttribute('aria-live', 'assertive');
  toastEl.setAttribute('aria-atomic', 'true');

  toastEl.innerHTML = `
    <div class="d-flex">
      <div class="toast-body">
        ${message}
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
  `;

  // Add toast to container
  toastContainer.appendChild(toastEl);

  // Initialize and show toast
  const toast = new bootstrap.Toast(toastEl, { autohide: true, delay: 3000 });
  toast.show();
}

// Render heatmap
async function renderHeatmap(type = 'crop') {
  try {
    // Destroy existing chart if it exists
    if (heatmapChart) {
      heatmapChart.destroy();
    }

    // Show loading indicator
    document.getElementById('heatmapChart').innerHTML = `
      <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <span class="ms-2">Loading heatmap data...</span>
      </div>
    `;

    // Fetch market data if not already available
    if (!marketData || marketData.length === 0) {
      await loadMarketData();
    }

    // Prepare data for heatmap
    let heatmapData;

    if (type === 'crop') {
      heatmapData = prepareHeatmapDataByCrop(marketData);
    } else {
      heatmapData = prepareHeatmapDataByLocation(marketData);
    }

    // Create heatmap chart
    const options = {
      series: heatmapData.series,
      chart: {
        height: 400,
        type: 'heatmap',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      colors: ["#4CAF50"],
      title: {
        text: type === 'crop' ? 'Price Heatmap by Crop' : 'Price Heatmap by Location'
      },
      xaxis: {
        type: 'category',
        categories: heatmapData.categories
      },
      plotOptions: {
        heatmap: {
          shadeIntensity: 0.5,
          colorScale: {
            ranges: [
              {
                from: heatmapData.min,
                to: heatmapData.min + (heatmapData.max - heatmapData.min) * 0.33,
                color: '#4CAF50',
                name: 'Low'
              },
              {
                from: heatmapData.min + (heatmapData.max - heatmapData.min) * 0.33,
                to: heatmapData.min + (heatmapData.max - heatmapData.min) * 0.66,
                color: '#FFC107',
                name: 'Medium'
              },
              {
                from: heatmapData.min + (heatmapData.max - heatmapData.min) * 0.66,
                to: heatmapData.max,
                color: '#F44336',
                name: 'High'
              }
            ]
          }
        }
      },
      tooltip: {
        y: {
          formatter: function(value) {
            return '$' + value.toFixed(2);
          }
        }
      }
    };

    // Clear loading indicator
    document.getElementById('heatmapChart').innerHTML = '';

    // Create chart
    heatmapChart = new ApexCharts(document.getElementById('heatmapChart'), options);
    heatmapChart.render();
  } catch (error) {
    console.error('Error rendering heatmap:', error);
    document.getElementById('heatmapChart').innerHTML = `
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        Failed to render heatmap: ${error.message}
      </div>
    `;
  }
}

// Prepare heatmap data by crop
function prepareHeatmapDataByCrop(data) {
  // Get unique crops and locations
  const crops = [...new Set(data.map(item => item.crop))];
  const locations = [...new Set(data.map(item => item.location))];

  // Format names
  const formatName = (str) => str.charAt(0).toUpperCase() + str.slice(1).replace(/-/g, ' ');

  // Prepare series data
  const series = locations.map(location => {
    const locationData = crops.map(crop => {
      const item = data.find(d => d.crop === crop && d.location === location);
      return {
        x: formatName(crop),
        y: item ? item.currentPrice : 0
      };
    });

    return {
      name: formatName(location),
      data: locationData
    };
  });

  // Find min and max values
  let min = Infinity;
  let max = -Infinity;

  data.forEach(item => {
    if (item.currentPrice < min) min = item.currentPrice;
    if (item.currentPrice > max) max = item.currentPrice;
  });

  return {
    series,
    categories: crops.map(formatName),
    min,
    max
  };
}

// Prepare heatmap data by location
function prepareHeatmapDataByLocation(data) {
  // Get unique crops and locations
  const crops = [...new Set(data.map(item => item.crop))];
  const locations = [...new Set(data.map(item => item.location))];

  // Format names
  const formatName = (str) => str.charAt(0).toUpperCase() + str.slice(1).replace(/-/g, ' ');

  // Prepare series data
  const series = crops.map(crop => {
    const cropData = locations.map(location => {
      const item = data.find(d => d.crop === crop && d.location === location);
      return {
        x: formatName(location),
        y: item ? item.currentPrice : 0
      };
    });

    return {
      name: formatName(crop),
      data: cropData
    };
  });

  // Find min and max values
  let min = Infinity;
  let max = -Infinity;

  data.forEach(item => {
    if (item.currentPrice < min) min = item.currentPrice;
    if (item.currentPrice > max) max = item.currentPrice;
  });

  return {
    series,
    categories: locations.map(formatName),
    min,
    max
  };
}

// Render historical analysis
async function renderHistoricalAnalysis(type = 'trend') {
  try {
    // Destroy existing chart if it exists
    if (historicalAnalysisChart) {
      historicalAnalysisChart.destroy();
    }

    // Show loading indicator
    document.getElementById('historicalAnalysisChart').innerHTML = `
      <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <span class="ms-2">Loading analysis data...</span>
      </div>
    `;

    // Show/hide legend items based on analysis type
    document.querySelector('.seasonal-legend').classList.toggle('d-none', type !== 'seasonal');
    document.querySelector('.volatility-legend').classList.toggle('d-none', type !== 'volatility');

    // Fetch analysis data from API
    let endpoint;
    switch (type) {
      case 'trend':
        endpoint = '/market-trends/api/analysis/trend';
        break;
      case 'seasonal':
        endpoint = '/market-trends/api/analysis/seasonality';
        break;
      case 'volatility':
        endpoint = '/market-trends/api/analysis/volatility';
        break;
      default:
        endpoint = '/market-trends/api/analysis/trend';
    }

    // Build query parameters
    const params = new URLSearchParams({
      crop: selectedCrop,
      location: selectedLocation,
      timeRange: selectedTimeRange
    });

    // Fetch analysis data
    const response = await fetch(`${endpoint}?${params.toString()}`);

    if (!response.ok) {
      throw new Error('Failed to fetch analysis data');
    }

    const analysisData = await response.json();

    // Prepare chart options based on analysis type
    let options;

    switch (type) {
      case 'trend':
        options = prepareTrendAnalysisOptions(analysisData);
        break;
      case 'seasonal':
        options = prepareSeasonalAnalysisOptions(analysisData);
        break;
      case 'volatility':
        options = prepareVolatilityAnalysisOptions(analysisData);
        break;
      default:
        options = prepareTrendAnalysisOptions(analysisData);
    }

    // Clear loading indicator
    document.getElementById('historicalAnalysisChart').innerHTML = '';

    // Create chart
    historicalAnalysisChart = new ApexCharts(document.getElementById('historicalAnalysisChart'), options);
    historicalAnalysisChart.render();
  } catch (error) {
    console.error(`Error rendering ${type} analysis:`, error);
    document.getElementById('historicalAnalysisChart').innerHTML = `
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        Failed to render analysis: ${error.message}
      </div>
    `;
  }
}

// Render forecast
async function renderForecast(term = 'short') {
  try {
    // Destroy existing chart if it exists
    if (forecastChart) {
      forecastChart.destroy();
    }

    // Show loading indicator
    document.getElementById('forecastChart').innerHTML = `
      <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <span class="ms-2">Loading forecast data...</span>
      </div>
    `;

    // Build query parameters
    const params = new URLSearchParams({
      crop: selectedCrop,
      location: selectedLocation,
      timeRange: selectedTimeRange,
      forecastHorizon: term === 'short' ? 7 : 30,
      confidenceLevel: 0.95
    });

    // Fetch forecast data
    const response = await fetch(`/market-trends/api/analysis/forecast?${params.toString()}`);

    if (!response.ok) {
      throw new Error('Failed to fetch forecast data');
    }

    const forecastData = await response.json();

    // Prepare chart options
    const options = prepareForecastOptions(forecastData, term);

    // Clear loading indicator
    document.getElementById('forecastChart').innerHTML = '';

    // Create chart
    forecastChart = new ApexCharts(document.getElementById('forecastChart'), options);
    forecastChart.render();
  } catch (error) {
    console.error(`Error rendering ${term}-term forecast:`, error);
    document.getElementById('forecastChart').innerHTML = `
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        Failed to render forecast: ${error.message}
      </div>
    `;
  }
}

// Prepare trend analysis chart options
function prepareTrendAnalysisOptions(analysisData) {
  // Extract data
  const timeSeriesData = analysisData.timeSeriesData;
  const trendLine = analysisData.regression.trendLine;
  const movingAverage = analysisData.movingAverage;

  // Format dates
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString();
  };

  // Prepare series data
  const series = [
    {
      name: 'Actual Price',
      type: 'line',
      data: timeSeriesData.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Trend Line',
      type: 'line',
      data: trendLine.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Moving Average',
      type: 'line',
      data: movingAverage.map(point => ({
        x: formatDate(point.date),
        y: point.price
      })).filter(point => point.y !== null)
    }
  ];

  // Prepare chart options
  return {
    series,
    chart: {
      height: 400,
      type: 'line',
      toolbar: {
        show: true
      }
    },
    stroke: {
      width: [3, 2, 2],
      curve: 'smooth',
      dashArray: [0, 0, 0]
    },
    colors: ['#4CAF50', '#2196F3', '#FF9800'],
    title: {
      text: 'Price Trend Analysis',
      align: 'left'
    },
    legend: {
      tooltipHoverFormatter: function(val, opts) {
        return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
      }
    },
    markers: {
      size: 0,
      hover: {
        sizeOffset: 6
      }
    },
    xaxis: {
      type: 'category'
    },
    yaxis: {
      title: {
        text: 'Price ($)'
      }
    },
    tooltip: {
      y: {
        formatter: function(value) {
          return '$' + value.toFixed(2);
        }
      }
    },
    grid: {
      borderColor: '#e0e0e0'
    },
    annotations: {
      yaxis: [
        {
          y: timeSeriesData.reduce((sum, point) => sum + point.price, 0) / timeSeriesData.length,
          borderColor: '#999',
          borderWidth: 1,
          borderDash: [5, 5],
          label: {
            text: 'Average Price',
            position: 'left',
            style: {
              color: '#333',
              background: '#f8f9fa'
            }
          }
        }
      ]
    }
  };
}

// Prepare seasonal analysis chart options
function prepareSeasonalAnalysisOptions(analysisData) {
  // Extract data
  const timeSeriesData = analysisData.timeSeriesData;
  const deseasonalizedData = analysisData.deseasonalizedData;
  const seasonalIndices = analysisData.seasonalIndices;

  // Format dates
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString();
  };

  // Prepare series data
  const series = [
    {
      name: 'Actual Price',
      type: 'line',
      data: timeSeriesData.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Deseasonalized Price',
      type: 'line',
      data: deseasonalizedData.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    }
  ];

  // Add seasonal pattern
  const seasonalPattern = [];
  for (let i = 0; i < timeSeriesData.length; i++) {
    const seasonIndex = i % seasonalIndices.length;
    const avgPrice = timeSeriesData.reduce((sum, point) => sum + point.price, 0) / timeSeriesData.length;

    seasonalPattern.push({
      x: formatDate(timeSeriesData[i].date),
      y: avgPrice * seasonalIndices[seasonIndex]
    });
  }

  series.push({
    name: 'Seasonal Pattern',
    type: 'line',
    data: seasonalPattern
  });

  // Prepare chart options
  return {
    series,
    chart: {
      height: 400,
      type: 'line',
      toolbar: {
        show: true
      }
    },
    stroke: {
      width: [3, 2, 2],
      curve: 'smooth',
      dashArray: [0, 0, 0]
    },
    colors: ['#4CAF50', '#2196F3', '#FFC107'],
    title: {
      text: 'Seasonal Analysis',
      align: 'left'
    },
    legend: {
      tooltipHoverFormatter: function(val, opts) {
        return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
      }
    },
    markers: {
      size: 0,
      hover: {
        sizeOffset: 6
      }
    },
    xaxis: {
      type: 'category'
    },
    yaxis: {
      title: {
        text: 'Price ($)'
      }
    },
    tooltip: {
      y: {
        formatter: function(value) {
          return '$' + value.toFixed(2);
        }
      }
    },
    grid: {
      borderColor: '#e0e0e0'
    }
  };
}

// Prepare volatility analysis chart options
function prepareVolatilityAnalysisOptions(analysisData) {
  // Extract data
  const timeSeriesData = analysisData.timeSeriesData;
  const volatility = analysisData.volatility;
  const volatilityBands = analysisData.volatilityBands;

  // Format dates
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString();
  };

  // Prepare series data
  const series = [
    {
      name: 'Price',
      type: 'line',
      data: timeSeriesData.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Upper Band',
      type: 'line',
      data: volatilityBands.upperBand.map(point => ({
        x: formatDate(point.date),
        y: point.price
      })).filter(point => point.y !== null)
    },
    {
      name: 'Lower Band',
      type: 'line',
      data: volatilityBands.lowerBand.map(point => ({
        x: formatDate(point.date),
        y: point.price
      })).filter(point => point.y !== null)
    }
  ];

  // Prepare chart options
  return {
    series,
    chart: {
      height: 400,
      type: 'line',
      toolbar: {
        show: true
      }
    },
    stroke: {
      width: [3, 2, 2],
      curve: 'smooth',
      dashArray: [0, 0, 0]
    },
    colors: ['#4CAF50', '#F44336', '#F44336'],
    title: {
      text: 'Price Volatility Analysis',
      align: 'left'
    },
    legend: {
      tooltipHoverFormatter: function(val, opts) {
        return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
      }
    },
    markers: {
      size: 0,
      hover: {
        sizeOffset: 6
      }
    },
    xaxis: {
      type: 'category'
    },
    yaxis: {
      title: {
        text: 'Price ($)'
      }
    },
    tooltip: {
      y: {
        formatter: function(value) {
          return '$' + value.toFixed(2);
        }
      }
    },
    grid: {
      borderColor: '#e0e0e0'
    },
    fill: {
      type: 'gradient',
      gradient: {
        shade: 'light',
        type: 'vertical',
        shadeIntensity: 0.1,
        inverseColors: false,
        opacityFrom: 0.2,
        opacityTo: 0.1,
        stops: [0, 100]
      }
    }
  };
}

// Prepare forecast chart options
function prepareForecastOptions(forecastData, term) {
  // Extract data
  const timeSeriesData = forecastData.timeSeriesData;
  const forecast = forecastData.forecast;
  const confidenceIntervals = forecastData.confidenceIntervals;

  // Format dates
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString();
  };

  // Prepare series data
  const series = [
    {
      name: 'Historical Price',
      type: 'line',
      data: timeSeriesData.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Forecast',
      type: 'line',
      data: forecast.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Upper Bound',
      type: 'line',
      data: confidenceIntervals.upperBound.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    },
    {
      name: 'Lower Bound',
      type: 'line',
      data: confidenceIntervals.lowerBound.map(point => ({
        x: formatDate(point.date),
        y: point.price
      }))
    }
  ];

  // Prepare chart options
  return {
    series,
    chart: {
      height: 400,
      type: 'line',
      toolbar: {
        show: true
      }
    },
    stroke: {
      width: [3, 3, 1, 1],
      curve: 'smooth',
      dashArray: [0, 0, 3, 3]
    },
    colors: ['#4CAF50', '#2196F3', '#90CAF9', '#90CAF9'],
    title: {
      text: term === 'short' ? 'Short-term Price Forecast' : 'Long-term Price Forecast',
      align: 'left'
    },
    legend: {
      tooltipHoverFormatter: function(val, opts) {
        return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
      }
    },
    markers: {
      size: 0,
      hover: {
        sizeOffset: 6
      }
    },
    xaxis: {
      type: 'category'
    },
    yaxis: {
      title: {
        text: 'Price ($)'
      }
    },
    tooltip: {
      y: {
        formatter: function(value) {
          return '$' + value.toFixed(2);
        }
      }
    },
    grid: {
      borderColor: '#e0e0e0'
    },
    fill: {
      type: 'gradient',
      gradient: {
        shade: 'light',
        type: 'vertical',
        shadeIntensity: 0.1,
        inverseColors: false,
        opacityFrom: 0.2,
        opacityTo: 0.1,
        stops: [0, 100]
      }
    },
    annotations: {
      xaxis: [
        {
          x: formatDate(timeSeriesData[timeSeriesData.length - 1].date),
          borderColor: '#999',
          borderWidth: 1,
          borderDash: [5, 5],
          label: {
            text: 'Forecast Start',
            position: 'top',
            style: {
              color: '#333',
              background: '#f8f9fa'
            }
          }
        }
      ]
    }
  };
}
