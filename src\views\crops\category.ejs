<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/crops">Crops</a></li>
        <li class="breadcrumb-item active" aria-current="page"><%= category.charAt(0).toUpperCase() + category.slice(1) %></li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4"><%= category.charAt(0).toUpperCase() + category.slice(1) %> for Sale</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
    
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <a href="/crops/user/my-crops" class="btn btn-outline-success">
          <i class="bi bi-list-check"></i> My Crops
        </a>
        <a href="/crops/new" class="btn btn-success ms-2">
          <i class="bi bi-plus-circle"></i> Add New Crop
        </a>
      </div>
      
      <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
          Filter by Category
        </button>
        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
          <li><a class="dropdown-item" href="/crops">All Categories</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="/crops/category/vegetables">Vegetables</a></li>
          <li><a class="dropdown-item" href="/crops/category/fruits">Fruits</a></li>
          <li><a class="dropdown-item" href="/crops/category/grains">Grains</a></li>
          <li><a class="dropdown-item" href="/crops/category/herbs">Herbs</a></li>
          <li><a class="dropdown-item" href="/crops/category/other">Other</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <% if (crops && crops.length > 0) { %>
    <% crops.forEach(crop => { %>
      <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
          <% if (crop.imageUrl) { %>
            <img src="<%= crop.imageUrl %>" class="card-img-top" alt="<%= crop.name %>" style="height: 200px; object-fit: cover;">
          <% } else { %>
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
              <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
            </div>
          <% } %>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
              <h5 class="card-title"><%= crop.name %></h5>
              <span class="badge bg-success">$<%= crop.price.toFixed(2) %>/<%= crop.unit %></span>
            </div>
            <p class="card-text text-muted small">
              <i class="bi bi-geo-alt"></i> <%= crop.location %>
            </p>
            <p class="card-text"><%= crop.description %></p>
            <p class="card-text">
              <small class="text-muted">
                Available: <%= crop.quantity %> <%= crop.unit %>(s)
              </small>
            </p>
          </div>
          <div class="card-footer bg-white border-top-0">
            <div class="d-flex justify-content-between align-items-center">
              <small class="text-muted">
                Posted by <%= crop.userName %>
              </small>
              <a href="/crops/<%= crop.id %>" class="btn btn-sm btn-outline-success">View Details</a>
            </div>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <div class="col-md-12">
      <div class="alert alert-info" role="alert">
        No crops available in this category yet. Be the first to add a crop for sale!
      </div>
    </div>
  <% } %>
</div>

<div class="row mt-4">
  <div class="col-md-12">
    <a href="/crops" class="btn btn-outline-secondary">
      <i class="bi bi-arrow-left"></i> Back to All Crops
    </a>
  </div>
</div>
