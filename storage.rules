rules_version = '2';

// Craft rules based on data in your Firestore database
// allow write: if firestore.get(
//    /databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin;
service firebase.storage {
  match /b/{bucket}/o {
    // Allow read/write access to all users under any conditions
    // Warning: **ONLY** use this rule set for testing and development
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
