<div class="container auth-container">
  <div class="row">
    <div class="col-md-12">
      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert" style="border-radius: 10px; border: none;">
          <i class="bi bi-exclamation-triangle-fill me-2"></i>
          <%= error %>
        </div>
      <% } %>

      <% if (typeof message !== 'undefined' && message) { %>
        <div class="alert alert-info" role="alert" style="border-radius: 10px; border: none; background-color: rgba(76, 175, 80, 0.1); color: var(--primary);">
          <i class="bi bi-info-circle-fill me-2"></i>
          <%= message %>
        </div>
      <% } %>
    </div>
  </div>

  <div class="row g-4 align-items-stretch">
    <div class="col-md-6 mb-4">
      <div class="auth-card h-100">
        <div class="auth-header">
          <h3 class="auth-title">Community Content</h3>
          <p class="auth-subtitle mb-0">See what our community is sharing</p>
        </div>
        <div class="card-body">
          <p class="text-muted mb-4">Login or register to interact with content from sustainable farmers around the world.</p>

          <% if (typeof uploads !== 'undefined' && uploads && uploads.length > 0) { %>
            <div class="row g-3" id="content-container">
              <% uploads.slice(0, 6).forEach(upload => { %>
                <div class="col-md-6 mb-3">
                  <div class="card h-100 border-0 shadow-sm hover-shadow">
                    <% if (upload.fileUrl) { %>
                      <%
                        // Determine file type from either the fileType property or the URL extension
                        let isImage = false;
                        let isVideo = false;
                        let isPdf = false;

                        if (upload.fileType) {
                          // If we have a fileType property, use it
                          isImage = upload.fileType.startsWith('image/');
                          isVideo = upload.fileType.startsWith('video/');
                          isPdf = upload.fileType === 'application/pdf';
                        } else {
                          // Otherwise try to determine from URL
                          const url = upload.fileUrl.toLowerCase();
                          isImage = url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.webp') || url.includes('data:image/');
                          isVideo = url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg') || url.includes('data:video/');
                          isPdf = url.endsWith('.pdf') || url.includes('data:application/pdf');
                        }
                      %>

                      <% if (isImage) { %>
                        <img src="<%= upload.fileUrl %>" class="card-img-top" alt="<%= upload.title %>" style="height: 140px; object-fit: cover; border-radius: 12px 12px 0 0;">
                      <% } else if (isVideo) { %>
                        <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 140px; background-color: var(--primary-lightest); border-radius: 12px 12px 0 0;">
                          <i class="bi bi-film" style="font-size: 2.5rem; color: var(--primary);"></i>
                        </div>
                      <% } else if (isPdf) { %>
                        <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 140px; background-color: var(--primary-lightest); border-radius: 12px 12px 0 0;">
                          <i class="bi bi-file-pdf" style="font-size: 2.5rem; color: var(--primary);"></i>
                        </div>
                      <% } else { %>
                        <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 140px; background-color: var(--primary-lightest); border-radius: 12px 12px 0 0;">
                          <i class="bi bi-file-earmark" style="font-size: 2.5rem; color: var(--primary);"></i>
                        </div>
                      <% } %>
                    <% } else { %>
                      <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 140px; background-color: var(--primary-lightest); border-radius: 12px 12px 0 0;">
                        <i class="bi bi-image" style="font-size: 2.5rem; color: var(--primary);"></i>
                      </div>
                    <% } %>
                    <div class="card-body p-3">
                      <h6 class="card-title fw-bold mb-1"><%= upload.title %></h6>
                      <p class="card-text small text-muted mb-2"><%= upload.description.length > 50 ? upload.description.substring(0, 50) + '...' : upload.description %></p>
                      <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                          <a href="/profile/user/<%= upload.userId %>" class="text-decoration-none" title="View <%= upload.userName %>'s profile">
                            <% if (upload.userPhotoURL) { %>
                              <img src="<%= upload.userPhotoURL %>" alt="<%= upload.userName %>" style="width: 24px; height: 24px; border-radius: 50%; object-fit: cover; border: 1px solid #e4e6ea; margin-right: 8px; transition: border-color 0.2s ease;" onmouseover="this.style.borderColor='#42b883'" onmouseout="this.style.borderColor='#e4e6ea'">
                            <% } else { %>
                              <div style="width: 24px; height: 24px; border-radius: 50%; background: linear-gradient(135deg, #42b883, #369870); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; border: 1px solid #e4e6ea; margin-right: 8px; transition: transform 0.2s ease;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="bi bi-person-fill"></i>
                              </div>
                            <% } %>
                          </a>
                          <a href="/profile/user/<%= upload.userId %>" class="text-decoration-none" title="View <%= upload.userName %>'s profile">
                            <small class="text-muted" style="transition: color 0.2s ease;" onmouseover="this.style.color='#42b883'" onmouseout="this.style.color='#6c757d'"><%= upload.userName %></small>
                          </a>
                        </div>
                        <span class="badge" style="background-color: var(--primary-lightest); color: var(--primary); font-weight: 600; padding: 0.35rem 0.65rem; border-radius: 50px;"><%= upload.category %></span>
                      </div>
                    </div>
                  </div>
                </div>
              <% }); %>
            </div>

            <div class="text-center mt-4">
              <p class="text-muted">Login to see more content and interact with the community!</p>
            </div>
          <% } else { %>
            <div class="text-center py-5">
              <div class="mb-4" style="width: 80px; height: 80px; background-color: var(--primary-lightest); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                <i class="bi bi-cloud-upload" style="font-size: 2.5rem; color: var(--primary);"></i>
              </div>
              <h5 class="fw-bold">No Content Uploaded Yet</h5>
              <p class="text-muted">Be the first to share content with the community!</p>
            </div>
          <% } %>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="auth-card h-100">
        <div class="auth-header">
          <h3 class="auth-title">Welcome Back</h3>
          <p class="auth-subtitle mb-0">Login to your account</p>
        </div>
        <div class="card-body">
          <% if (typeof success !== 'undefined') { %>
            <div class="alert alert-success" role="alert" style="border-radius: 10px; border: none;">
              <i class="bi bi-check-circle-fill me-2"></i>
              <%= success %>
            </div>
          <% } %>

          <form action="/auth/login" method="POST" id="loginForm" class="auth-form">
            <div class="mb-4">
              <label for="email" class="form-label">Email address</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-envelope" style="color: var(--primary);"></i>
                </span>
                <input type="email" class="form-control auth-input" id="email" name="email" placeholder="Enter your email" style="border-left: none;" required>
              </div>
            </div>

            <div class="mb-4">
              <label for="password" class="form-label">Password</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-lock" style="color: var(--primary);"></i>
                </span>
                <input type="password" class="form-control auth-input" id="password" name="password" placeholder="Enter your password" style="border-left: none; border-right: none;" required>
                <button class="input-group-text" type="button" id="togglePassword" style="background-color: transparent; border-left: none;">
                  <i class="bi bi-eye" style="color: var(--primary);"></i>
                </button>
              </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe" style="border-color: var(--primary);">
                <label class="form-check-label" for="rememberMe">Remember me</label>
              </div>
              <a href="/auth/forgot-password" class="auth-link">Forgot Password?</a>
            </div>

            <div class="d-grid mb-4">
              <button type="submit" class="auth-btn">
                Login <i class="bi bi-box-arrow-in-right ms-2"></i>
              </button>
            </div>
          </form>

          <div class="text-center">
            <p>Don't have an account? <a href="/auth/register" class="auth-link">Register here</a></p>
          </div>

          <!-- Firebase Client Configuration -->
          <script type="module" src="/js/firebase-client.js"></script>
          <script src="/js/login.js"></script>
        </div>
      </div>
    </div>
  </div>
</div>
