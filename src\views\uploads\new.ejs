<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/uploads">Community Content</a></li>
        <li class="breadcrumb-item active" aria-current="page">Upload New Content</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card shadow-sm">
      <div class="card-header bg-success text-white">
        <h3 class="mb-0">Upload New Content</h3>
      </div>
      <div class="card-body">
        <% if (typeof error !== 'undefined') { %>
          <div class="alert alert-danger" role="alert">
            <%= error %>
          </div>
        <% } %>

        <form action="/uploads" method="POST" id="uploadForm" enctype="multipart/form-data">
          <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" required value="<%= typeof formData !== 'undefined' && formData.title ? formData.title : '' %>">
          </div>

          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" name="description" rows="3" required><%= typeof formData !== 'undefined' && formData.description ? formData.description : '' %></textarea>
          </div>

          <div class="mb-3">
            <label for="category" class="form-label">Category</label>
            <select class="form-select" id="category" name="category" required>
              <option value="" disabled <%= typeof formData === 'undefined' || !formData.category ? 'selected' : '' %>>Select category</option>
              <option value="Crops" <%= typeof formData !== 'undefined' && formData.category === 'Crops' ? 'selected' : '' %>>Crops</option>
              <option value="Farming Techniques" <%= typeof formData !== 'undefined' && formData.category === 'Farming Techniques' ? 'selected' : '' %>>Farming Techniques</option>
              <option value="Other" <%= typeof formData !== 'undefined' && formData.category === 'Other' ? 'selected' : '' %>>Other</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="file" class="form-label">Upload File</label>
            <input type="file" class="form-control" id="file" name="file" accept="image/*,video/*,application/pdf" required>
            <div class="form-text">You can upload images, videos, or PDFs. Maximum file size: 10MB.</div>
          </div>

          <!-- Camera capture for mobile devices -->
          <div class="mb-3">
            <label class="form-label">Or take a photo with your camera</label>
            <div class="d-grid">
              <button type="button" class="btn btn-primary" id="cameraButton">
                <i class="bi bi-camera"></i> Open Camera
              </button>
            </div>
          </div>

          <!-- File preview container -->
          <div id="previewContainer" class="mt-3" style="display: none;">
            <div class="card">
              <div class="card-body">
                <h6 class="card-subtitle mb-2 text-muted">Preview</h6>
                <div id="previewContent" class="text-center">
                  <!-- Preview content will be inserted here by JavaScript -->
                </div>
              </div>
            </div>
          </div>

          <div class="d-grid gap-2 mt-4">
            <button type="submit" class="btn btn-success btn-lg">
              <i class="bi bi-cloud-upload me-2"></i> Upload Content
            </button>
            <a href="/dashboard" class="btn btn-outline-secondary">Cancel</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const fileInput = document.getElementById('file');
    const cameraButton = document.getElementById('cameraButton');
    const previewContainer = document.getElementById('previewContainer');
    const previewContent = document.getElementById('previewContent');
    const uploadForm = document.getElementById('uploadForm');

    // Log to verify elements are found
    console.log('DOM elements loaded:', {
      fileInput: !!fileInput,
      cameraButton: !!cameraButton,
      previewContainer: !!previewContainer,
      uploadForm: !!uploadForm
    });

    // Function to create preview
    function createPreview(file) {
      console.log('Creating preview for file:', file.name, 'type:', file.type);

      // Show preview container
      previewContainer.style.display = 'block';

      // Create a URL for the file
      const url = URL.createObjectURL(file);

      // Create appropriate preview based on file type
      if (file.type.startsWith('image/')) {
        // Image preview
        previewContent.innerHTML = `
          <img src="${url}" class="img-fluid rounded" alt="Image preview" style="max-height: 300px;">
        `;
      } else if (file.type.startsWith('video/')) {
        // Video preview
        previewContent.innerHTML = `
          <div class="ratio ratio-16x9">
            <video controls>
              <source src="${url}" type="${file.type}">
              Your browser does not support the video tag.
            </video>
          </div>
        `;
      } else if (file.type === 'application/pdf') {
        // PDF preview
        previewContent.innerHTML = `
          <div class="text-center">
            <i class="bi bi-file-pdf text-danger" style="font-size: 3rem;"></i>
            <p>PDF file: ${file.name}</p>
          </div>
        `;
      } else {
        // Generic file preview
        previewContent.innerHTML = `
          <div class="text-center">
            <i class="bi bi-file-earmark text-primary" style="font-size: 3rem;"></i>
            <p>${file.name}</p>
          </div>
        `;
      }
    }

    // Event listener for file input
    if (fileInput) {
      fileInput.addEventListener('change', function(event) {
        console.log('File input changed:', this.files);
        if (this.files && this.files[0]) {
          // Check file size (max 10MB)
          if (this.files[0].size > 10 * 1024 * 1024) {
            alert('File is too large. Maximum file size is 10MB.');
            this.value = ''; // Clear the input
            return;
          }

          createPreview(this.files[0]);
        }
      });
    }

    // Event listener for camera button
    if (cameraButton) {
      cameraButton.addEventListener('click', function(event) {
        console.log('Camera button clicked');

        // Set the file input to capture from camera
        fileInput.setAttribute('capture', 'environment');
        fileInput.click();

        // Remove the capture attribute after clicking
        setTimeout(() => {
          fileInput.removeAttribute('capture');
        }, 1000);
      });
    }

    // Form validation
    if (uploadForm) {
      uploadForm.addEventListener('submit', function(event) {
        if (!fileInput.files || !fileInput.files[0]) {
          event.preventDefault();
          alert('Please select a file to upload');
        }
      });
    }
  });
</script>
