<%- include('../partials/header') %>

<div class="container-fluid mt-4">
  <div class="row">
    <!-- Left Sidebar - Conversation List -->
    <div class="col-md-4 col-lg-3">
      <div class="card">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Messages</h5>
          <button class="btn btn-sm btn-light" id="new-message-btn" title="New Message">
            <i class="bi bi-pencil-square"></i>
          </button>
        </div>
        <div class="card-body p-0">
          <!-- Search Box -->
          <div class="p-3 border-bottom">
            <div class="input-group">
              <input type="text" class="form-control" placeholder="Search messages" id="search-conversations">
              <button class="btn btn-outline-success" type="button">
                <i class="bi bi-search"></i>
              </button>
            </div>
          </div>

          <!-- Conversation List -->
          <div class="conversation-list">
            <% if (conversations && conversations.length > 0) { %>
              <% conversations.forEach(conversation => { %>
                <a href="/messaging/<%= conversation.id %>" class="text-decoration-none text-dark">
                  <div class="conversation-item p-3 border-bottom <%= activeConversation && activeConversation.id === conversation.id ? 'active bg-light' : '' %>">
                    <div class="d-flex">
                      <% if (conversation.otherParticipant.photoURL) { %>
                        <img src="<%= conversation.otherParticipant.photoURL %>" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                      <% } else { %>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                          <i class="bi bi-person-fill text-secondary"></i>
                        </div>
                      <% } %>
                      <div class="flex-grow-1">
                        <div class="d-flex justify-content-between">
                          <h6 class="mb-0"><%= conversation.otherParticipant.displayName || 'User' %></h6>
                          <% if (conversation.lastMessageTimestamp) { %>
                            <small class="text-muted">
                              <%= new Date(conversation.lastMessageTimestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                            </small>
                          <% } %>
                        </div>
                        <p class="text-muted small mb-0 text-truncate">
                          <% if (conversation.lastMessage) { %>
                            <% if (conversation.lastMessage.senderId === user.uid) { %>
                              <span class="text-muted">You: </span>
                            <% } %>
                            <%= conversation.lastMessage.text %>
                          <% } else { %>
                            No messages yet
                          <% } %>
                        </p>
                      </div>
                    </div>
                  </div>
                </a>
              <% }); %>
            <% } else { %>
              <div class="text-center py-5">
                <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3">No Conversations Yet</h5>
                <p class="text-muted">Start a new conversation with your connections.</p>
                <button class="btn btn-success" id="start-conversation-btn">New Message</button>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content - Messages -->
    <div class="col-md-8 col-lg-9">
      <% if (activeConversation) { %>
        <!-- Conversation Header -->
        <div class="card mb-0">
          <div class="card-header bg-success text-white">
            <div class="d-flex align-items-center">
              <% if (activeConversation.otherParticipant.photoURL) { %>
                <img src="<%= activeConversation.otherParticipant.photoURL %>" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
              <% } else { %>
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                  <i class="bi bi-person-fill text-secondary"></i>
                </div>
              <% } %>
              <div>
                <h5 class="mb-0"><%= activeConversation.otherParticipant.displayName || 'User' %></h5>
              </div>
            </div>
          </div>
        </div>

        <!-- Messages Container -->
        <div class="card mb-0 border-top-0" style="height: calc(100vh - 250px);">
          <div class="card-body p-3 overflow-auto" id="messages-container">
            <% if (messages && messages.length > 0) { %>
              <% messages.forEach(message => { %>
                <div class="message mb-3 <%= message.senderId === user.uid ? 'text-end' : '' %>">
                  <div class="d-inline-block p-3 rounded <%= message.senderId === user.uid ? 'bg-success text-white' : 'bg-light' %>" style="max-width: 75%;">
                    <p class="mb-0"><%= message.text %></p>
                    <% if (message.attachmentURL) { %>
                      <div class="mt-2">
                        <img src="<%= message.attachmentURL %>" class="img-fluid rounded" style="max-height: 200px;">
                      </div>
                    <% } %>
                    <small class="d-block mt-1 <%= message.senderId === user.uid ? 'text-white-50' : 'text-muted' %>">
                      <%= new Date(message.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                      <% if (message.senderId === user.uid && message.read) { %>
                        <i class="bi bi-check-all ms-1"></i>
                      <% } %>
                    </small>
                  </div>
                </div>
              <% }); %>
            <% } else { %>
              <div class="text-center py-5">
                <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3">No Messages Yet</h5>
                <p class="text-muted">Start the conversation by sending a message below.</p>
              </div>
            <% } %>
          </div>
        </div>

        <!-- Message Input -->
        <div class="card border-top-0">
          <div class="card-body p-3">
            <form id="message-form">
              <div class="input-group">
                <button class="btn btn-outline-secondary" type="button" id="attachment-btn">
                  <i class="bi bi-paperclip"></i>
                </button>
                <input type="text" class="form-control" placeholder="Type a message..." id="message-input">
                <button class="btn btn-success" type="submit">
                  <i class="bi bi-send-fill"></i>
                </button>
              </div>
              <input type="file" id="attachment-input" class="d-none" accept="image/*">
              <div id="attachment-preview" class="mt-2 d-none">
                <div class="position-relative d-inline-block">
                  <img id="attachment-image" class="img-thumbnail" style="max-height: 100px;">
                  <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0" id="remove-attachment-btn">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      <% } else { %>
        <div class="card" style="height: calc(100vh - 150px);">
          <div class="card-body d-flex flex-column align-items-center justify-content-center">
            <i class="bi bi-chat-dots text-muted" style="font-size: 5rem;"></i>
            <h3 class="mt-4">Your Messages</h3>
            <p class="text-muted text-center">Select a conversation from the list or start a new one.</p>
            <button class="btn btn-success mt-3" id="select-contact-btn">New Message</button>
          </div>
        </div>
      <% } %>
    </div>
  </div>
</div>

<!-- New Message Modal -->
<div class="modal fade" id="newMessageModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">New Message</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label for="contact-search" class="form-label">Select a contact:</label>
          <input type="text" class="form-control" id="contact-search" placeholder="Search connections...">
        </div>
        <div id="contacts-list" class="mt-3">
          <!-- Contacts will be loaded here -->
          <div class="text-center py-3">
            <div class="spinner-border text-success" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Scroll to bottom of messages container
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // New message button
    const newMessageBtn = document.getElementById('new-message-btn');
    const startConversationBtn = document.getElementById('start-conversation-btn');
    const selectContactBtn = document.getElementById('select-contact-btn');
    
    const openNewMessageModal = () => {
      // Initialize the modal
      const newMessageModal = new bootstrap.Modal(document.getElementById('newMessageModal'));
      
      // Load contacts
      loadContacts();
      
      // Show the modal
      newMessageModal.show();
    };
    
    if (newMessageBtn) {
      newMessageBtn.addEventListener('click', openNewMessageModal);
    }
    
    if (startConversationBtn) {
      startConversationBtn.addEventListener('click', openNewMessageModal);
    }
    
    if (selectContactBtn) {
      selectContactBtn.addEventListener('click', openNewMessageModal);
    }

    // Message form submission
    const messageForm = document.getElementById('message-form');
    if (messageForm) {
      messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
      });
    }

    // Attachment button
    const attachmentBtn = document.getElementById('attachment-btn');
    const attachmentInput = document.getElementById('attachment-input');
    const attachmentPreview = document.getElementById('attachment-preview');
    const attachmentImage = document.getElementById('attachment-image');
    const removeAttachmentBtn = document.getElementById('remove-attachment-btn');
    
    if (attachmentBtn && attachmentInput) {
      attachmentBtn.addEventListener('click', function() {
        attachmentInput.click();
      });
      
      attachmentInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
          const reader = new FileReader();
          
          reader.onload = function(e) {
            attachmentImage.src = e.target.result;
            attachmentPreview.classList.remove('d-none');
          };
          
          reader.readAsDataURL(this.files[0]);
        }
      });
      
      if (removeAttachmentBtn) {
        removeAttachmentBtn.addEventListener('click', function() {
          attachmentInput.value = '';
          attachmentPreview.classList.add('d-none');
        });
      }
    }
  });

  // Function to load contacts
  function loadContacts() {
    const contactsList = document.getElementById('contacts-list');
    
    // This would be replaced with an actual API call
    fetch('/network/api/connections')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.connections && data.connections.length > 0) {
          let html = '';
          
          data.connections.forEach(connection => {
            html += `
              <div class="contact-item p-2 border-bottom">
                <a href="/messaging/${connection.otherUser.uid}" class="d-flex align-items-center text-decoration-none text-dark">
                  ${connection.otherUser.photoURL 
                    ? `<img src="${connection.otherUser.photoURL}" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">`
                    : `<div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                        <i class="bi bi-person-fill text-secondary"></i>
                      </div>`
                  }
                  <div>
                    <h6 class="mb-0">${connection.otherUser.displayName || 'User'}</h6>
                    <small class="text-muted">${connection.otherUser.headline || 'Sustainable Farmer'}</small>
                  </div>
                </a>
              </div>
            `;
          });
          
          contactsList.innerHTML = html;
        } else {
          contactsList.innerHTML = `
            <div class="text-center py-3">
              <p class="text-muted">No connections found. <a href="/network/search">Find farmers</a> to connect with.</p>
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('Error loading contacts:', error);
        contactsList.innerHTML = `
          <div class="text-center py-3">
            <p class="text-danger">Error loading contacts. Please try again.</p>
          </div>
        `;
      });
  }

  // Function to send a message
  function sendMessage() {
    const messageInput = document.getElementById('message-input');
    const text = messageInput.value.trim();
    
    if (!text) return;
    
    const conversationId = '<%= activeConversation ? activeConversation.id : "" %>';
    
    if (!conversationId) return;
    
    // Clear the input
    messageInput.value = '';
    
    // Add a temporary message to the UI
    addTemporaryMessage(text);
    
    // Send the message to the server
    fetch(`/messaging/api/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ text })
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        alert('Error sending message: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error sending message:', error);
      alert('An error occurred while sending your message. Please try again.');
    });
  }

  // Function to add a temporary message to the UI
  function addTemporaryMessage(text) {
    const messagesContainer = document.getElementById('messages-container');
    
    if (!messagesContainer) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message mb-3 text-end';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'd-inline-block p-3 rounded bg-success text-white';
    messageContent.style.maxWidth = '75%';
    
    const messageText = document.createElement('p');
    messageText.className = 'mb-0';
    messageText.textContent = text;
    
    const messageTime = document.createElement('small');
    messageTime.className = 'd-block mt-1 text-white-50';
    messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageContent.appendChild(messageText);
    messageContent.appendChild(messageTime);
    messageDiv.appendChild(messageContent);
    messagesContainer.appendChild(messageDiv);
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }
</script>

<%- include('../partials/footer') %>
