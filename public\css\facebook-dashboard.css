/* Facebook-style Dashboard with Agricultural Theme */

:root {
  /* Facebook colors with agricultural twist */
  --fb-blue: #1877f2;
  --fb-dark-blue: #166fe5;
  --fb-green: #42b72a;
  --fb-dark-green: #36a420;
  --fb-light-gray: #f0f2f5;
  --fb-gray: #e4e6eb;
  --fb-dark-gray: #65676b;
  --fb-black: #333333; /* Changed from #1c1e21 to match the app's color scheme */

  /* Agricultural theme colors */
  --agri-green: #4CAF50;
  --agri-dark-green: #388E3C;
  --agri-light-green: #8BC34A;
  --agri-brown: #795548;
  --agri-light-brown: #A1887F;
  --agri-beige: #F5F5DC;
  --agri-wheat: #F5DEB3;
  --agri-soil: #8B4513;

  /* Standardized theme colors for consistent application styling */
  --theme-primary: var(--agri-green);
  --theme-primary-dark: var(--agri-dark-green);
  --theme-primary-light: var(--agri-light-green);
  --theme-secondary: var(--agri-brown);
  --theme-secondary-light: var(--agri-light-brown);
  --theme-accent: var(--agri-wheat);
  --theme-background: var(--fb-light-gray);
  --theme-card-bg: white;
  --theme-text-dark: #333333; /* Explicitly set to match the app's color scheme */
  --theme-text-light: white;
  --theme-text-light-muted: rgba(255, 255, 255, 0.8);
  --theme-border: var(--agri-light-green);
}

/* Main Layout */
body.fb-body {
  background-color: var(--fb-light-gray);
  font-family: Helvetica, Arial, sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 56px; /* Add padding for fixed header */
}

.fb-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Enhanced Facebook-style Header */
.fb-header {
  background: linear-gradient(135deg, var(--primary-500, #4CAF50) 0%, var(--primary-600, #45a049) 100%);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 var(--space-6, 1.5rem);
  justify-content: space-between;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal, 250ms ease-in-out);
}

/* Left side elements container */
.fb-header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.fb-header-logo {
  display: flex;
  align-items: center;
}

.fb-header-logo img {
  height: 40px;
}

/* Center navigation links */
.fb-header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 2;
  gap: 8px;
}

.fb-nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 64px;
  padding: 0 var(--space-4, 1rem);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  position: relative;
  transition: all var(--transition-normal, 250ms ease-in-out);
  border-radius: var(--radius-lg, 0.75rem);
  margin: 0 var(--space-1, 0.25rem);
}

.fb-nav-link i {
  font-size: 20px;
  margin-bottom: 4px;
}

.fb-nav-link span {
  font-size: 11px;
  font-weight: 500;
}

.fb-nav-link:hover {
  color: white;
  text-decoration: none;
}

.fb-nav-link.active {
  color: white;
}

.fb-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: white;
}

/* Right side elements container */
.fb-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
}

.fb-search {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full, 9999px);
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  display: flex;
  align-items: center;
  width: 280px;
  transition: all var(--transition-normal, 250ms ease-in-out);
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.fb-search:focus-within {
  background-color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.02);
}

.fb-search i {
  color: var(--fb-dark-gray);
  margin-right: 8px;
}

.fb-search input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  font-size: 15px;
}

.fb-header-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Weather Widget in Header */
.fb-weather-widget {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.fb-weather-widget:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.fb-weather-widget .weather-icon-container i {
  color: #F5DEB3;
  font-size: 22px;
}

.fb-weather-widget .weather-temp {
  font-weight: 600;
  font-size: 16px;
}

.fb-weather-widget .weather-location {
  font-size: 12px;
  opacity: 0.9;
}

.fb-weather-widget .weather-widget-loading,
.fb-weather-widget .weather-widget-error {
  display: none;
}

.fb-header-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--fb-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fb-header-icon:hover {
  background-color: var(--agri-light-green);
}

.fb-header-icon i {
  font-size: 20px;
  color: var(--agri-dark-green);
}

.fb-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: 8px;
  cursor: pointer;
  border: 2px solid var(--agri-light-green);
}

/* Left Sidebar */
.fb-left-sidebar {
  position: fixed;
  top: 56px;
  left: 0;
  width: 280px;
  height: calc(100vh - 56px);
  overflow-y: auto;
  padding: 16px 8px;
  background-color: var(--fb-light-gray);
  border-right: 1px solid var(--agri-light-green);
}

.fb-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 5px;
  text-decoration: none;
  color: inherit;
  position: relative;
  overflow: hidden;
}

.fb-menu-item:hover {
  background-color: var(--fb-gray);
  text-decoration: none;
  color: inherit;
}

.fb-menu-item.active {
  background-color: rgba(76, 175, 80, 0.2);
  text-decoration: none;
  color: inherit;
}

.fb-menu-item i {
  font-size: 24px;
  margin-right: 12px;
  color: var(--agri-green);
}

.fb-menu-item.active i {
  color: var(--agri-dark-green);
}

.fb-menu-item span {
  font-size: 15px;
  font-weight: 500;
  color: var(--theme-text-dark);
}

.fb-menu-divider {
  height: 1px;
  background-color: var(--agri-light-green);
  margin: 8px 0;
  opacity: 0.5;
}

/* Main Content Area */
.fb-main-content {
  margin-left: 280px;
  padding: 20px;
  display: flex;
  justify-content: center;
  background-color: var(--fb-light-gray);
  min-height: calc(100vh - 56px);
  width: calc(100% - 280px);
  box-sizing: border-box;
}

.fb-feed {
  flex: 1;
  max-width: 680px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* Community Feed Container */
.community-feed {
  width: 100%;
  box-sizing: border-box;
}

.news-feed {
  width: 100%;
  box-sizing: border-box;
}

/* Ensure proper spacing between elements */
.fb-feed > * {
  margin-bottom: 16px;
}

.fb-feed > *:last-child {
  margin-bottom: 0;
}

/* Facebook-style post separation */
.fb-post + .fb-post {
  margin-top: 24px;
}

/* Add subtle separator between posts */
.fb-post:not(:last-child)::after {
  content: '';
  display: block;
  height: 8px;
  background: linear-gradient(to bottom, transparent 0%, #f0f2f5 50%, transparent 100%);
  margin-top: 24px;
  position: relative;
  z-index: 0;
}

/* Prevent content overflow and overlapping */
.fb-container {
  overflow-x: hidden;
}

.fb-main-content {
  overflow-x: hidden;
}

.fb-feed {
  overflow-x: hidden;
}

.fb-post {
  clear: both;
  overflow: hidden;
}

/* Ensure images don't overflow */
.fb-post-image {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Ensure text content doesn't overflow */
.fb-post-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.fb-post-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Facebook-style Stories Container */
.fb-stories-container {
  display: flex;
  overflow-x: auto;
  padding: 8px 0;
  margin-bottom: 16px;
  gap: 10px;
  scrollbar-width: none; /* Firefox */
}

.fb-stories-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.fb-story {
  min-width: 120px;
  height: 200px;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
  flex-shrink: 0;
  background-color: var(--fb-light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fb-story:hover {
  transform: translateY(-2px);
}

.fb-story-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 15px;
  height: 100%;
  position: relative;
}

.fb-story-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--agri-light-green);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  border: 4px solid var(--agri-green);
}

.fb-story-icon i {
  font-size: 20px;
  color: white;
}

.fb-story-content span {
  font-size: 14px;
  font-weight: 600;
  color: var(--fb-black);
  position: absolute;
  bottom: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Welcome Story */
.fb-welcome-story {
  background: linear-gradient(135deg, var(--agri-green) 0%, var(--agri-dark-green) 100%);
  color: white;
  min-width: 200px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fb-welcome-story .welcome-text {
  text-align: center;
}

.fb-welcome-story h2 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 5px;
}

.fb-welcome-story h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.fb-welcome-story p {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 0;
}

/* Content Filter Tabs */
.fb-content-filter {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.filter-tabs {
  display: flex;
  border-bottom: 1px solid var(--fb-gray);
}

.filter-tab {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: var(--fb-dark-gray);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.filter-tab i {
  font-size: 20px;
}

.filter-tab span {
  font-size: 14px;
}

.filter-tab:hover {
  background-color: var(--fb-light-gray);
}

.filter-tab.active {
  color: var(--agri-green);
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--agri-green);
}

/* Create Post Box */
.fb-create-post {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  margin-bottom: 16px;
}

.fb-create-post-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.fb-create-post-input {
  margin-left: 10px;
  background-color: var(--fb-light-gray);
  border-radius: 20px;
  padding: 8px 16px;
  width: 100%;
  cursor: pointer;
  color: var(--fb-dark-gray);
}

.fb-create-post-actions {
  display: flex;
  border-top: 1px solid var(--fb-gray);
  padding-top: 10px;
  margin-top: 10px;
}

.fb-post-action {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fb-post-action:hover {
  background-color: var(--fb-light-gray);
}

.fb-post-action i {
  margin-right: 6px;
  font-size: 20px;
}

.fb-post-action.photo i {
  color: #45BD62;
}

.fb-post-action.video i {
  color: #F5533D;
}

.fb-post-action.event i {
  color: #F7B928;
}

/* Post Card - Enhanced Facebook Style */
.fb-post {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
  border: 1px solid #e4e6ea;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  isolation: isolate;
  transition: box-shadow 0.2s ease;
}

.fb-post:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.fb-post-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
  background-color: white;
  border-bottom: none;
}

/* Post Content Area */
.fb-post-content {
  padding: 0 16px 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.fb-post-text {
  margin-bottom: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.fb-post-text h5 {
  font-size: 16px;
  font-weight: 600;
  color: #1c1e21;
  margin-bottom: 4px;
  line-height: 1.3;
}

.fb-post-text p {
  font-size: 15px;
  color: #1c1e21;
  line-height: 1.33;
  margin-bottom: 0;
}

/* Post Image Styling */
.fb-post-image {
  width: 100%;
  height: auto;
  display: block;
  margin: 12px 0 0 0;
  border-radius: 0;
  object-fit: cover;
  max-height: 500px;
}

/* Facebook-style Avatar */
.fb-post-avatar {
  flex-shrink: 0;
}

.fb-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e6ea;
  transition: border-color 0.2s ease;
}

.fb-avatar:hover {
  border-color: #1877f2;
  cursor: pointer;
}

.fb-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #42b883, #369870);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  border: 2px solid #e4e6ea;
  transition: all 0.2s ease;
}

.fb-avatar-placeholder:hover {
  border-color: #42b883;
  transform: scale(1.05);
  cursor: pointer;
}

/* Avatar Link Styles */
.fb-avatar-link {
  text-decoration: none;
  display: inline-block;
  transition: transform 0.2s ease;
}

.fb-avatar-link:hover {
  transform: scale(1.05);
}

.fb-avatar-link:focus {
  outline: 2px solid #42b883;
  outline-offset: 2px;
  border-radius: 50%;
}

/* Ensure avatar images inside links maintain their styles */
.fb-avatar-link .fb-avatar {
  display: block;
}

.fb-avatar-link .fb-avatar-placeholder {
  display: flex;
}

.fb-post-user-info {
  flex: 1;
}

.fb-post-username {
  font-weight: 600;
  color: var(--fb-black);
  font-size: 15px;
}

.fb-post-username a {
  color: var(--fb-black);
  text-decoration: none;
  transition: color 0.2s ease;
}

.fb-post-username a:hover {
  color: var(--agri-green);
  text-decoration: none;
}

/* Facebook-style Comments Section */
.fb-comments-section {
  background: white;
  border-top: 1px solid #e4e6ea;
  margin-top: 0;
}

/* Ensure comments section is always visible */
.fb-post .fb-comments-section {
  display: block !important;
}

.fb-existing-comments {
  padding: 12px 16px 0;
}

.fb-comment {
  display: flex;
  margin-bottom: 12px;
  gap: 8px;
}

.fb-comment-avatar {
  flex-shrink: 0;
}

.fb-comment-avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e4e6ea;
}

.fb-comment-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #42b883, #369870);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  border: 1px solid #e4e6ea;
}

.fb-comment-content {
  flex: 1;
  min-width: 0;
}

.fb-comment-bubble {
  background: #f0f2f5;
  border-radius: 16px;
  padding: 8px 12px;
  display: inline-block;
  max-width: 100%;
  word-wrap: break-word;
}

.fb-comment-author {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
}

.fb-comment-author-link {
  color: var(--fb-black);
  text-decoration: none;
  transition: color 0.2s ease;
}

.fb-comment-author-link:hover {
  color: var(--agri-green);
  text-decoration: none;
}

.fb-comment-text {
  font-size: 14px;
  line-height: 1.4;
  color: var(--fb-black);
}

.fb-comment-actions {
  margin-top: 4px;
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.fb-comment-time {
  color: #65676b;
  font-weight: 600;
}

.fb-comment-action {
  color: #65676b;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s ease;
}

.fb-comment-action:hover {
  color: var(--agri-green);
}

/* Comment Input Section - Facebook Style */
.fb-comment-input-section {
  border-top: 1px solid #e4e6ea;
  background: #f8f9fa;
  padding: 12px 16px 16px;
  width: 100%;
  box-sizing: border-box;
  clear: both;
}

.fb-comment-input-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.fb-comment-input-avatar {
  flex-shrink: 0;
}

.fb-comment-input-wrapper {
  flex: 1;
  position: relative;
  background: #f0f2f5;
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid transparent;
  min-height: 36px;
}

.fb-comment-input-wrapper:hover {
  background: #e4e6ea;
}

.fb-comment-input-wrapper:focus-within {
  background: white;
  border-color: #42b883;
  box-shadow: 0 0 0 2px rgba(66, 184, 131, 0.2);
}

/* Enhanced comment input container */
.fb-comment-input-container {
  background: white;
  border-radius: 24px;
  padding: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.fb-comment-input {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-size: 14px;
  line-height: 1.4;
  color: var(--fb-black);
  min-height: 20px;
  max-height: 100px;
  overflow-y: auto;
}

.fb-comment-input::placeholder {
  color: #65676b;
  font-style: italic;
}

.fb-comment-input:focus::placeholder {
  color: #42b883;
}

.fb-comment-input-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.fb-comment-emoji-btn,
.fb-comment-photo-btn,
.fb-comment-send-btn {
  background: none;
  border: none;
  color: #65676b;
  font-size: 16px;
  padding: 4px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.fb-comment-emoji-btn:hover,
.fb-comment-photo-btn:hover {
  background: #e4e6ea;
  color: var(--agri-green);
}

.fb-comment-send-btn {
  color: var(--agri-green);
}

.fb-comment-send-btn:hover {
  background: var(--agri-green);
  color: white;
  transform: scale(1.1);
}

.fb-comment-send-btn:disabled {
  color: #bcc0c4;
  cursor: not-allowed;
}

.fb-comment-send-btn:disabled:hover {
  background: none;
  transform: none;
}

.fb-post-time {
  color: var(--fb-dark-gray);
  font-size: 13px;
}

.fb-post-options {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
}

.fb-post-options:hover {
  background-color: var(--fb-light-gray);
}

.fb-post-content {
  padding: 0 16px 16px;
}

.fb-post-text {
  margin-bottom: 12px;
  font-size: 15px;
  line-height: 1.5;
}

.fb-post-text h5 {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--agri-dark-green);
}

.fb-post-image {
  width: 100%;
  max-height: 500px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

/* File containers */
.fb-post-file-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 12px;
  text-align: center;
}

.fb-post-file-container.video {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px dashed var(--agri-green);
}

.fb-post-file-container.pdf {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px dashed #f44336;
}

.fb-post-file-container.file {
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px dashed #2196f3;
}

.fb-post-file-container i {
  font-size: 40px;
  margin-bottom: 10px;
}

.fb-post-file-container.video i {
  color: var(--agri-green);
}

.fb-post-file-container.pdf i {
  color: #f44336;
}

.fb-post-file-container.file i {
  color: #2196f3;
}

/* Enhanced Post Stats */
.fb-post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  color: #65676b;
  font-size: 15px;
  border-bottom: 1px solid #e4e6ea;
  background-color: white;
}

.fb-post-stats > div:first-child {
  display: flex;
  align-items: center;
  gap: 6px;
}

.fb-post-stats i {
  font-size: 16px;
  color: #1877f2;
}

.fb-post-stats > div:last-child {
  color: #65676b;
  font-size: 15px;
}

.fb-post-stats span {
  font-weight: 600;
  color: #65676b;
}

/* Enhanced Post Actions */
.fb-post-actions {
  display: flex;
  padding: 4px 0;
  margin: 0;
  background-color: white;
}

/* Enhanced Action Buttons */
.fb-post-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #65676b;
  font-weight: 600;
  font-size: 15px;
  margin: 0 2px;
  position: relative;
  overflow: hidden;
}

.fb-post-action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.fb-post-action-button i {
  margin-right: 8px;
  font-size: 20px;
  transition: transform 0.2s ease;
}

.fb-post-action-button:hover i {
  transform: scale(1.1);
}

/* Like Button Specific Styling */
.fb-post-action-button.like:hover {
  background-color: rgba(24, 119, 242, 0.1);
  color: #1877f2;
}

.fb-post-action-button.liked {
  color: #1877f2;
}

.fb-post-action-button.liked i {
  color: #1877f2;
}

/* Comment Button Specific Styling */
.fb-post-action-button.comment:hover {
  background-color: rgba(66, 184, 131, 0.1);
  color: #42b883;
}

/* Share Button Specific Styling */
.fb-post-action-button.share:hover {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

/* Comments section */
.fb-post-comments {
  padding: 8px 16px 16px;
  border-top: 1px solid var(--fb-gray);
}

.fb-comment-form {
  margin-bottom: 12px;
}

.fb-comment-input-container {
  display: flex;
  align-items: center;
}

.fb-comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid var(--agri-light-green);
}

.fb-comment-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  background-color: var(--fb-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--fb-dark-gray);
}

.fb-comment-input-wrapper {
  flex: 1;
  position: relative;
}

.fb-comment-input {
  width: 100%;
  border-radius: 20px;
  border: 1px solid var(--fb-gray);
  padding: 8px 40px 8px 12px;
  background-color: var(--fb-light-gray);
  outline: none;
  font-size: 14px;
}

.fb-comment-input:focus {
  border-color: var(--agri-light-green);
  background-color: white;
}

.fb-comment-input-actions {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

.fb-comment-input-actions i {
  margin-left: 8px;
  font-size: 16px;
  color: var(--fb-dark-gray);
  cursor: pointer;
}

.fb-comment-input-actions i:hover {
  color: var(--agri-green);
}

.fb-login-prompt {
  text-align: center;
  padding: 8px;
  background-color: var(--fb-light-gray);
  border-radius: 8px;
  font-size: 14px;
}

.fb-comments-section {
  margin-top: 12px;
}

.fb-comment {
  display: flex;
  margin-bottom: 12px;
  animation: fadeIn 0.3s ease-out;
}

.fb-comment-content {
  flex: 1;
}

.fb-comment-bubble {
  background-color: var(--fb-light-gray);
  border-radius: 18px;
  padding: 8px 12px;
  position: relative;
  display: inline-block;
  max-width: 100%;
}

.fb-comment-username {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
}

.fb-comment-text {
  font-size: 14px;
  word-break: break-word;
}

.fb-comment-actions {
  display: flex;
  margin-top: 2px;
  padding-left: 12px;
  font-size: 12px;
}

.fb-comment-action {
  margin-right: 8px;
  color: var(--fb-dark-gray);
  font-weight: 600;
  cursor: pointer;
}

.fb-comment-action:hover {
  text-decoration: underline;
}

.fb-comment-time {
  color: var(--fb-dark-gray);
  margin-right: 8px;
}

.fb-no-comments {
  text-align: center;
  color: var(--fb-dark-gray);
  font-size: 14px;
  padding: 16px 0;
}

/* Like animation */
@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}

.like-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: rgba(76, 175, 80, 0.2);
  border-radius: 50%;
  animation: likeAnimation 0.7s ease-out;
  pointer-events: none;
}

/* Ripple effect for menu items */
.fb-ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(76, 175, 80, 0.2);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

/* Agricultural Theme Elements */
.agri-leaf-icon {
  position: absolute;
  opacity: 0.05;
  z-index: -1;
}

.agri-leaf-1 {
  top: 100px;
  left: 50px;
  transform: rotate(45deg);
  font-size: 80px;
  color: var(--agri-green);
}

.agri-leaf-2 {
  bottom: 100px;
  right: 50px;
  transform: rotate(-45deg);
  font-size: 100px;
  color: var(--agri-light-green);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .fb-left-sidebar {
    width: 70px;
  }

  .fb-menu-item span {
    display: none;
  }

  .fb-menu-item i {
    margin-right: 0;
    font-size: 24px;
  }

  .fb-main-content {
    margin-left: 70px;
  }

  /* Header adjustments */
  .fb-header-left, .fb-header-right {
    gap: 8px;
  }

  .fb-search {
    width: 180px;
  }

  .fb-weather-widget .weather-location {
    display: none;
  }

  /* Center nav adjustments */
  .fb-header-center {
    gap: 4px;
  }

  .fb-nav-link {
    padding: 0 12px;
  }
}

@media (max-width: 768px) {
  .fb-header-left, .fb-header-right {
    gap: 4px;
  }

  .fb-search {
    width: 140px;
  }

  .fb-weather-widget {
    padding: 6px 8px;
  }

  .fb-header-logo h5 {
    display: none;
  }

  /* Center nav adjustments */
  .fb-nav-link span {
    display: none;
  }

  .fb-nav-link i {
    margin-bottom: 0;
    font-size: 24px;
  }

  .fb-nav-link {
    padding: 0 10px;
  }
}

@media (max-width: 576px) {
  .fb-search {
    width: 120px;
  }

  .fb-header {
    padding: 0 8px;
  }

  .fb-header-icon {
    width: 36px;
    height: 36px;
  }

  .fb-user-avatar {
    width: 36px;
    height: 36px;
  }

  .fb-nav-link {
    padding: 0 8px;
  }
}

@media (max-width: 480px) {
  .fb-search {
    display: none;
  }

  .fb-header-center {
    flex: 3;
  }
}

@media (max-width: 768px) {
  .fb-left-sidebar {
    display: none;
  }

  .fb-main-content {
    margin-left: 0;
    width: 100%;
    padding: 15px;
  }

  .fb-feed {
    max-width: 100%;
  }

  .fb-search {
    width: 40px;
  }

  .fb-search input {
    display: none;
  }
}

/* Additional responsive fixes for content overlap */
@media (max-width: 1200px) {
  .fb-left-sidebar {
    width: 240px;
  }

  .fb-main-content {
    margin-left: 240px;
    width: calc(100% - 240px);
  }
}

@media (max-width: 992px) {
  .fb-left-sidebar {
    width: 200px;
  }

  .fb-main-content {
    margin-left: 200px;
    width: calc(100% - 200px);
    padding: 15px;
  }

  .fb-feed {
    max-width: 100%;
  }

  .fb-post {
    margin-bottom: 15px;
  }
}

/* Facebook-style Footer */
.fb-footer {
  background-color: var(--theme-primary);
  color: var(--theme-text-light);
  padding: 20px 0;
  margin-top: 40px;
  width: calc(100% - 280px);
  margin-left: 280px;
  box-sizing: border-box;
  clear: both;
}

.fb-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.fb-footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
}

.fb-footer-links a {
  color: var(--theme-text-light-muted);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.fb-footer-links a:hover {
  color: var(--theme-text-light);
  text-decoration: underline;
}

.fb-footer-copyright {
  font-size: 12px;
  color: var(--theme-text-light-muted);
}

.fb-footer-copyright p {
  margin: 0;
}

@media (max-width: 768px) {
  .fb-footer {
    margin-left: 0;
    width: 100%;
  }
}

