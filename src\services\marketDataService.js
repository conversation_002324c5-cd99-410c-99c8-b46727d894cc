/**
 * Market Data Service
 * Handles fetching market data from external APIs and processing it
 */

import fetch from 'node-fetch';
import { getFirestore } from 'firebase-admin/firestore';

// API endpoints
const USDA_API_BASE_URL = 'https://marsapi.ams.usda.gov/services/v1.2';
const USDA_API_KEY = process.env.USDA_API_KEY; // Set your actual API key in environment variables

// Cache duration in milliseconds (1 hour)
const CACHE_DURATION = 60 * 60 * 1000;

// In-memory cache
const dataCache = {
  commodities: { data: null, timestamp: 0 },
  markets: { data: null, timestamp: 0 },
  prices: { data: {}, timestamp: {} }
};

/**
 * Get available commodities from USDA API
 * @returns {Promise<Array>} List of available commodities
 */
export async function getCommodities() {
  try {
    // Check cache first
    if (dataCache.commodities.data && 
        Date.now() - dataCache.commodities.timestamp < CACHE_DURATION) {
      return dataCache.commodities.data;
    }
    
    // Fetch from API
    const response = await fetch(`${USDA_API_BASE_URL}/commodities?api_key=${USDA_API_KEY}`);
    
    if (!response.ok) {
      throw new Error(`USDA API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Process the data
    const commodities = data.results.map(item => ({
      id: item.commodity_name.toLowerCase().replace(/\s+/g, '-'),
      name: item.commodity_name,
      group: item.group_name
    }));
    
    // Update cache
    dataCache.commodities.data = commodities;
    dataCache.commodities.timestamp = Date.now();
    
    // Also store in Firestore for persistence
    const db = getFirestore();
    await db.collection('marketData').doc('commodities').set({
      data: commodities,
      lastUpdated: new Date().toISOString()
    });
    
    return commodities;
  } catch (error) {
    console.error('Error fetching commodities:', error);
    
    // Try to get from Firestore if API fails
    try {
      const db = getFirestore();
      const doc = await db.collection('marketData').doc('commodities').get();
      
      if (doc.exists) {
        const data = doc.data();
        
        // Update cache
        dataCache.commodities.data = data.data;
        dataCache.commodities.timestamp = Date.now();
        
        return data.data;
      }
    } catch (dbError) {
      console.error('Error fetching commodities from Firestore:', dbError);
    }
    
    // Return empty array if both API and Firestore fail
    return [];
  }
}

/**
 * Get available markets from USDA API
 * @returns {Promise<Array>} List of available markets
 */
export async function getMarkets() {
  try {
    // Check cache first
    if (dataCache.markets.data && 
        Date.now() - dataCache.markets.timestamp < CACHE_DURATION) {
      return dataCache.markets.data;
    }
    
    // Fetch from API
    const response = await fetch(`${USDA_API_BASE_URL}/markets?api_key=${USDA_API_KEY}`);
    
    if (!response.ok) {
      throw new Error(`USDA API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Process the data
    const markets = data.results.map(item => ({
      id: item.market_name.toLowerCase().replace(/\s+/g, '-'),
      name: item.market_name,
      state: item.state_name || 'N/A',
      type: item.market_type || 'N/A'
    }));
    
    // Update cache
    dataCache.markets.data = markets;
    dataCache.markets.timestamp = Date.now();
    
    // Also store in Firestore for persistence
    const db = getFirestore();
    await db.collection('marketData').doc('markets').set({
      data: markets,
      lastUpdated: new Date().toISOString()
    });
    
    return markets;
  } catch (error) {
    console.error('Error fetching markets:', error);
    
    // Try to get from Firestore if API fails
    try {
      const db = getFirestore();
      const doc = await db.collection('marketData').doc('markets').get();
      
      if (doc.exists) {
        const data = doc.data();
        
        // Update cache
        dataCache.markets.data = data.data;
        dataCache.markets.timestamp = Date.now();
        
        return data.data;
      }
    } catch (dbError) {
      console.error('Error fetching markets from Firestore:', dbError);
    }
    
    // Return empty array if both API and Firestore fail
    return [];
  }
}

/**
 * Get market prices for a specific commodity and market
 * @param {string} commodity - Commodity ID or 'all'
 * @param {string} market - Market ID or 'all'
 * @param {string} timeRange - Time range (1day, 7days, 30days, 90days, 1year, 5years)
 * @returns {Promise<Array>} Market price data
 */
export async function getMarketPrices(commodity = 'all', market = 'all', timeRange = '7days') {
  try {
    const cacheKey = `${commodity}_${market}_${timeRange}`;
    
    // Check cache first
    if (dataCache.prices.data[cacheKey] && 
        Date.now() - dataCache.prices.timestamp[cacheKey] < CACHE_DURATION) {
      return dataCache.prices.data[cacheKey];
    }
    
    // Convert time range to date range
    const { startDate, endDate } = getDateRangeFromTimeRange(timeRange);
    
    // Build query parameters
    const params = new URLSearchParams({
      api_key: USDA_API_KEY,
      start_date: startDate,
      end_date: endDate
    });
    
    // Add commodity filter if not 'all'
    if (commodity !== 'all') {
      params.append('commodity_name', commodity.replace(/-/g, ' '));
    }
    
    // Add market filter if not 'all'
    if (market !== 'all') {
      params.append('market_name', market.replace(/-/g, ' '));
    }
    
    // Fetch from API
    const response = await fetch(`${USDA_API_BASE_URL}/reports?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`USDA API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Process the data
    const prices = processMarketPriceData(data.results, commodity, market, timeRange);
    
    // Update cache
    dataCache.prices.data[cacheKey] = prices;
    dataCache.prices.timestamp[cacheKey] = Date.now();
    
    // Also store in Firestore for persistence
    const db = getFirestore();
    await db.collection('marketData').doc(`prices_${cacheKey}`).set({
      data: prices,
      lastUpdated: new Date().toISOString(),
      params: { commodity, market, timeRange }
    });
    
    return prices;
  } catch (error) {
    console.error('Error fetching market prices:', error);
    
    // Try to get from Firestore if API fails
    try {
      const cacheKey = `${commodity}_${market}_${timeRange}`;
      const db = getFirestore();
      const doc = await db.collection('marketData').doc(`prices_${cacheKey}`).get();
      
      if (doc.exists) {
        const data = doc.data();
        
        // Update cache
        dataCache.prices.data[cacheKey] = data.data;
        dataCache.prices.timestamp[cacheKey] = Date.now();
        
        return data.data;
      }
    } catch (dbError) {
      console.error('Error fetching market prices from Firestore:', dbError);
    }
    
    // Return empty array if both API and Firestore fail
    return [];
  }
}

/**
 * Process market price data from USDA API
 * @param {Array} results - API results
 * @param {string} commodity - Commodity ID or 'all'
 * @param {string} market - Market ID or 'all'
 * @param {string} timeRange - Time range
 * @returns {Array} Processed market price data
 */
function processMarketPriceData(results, commodity, market, timeRange) {
  // Group data by commodity and market
  const groupedData = {};
  
  results.forEach(report => {
    report.commodities.forEach(commodityData => {
      const commodityName = commodityData.commodity_name.toLowerCase().replace(/\s+/g, '-');
      
      if (commodity !== 'all' && commodityName !== commodity) {
        return;
      }
      
      commodityData.markets.forEach(marketData => {
        const marketName = marketData.market_name.toLowerCase().replace(/\s+/g, '-');
        
        if (market !== 'all' && marketName !== market) {
          return;
        }
        
        const key = `${commodityName}_${marketName}`;
        
        if (!groupedData[key]) {
          groupedData[key] = {
            crop: commodityName,
            location: marketName,
            timeSeriesData: []
          };
        }
        
        // Add price data point
        marketData.prices.forEach(price => {
          groupedData[key].timeSeriesData.push({
            date: report.report_date,
            price: parseFloat(price.price)
          });
        });
      });
    });
  });
  
  // Convert grouped data to array and calculate additional fields
  return Object.values(groupedData).map(item => {
    // Sort time series data by date
    item.timeSeriesData.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Calculate price change
    const firstPrice = item.timeSeriesData[0]?.price || 0;
    const lastPrice = item.timeSeriesData[item.timeSeriesData.length - 1]?.price || 0;
    const priceChange = firstPrice > 0 ? ((lastPrice - firstPrice) / firstPrice) * 100 : 0;
    
    return {
      ...item,
      currentPrice: lastPrice,
      priceChange: parseFloat(priceChange.toFixed(2)),
      lastUpdated: new Date().toISOString()
    };
  });
}

/**
 * Convert time range to date range
 * @param {string} timeRange - Time range (1day, 7days, 30days, 90days, 1year, 5years)
 * @returns {Object} Start and end dates in YYYY-MM-DD format
 */
function getDateRangeFromTimeRange(timeRange) {
  const endDate = new Date();
  let startDate = new Date();
  
  switch (timeRange) {
    case '1day':
      startDate.setDate(endDate.getDate() - 1);
      break;
    case '7days':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case '30days':
      startDate.setDate(endDate.getDate() - 30);
      break;
    case '90days':
      startDate.setDate(endDate.getDate() - 90);
      break;
    case '1year':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case '5years':
      startDate.setFullYear(endDate.getFullYear() - 5);
      break;
    default:
      startDate.setDate(endDate.getDate() - 7);
  }
  
  return {
    startDate: formatDate(startDate),
    endDate: formatDate(endDate)
  };
}

/**
 * Format date as YYYY-MM-DD
 * @param {Date} date - Date object
 * @returns {string} Formatted date
 */
function formatDate(date) {
  return date.toISOString().split('T')[0];
}




