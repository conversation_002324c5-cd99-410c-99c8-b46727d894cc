<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/resources">Resources</a></li>
        <li class="breadcrumb-item active" aria-current="page"><%= category %></li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4"><%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %> Resources</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
  </div>
</div>

<div class="row">
  <% if (resources && resources.length > 0) { %>
    <% resources.forEach(resource => { %>
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <% if (resource.imageUrl) { %>
            <img src="<%= resource.imageUrl %>" class="card-img-top" alt="<%= resource.title %>">
          <% } else { %>
            <div class="card-img-top bg-light text-center py-5">
              <i class="bi bi-file-earmark-text fs-1 text-muted"></i>
            </div>
          <% } %>
          <div class="card-body">
            <h5 class="card-title"><%= resource.title %></h5>
            <p class="card-text text-muted">
              <small>
                <i class="bi bi-calendar"></i> <%= new Date(resource.createdAt).toLocaleDateString() %>
              </small>
            </p>
            <p class="card-text"><%= resource.summary %></p>
          </div>
          <div class="card-footer bg-white border-top-0">
            <a href="/resources/<%= resource.id %>" class="btn btn-success">Read More</a>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <!-- Sample resources for demonstration -->
    <% 
      let sampleResources = [];
      
      if (category === 'organic-farming') {
        sampleResources = [
          {
            title: 'Introduction to Organic Farming',
            summary: 'Learn the basics of organic farming and how to transition from conventional methods.',
            icon: 'flower1'
          },
          {
            title: 'Natural Pest Control Methods',
            summary: 'Discover effective natural methods to control pests without harmful chemicals.',
            icon: 'bug'
          },
          {
            title: 'Crop Rotation Strategies',
            summary: 'Implement effective crop rotation to improve soil health and reduce pest problems.',
            icon: 'arrow-repeat'
          }
        ];
      } else if (category === 'water-conservation') {
        sampleResources = [
          {
            title: 'Drip Irrigation Systems',
            summary: 'Learn how to set up and maintain efficient drip irrigation systems for your farm.',
            icon: 'droplet'
          },
          {
            title: 'Rainwater Harvesting',
            summary: 'Capture and store rainwater for use during dry periods with these techniques.',
            icon: 'cloud-rain'
          },
          {
            title: 'Drought-Resistant Crops',
            summary: 'Discover crops that thrive with minimal water and how to cultivate them successfully.',
            icon: 'thermometer-sun'
          }
        ];
      } else if (category === 'renewable-energy') {
        sampleResources = [
          {
            title: 'Solar Power for Farms',
            summary: 'Harness solar energy to power your farm operations and reduce energy costs.',
            icon: 'sun'
          },
          {
            title: 'Wind Energy Solutions',
            summary: 'Explore wind power options suitable for agricultural settings.',
            icon: 'wind'
          },
          {
            title: 'Biogas Production',
            summary: 'Convert farm waste into usable energy through biogas production systems.',
            icon: 'recycle'
          }
        ];
      } else if (category === 'soil-health') {
        sampleResources = [
          {
            title: 'Composting Fundamentals',
            summary: 'Create nutrient-rich compost to improve soil health and reduce waste.',
            icon: 'layers'
          },
          {
            title: 'Cover Crops Guide',
            summary: 'Select and manage cover crops to protect and enhance your soil.',
            icon: 'grid'
          },
          {
            title: 'Soil Testing and Analysis',
            summary: 'Learn how to test your soil and interpret results for better crop management.',
            icon: 'clipboard-data'
          }
        ];
      } else {
        sampleResources = [
          {
            title: 'Sample Resource 1',
            summary: 'This is a sample resource for the ' + category + ' category.',
            icon: 'file-text'
          },
          {
            title: 'Sample Resource 2',
            summary: 'Another sample resource for the ' + category + ' category.',
            icon: 'file-text'
          },
          {
            title: 'Sample Resource 3',
            summary: 'Yet another sample resource for the ' + category + ' category.',
            icon: 'file-text'
          }
        ];
      }
    %>
    
    <div class="col-md-12 mb-4">
      <div class="alert alert-info" role="alert">
        <strong>Note:</strong> The following are sample resources for demonstration purposes.
      </div>
    </div>
    
    <% sampleResources.forEach(resource => { %>
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-img-top bg-light text-center py-5">
            <i class="bi bi-<%= resource.icon %> fs-1 text-success"></i>
          </div>
          <div class="card-body">
            <h5 class="card-title"><%= resource.title %></h5>
            <p class="card-text text-muted">
              <small>
                <i class="bi bi-tag"></i> <%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %>
                <span class="mx-2">|</span>
                <i class="bi bi-calendar"></i> <%= new Date().toLocaleDateString() %>
              </small>
            </p>
            <p class="card-text"><%= resource.summary %></p>
          </div>
          <div class="card-footer bg-white border-top-0">
            <a href="#" class="btn btn-success">Read More</a>
          </div>
        </div>
      </div>
    <% }); %>
  <% } %>
</div>

<div class="row mt-4">
  <div class="col-md-12">
    <a href="/resources" class="btn btn-outline-success">
      <i class="bi bi-arrow-left"></i> Back to All Resources
    </a>
  </div>
</div>
