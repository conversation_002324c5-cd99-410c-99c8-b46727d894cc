/* Weather Widget Styles */

.weather-widget {
  position: relative;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  background-color: rgba(255, 255, 255, 0.1);
  color: white !important;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  display: block;
}

.weather-widget:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.weather-widget-content {
  display: flex;
  align-items: center;
}

.weather-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.weather-info {
  display: flex;
  flex-direction: column;
}

.weather-temp {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1;
}

.weather-location {
  font-size: 0.75rem;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.weather-widget-loading,
.weather-widget-error {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 0.375rem;
}

.weather-widget.loading .weather-widget-loading {
  display: flex;
}

.weather-widget.error .weather-widget-error {
  display: flex;
}

.weather-widget.loading .weather-widget-content,
.weather-widget.error .weather-widget-content {
  visibility: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .weather-widget {
    min-width: auto;
    padding: 0.25rem;
  }

  .weather-location {
    display: none;
  }

  .weather-temp {
    font-size: 0.9rem;
  }
}
