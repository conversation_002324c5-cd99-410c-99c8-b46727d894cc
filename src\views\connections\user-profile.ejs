<!-- Header with title -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1>Farmer Profile</h1>
      <% if (user && user.displayName) { %>
        <div class="greeting-container" style="text-align: right;">
          <div id="greeting-text">
            <span class="time-greeting">Good day</span><span class="me-2">,</span>
            <span class="fw-bold"><%= user.displayName %></span>
          </div>
        </div>
      <% } %>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
  </div>
</div>

<!-- Main content with left navigation and profile -->
<div class="row">
  <!-- Left Side Navigation -->
  <div class="col-md-3 mb-4">
    <div class="dashboard-nav">
      <ul class="nav flex-column">
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/dashboard">
            <i class="bi bi-house-door me-2"></i> Dashboard
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses">
            <i class="bi bi-book me-2"></i> Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/resources">
            <i class="bi bi-file-earmark-text me-2"></i> Resources
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/courses/my-courses">
            <i class="bi bi-journal-check me-2"></i> My Courses
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/profile">
            <i class="bi bi-person-circle me-2"></i> Profile
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/new">
            <i class="bi bi-cloud-upload me-2"></i> Upload Content
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/uploads/my-uploads">
            <i class="bi bi-collection me-2"></i> My Uploads
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/weather">
            <i class="bi bi-cloud-sun me-2"></i> Weather
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link active" href="/connections">
            <i class="bi bi-people-fill me-2"></i> Farmer Network
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/messages">
            <i class="bi bi-chat-dots-fill me-2"></i> Messages
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link dashboard-nav-link" href="/auth/logout">
            <i class="bi bi-box-arrow-right me-2"></i> Logout
          </a>
        </li>
      </ul>
    </div>
  </div>

    <!-- Main content -->
    <div class="col-md-9">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h4 class="mb-0">Farmer Profile</h4>
          <a href="/connections" class="btn btn-light btn-sm">
            <i class="bi bi-people-fill me-1"></i> Back to Connections
          </a>
        </div>
        <div class="card-body">
          <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger" role="alert">
              <%= error %>
            </div>
          <% } %>

          <!-- Profile header -->
          <div class="d-flex align-items-center mb-4">
            <div class="me-4">
              <% if (profileUser.photoURL) { %>
                <img src="<%= profileUser.photoURL %>" alt="<%= profileUser.displayName %>" class="rounded-circle" width="100" height="100" style="object-fit: cover;">
              <% } else { %>
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                  <i class="bi bi-person-fill text-secondary" style="font-size: 3rem;"></i>
                </div>
              <% } %>
            </div>
            <div>
              <h3 class="mb-1"><%= profileUser.displayName %></h3>
              <div class="text-muted mb-2">
                <% if (profileUser.farmName) { %>
                  <span class="me-2"><i class="bi bi-house me-1"></i> <%= profileUser.farmName %></span>
                <% } %>
                <% if (profileUser.location) { %>
                  <span><i class="bi bi-geo-alt me-1"></i> <%= profileUser.location %></span>
                <% } %>
              </div>

              <!-- Connection status -->
              <div class="d-flex align-items-center">
                <% if (connectionStatus === 'accepted') { %>
                  <span class="badge bg-success me-2">
                    <i class="bi bi-check-circle-fill me-1"></i> Connected
                  </span>
                  <a href="/messages/conversation/<%= profileUser.uid %>" class="btn btn-primary btn-sm">
                    <i class="bi bi-chat-dots-fill me-1"></i> Message
                  </a>
                <% } else if (connectionStatus === 'pending') { %>
                  <% if (connection.requesterId === user.uid) { %>
                    <span class="badge bg-warning text-dark me-2">
                      <i class="bi bi-hourglass-split me-1"></i> Request Sent
                    </span>
                    <form action="/connections/remove/<%= connection.id %>" method="POST" class="d-inline">
                      <button type="submit" class="btn btn-outline-danger btn-sm">
                        <i class="bi bi-x-lg me-1"></i> Cancel Request
                      </button>
                    </form>
                  <% } else { %>
                    <span class="badge bg-info text-dark me-2">
                      <i class="bi bi-envelope me-1"></i> Request Received
                    </span>
                    <div class="btn-group" role="group">
                      <form action="/connections/accept/<%= connection.id %>" method="POST" class="d-inline">
                        <button type="submit" class="btn btn-success btn-sm me-2">
                          <i class="bi bi-check-lg me-1"></i> Accept
                        </button>
                      </form>
                      <form action="/connections/remove/<%= connection.id %>" method="POST" class="d-inline">
                        <button type="submit" class="btn btn-outline-danger btn-sm">
                          <i class="bi bi-x-lg me-1"></i> Decline
                        </button>
                      </form>
                    </div>
                  <% } %>
                <% } else { %>
                  <form action="/connections/request" method="POST" id="connectionForm">
                    <input type="hidden" name="recipientId" value="<%= profileUser.uid %>">
                    <button type="submit" class="btn btn-success btn-sm" id="connectBtn">
                      <i class="bi bi-person-plus-fill me-1"></i> Connect
                    </button>
                  </form>
                <% } %>
              </div>
            </div>
          </div>

          <!-- Profile details -->
          <div class="row">
            <div class="col-md-8">
              <!-- Bio section -->
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <h5 class="mb-0">About</h5>
                </div>
                <div class="card-body">
                  <% if (profileUser.bio) { %>
                    <p><%= profileUser.bio %></p>
                  <% } else { %>
                    <p class="text-muted">No bio information available.</p>
                  <% } %>
                </div>
              </div>

              <!-- Farming interests (placeholder) -->
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <h5 class="mb-0">Farming Interests</h5>
                </div>
                <div class="card-body">
                  <p class="text-muted">Farming interests will be displayed here when available.</p>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <!-- Contact information -->
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <h5 class="mb-0">Contact</h5>
                </div>
                <div class="card-body">
                  <% if (connectionStatus === 'accepted') { %>
                    <div class="mb-2">
                      <a href="/messages/conversation/<%= profileUser.uid %>" class="btn btn-primary btn-sm w-100">
                        <i class="bi bi-chat-dots-fill me-1"></i> Send Message
                      </a>
                    </div>
                  <% } else { %>
                    <p class="text-muted small">Connect with this farmer to message them directly.</p>
                  <% } %>
                </div>
              </div>

              <!-- Recent activity (placeholder) -->
              <div class="card">
                <div class="card-header bg-light">
                  <h5 class="mb-0">Recent Activity</h5>
                </div>
                <div class="card-body">
                  <p class="text-muted">Recent activity will be displayed here when available.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // AJAX connection request
    const connectionForm = document.getElementById('connectionForm');

    if (connectionForm) {
      connectionForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const connectBtn = document.getElementById('connectBtn');
        const originalButtonText = connectBtn.innerHTML;
        connectBtn.disabled = true;
        connectBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Connecting...';

        // Get form data
        const formData = new FormData(connectionForm);
        const recipientId = formData.get('recipientId');

        // Send AJAX request
        fetch('/connections/request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ recipientId }),
          credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Update UI to show pending request
            connectBtn.className = 'btn btn-warning btn-sm';
            connectBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Request Sent';

            // Reload the page after a short delay to show updated status
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          } else {
            connectBtn.className = 'btn btn-danger btn-sm';
            connectBtn.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i> Error';
            connectBtn.disabled = false;
            alert(data.error || 'Failed to send connection request');

            // Reset button after a delay
            setTimeout(() => {
              connectBtn.className = 'btn btn-success btn-sm';
              connectBtn.innerHTML = originalButtonText;
            }, 3000);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          connectBtn.className = 'btn btn-danger btn-sm';
          connectBtn.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i> Error';
          connectBtn.disabled = false;
          alert('Failed to send connection request. Please try again.');

          // Reset button after a delay
          setTimeout(() => {
            connectBtn.className = 'btn btn-success btn-sm';
            connectBtn.innerHTML = originalButtonText;
          }, 3000);
        });
      });
    }
  });
</script>

<link rel="stylesheet" href="/css/connections.css">
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize time-based greeting
    updateGreeting();
  });

  function updateGreeting() {
    const greetingElement = document.querySelector('.time-greeting');
    if (!greetingElement) return;

    const hour = new Date().getHours();
    let greeting = 'Good day';

    if (hour < 12) {
      greeting = 'Good morning';
    } else if (hour < 18) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }

    greetingElement.textContent = greeting;
  }
</script>
