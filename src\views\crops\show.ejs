<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/crops">Crops</a></li>
        <% if (crop && crop.category) { %>
          <li class="breadcrumb-item"><a href="/crops/category/<%= crop.category.toLowerCase() %>"><%= crop.category %></a></li>
        <% } %>
        <li class="breadcrumb-item active" aria-current="page"><%= crop ? crop.name : 'Crop Details' %></li>
      </ol>
    </nav>
  </div>
</div>

<% if (!crop) { %>
  <div class="row">
    <div class="col-md-12">
      <div class="alert alert-danger" role="alert">
        Crop not found or an error occurred.
      </div>
      <a href="/crops" class="btn btn-primary">Back to Crops</a>
    </div>
  </div>
<% } else { %>
  <div class="row">
    <div class="col-md-6">
      <% if (crop.imageUrl) { %>
        <img src="<%= crop.imageUrl %>" class="img-fluid rounded shadow-sm mb-4" alt="<%= crop.name %>">
      <% } else { %>
        <div class="bg-light rounded d-flex align-items-center justify-content-center shadow-sm mb-4" style="height: 300px;">
          <i class="bi bi-image text-muted" style="font-size: 5rem;"></i>
        </div>
      <% } %>
    </div>
    
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h1 class="mb-3"><%= crop.name %></h1>
          
          <div class="d-flex justify-content-between align-items-center mb-3">
            <span class="badge bg-success fs-5">$<%= crop.price.toFixed(2) %>/<%= crop.unit %></span>
            <span class="badge bg-secondary"><%= crop.category %></span>
          </div>
          
          <p class="lead"><%= crop.description %></p>
          
          <hr>
          
          <div class="row mb-3">
            <div class="col-6">
              <p class="mb-1"><strong>Available Quantity:</strong></p>
              <p><%= crop.quantity %> <%= crop.unit %>(s)</p>
            </div>
            <div class="col-6">
              <p class="mb-1"><strong>Location:</strong></p>
              <p><i class="bi bi-geo-alt"></i> <%= crop.location %></p>
            </div>
          </div>
          
          <div class="mb-3">
            <p class="mb-1"><strong>Seller:</strong></p>
            <p><%= crop.userName %></p>
          </div>
          
          <div class="mb-3">
            <p class="mb-1"><strong>Posted:</strong></p>
            <p><%= new Date(crop.createdAt).toLocaleDateString() %></p>
          </div>
          
          <div class="d-grid gap-2">
            <button class="btn btn-success" type="button" data-bs-toggle="modal" data-bs-target="#contactSellerModal">
              <i class="bi bi-chat"></i> Contact Seller
            </button>
            
            <% if (typeof user !== 'undefined' && user && user.uid === crop.userId) { %>
              <div class="btn-group">
                <a href="/crops/<%= crop.id %>/edit" class="btn btn-outline-secondary">
                  <i class="bi bi-pencil"></i> Edit
                </a>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCropModal">
                  <i class="bi bi-trash"></i> Delete
                </button>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h4 class="mb-0">Similar Crops</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <!-- This would be populated with actual similar crops in a real application -->
            <div class="col-md-4 mb-3">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">Similar Crop 1</h5>
                  <p class="card-text">Description of a similar crop.</p>
                  <a href="#" class="btn btn-sm btn-outline-success">View Details</a>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">Similar Crop 2</h5>
                  <p class="card-text">Description of a similar crop.</p>
                  <a href="#" class="btn btn-sm btn-outline-success">View Details</a>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">Similar Crop 3</h5>
                  <p class="card-text">Description of a similar crop.</p>
                  <a href="#" class="btn btn-sm btn-outline-success">View Details</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Contact Seller Modal -->
  <div class="modal fade" id="contactSellerModal" tabindex="-1" aria-labelledby="contactSellerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="contactSellerModalLabel">Contact Seller</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="contactForm">
            <div class="mb-3">
              <label for="message" class="form-label">Message</label>
              <textarea class="form-control" id="message" rows="5" placeholder="I'm interested in your <%= crop.name %>. Is it still available?"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="sendMessageBtn">Send Message</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Crop Modal -->
  <% if (typeof user !== 'undefined' && user && user.uid === crop.userId) { %>
    <div class="modal fade" id="deleteCropModal" tabindex="-1" aria-labelledby="deleteCropModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="deleteCropModalLabel">Confirm Delete</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete "<%= crop.name %>"? This action cannot be undone.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <form action="/crops/<%= crop.id %>/delete" method="POST">
              <button type="submit" class="btn btn-danger">Delete</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  <% } %>
<% } %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle contact form submission
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    if (sendMessageBtn) {
      sendMessageBtn.addEventListener('click', function() {
        alert('Message sent! (This is a demo feature)');
        const modal = bootstrap.Modal.getInstance(document.getElementById('contactSellerModal'));
        modal.hide();
      });
    }
  });
</script>
