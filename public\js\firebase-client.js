// Firebase Client-Side Configuration
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  connectAuthEmulator
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
import { 
  getFirestore,
  connectFirestoreEmulator 
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
  authDomain: "sustainablefarming-bf265.firebaseapp.com",
  projectId: "sustainablefarming-bf265",
  storageBucket: "sustainablefarming-bf265.appspot.com",
  messagingSenderId: "89904373415",
  appId: "1:89904373415:web:2b8bbc14c7802554cac582",
  measurementId: "G-LVMEESYTKJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Connect to emulators only if explicitly enabled
if (window.location.hostname === 'localhost' && window.location.search.includes('emulator=true')) {
  try {
    // Try to connect to emulators, but don't fail if they're not available
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    connectFirestoreEmulator(db, 'localhost', 8080);
    console.log('Connected to Firebase emulators');
  } catch (error) {
    console.log('Emulators not available, using production Firebase:', error.message);
    // Continue with production Firebase if emulators are not available
  }
} else {
  console.log('Using production Firebase services');
}

// Export Firebase services
window.firebaseAuth = auth;
window.firebaseDb = db;

// Authentication state observer
onAuthStateChanged(auth, (user) => {
  if (user) {
    console.log('User is signed in:', user.email);
    // Store user info in sessionStorage for server-side access
    sessionStorage.setItem('firebaseUser', JSON.stringify({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL
    }));
  } else {
    console.log('User is signed out');
    sessionStorage.removeItem('firebaseUser');
  }
});

// Authentication functions
window.firebaseLogin = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

window.firebaseRegister = async (email, password) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

window.firebaseLogout = async () => {
  try {
    await signOut(auth);
    sessionStorage.removeItem('firebaseUser');
    return true;
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};

// Utility function to get current user
window.getCurrentFirebaseUser = () => {
  return auth.currentUser;
};

// Check if user is authenticated
window.isFirebaseAuthenticated = () => {
  return !!auth.currentUser;
};

console.log('Firebase client initialized successfully');
