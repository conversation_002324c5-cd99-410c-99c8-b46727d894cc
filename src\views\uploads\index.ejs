<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4">Community Content</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
    
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <a href="/uploads/new" class="btn btn-success">
          <i class="bi bi-cloud-upload"></i> Upload New Content
        </a>
      </div>
      
      <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
          Filter by Category
        </button>
        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
          <li><a class="dropdown-item" href="/uploads">All Categories</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="#" data-category="Crops">Crops</a></li>
          <li><a class="dropdown-item" href="#" data-category="Farming Techniques">Farming Techniques</a></li>
          <li><a class="dropdown-item" href="#" data-category="Other">Other</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <% if (uploads && uploads.length > 0) { %>
    <% uploads.forEach(upload => { %>
      <div class="col-md-4 mb-4 upload-item" data-category="<%= upload.category %>">
        <div class="card h-100 shadow-sm">
          <% if (upload.fileUrl) { %>
            <% if (upload.fileUrl.endsWith('.jpg') || upload.fileUrl.endsWith('.jpeg') || upload.fileUrl.endsWith('.png') || upload.fileUrl.endsWith('.gif')) { %>
              <img src="<%= upload.fileUrl %>" class="card-img-top" alt="<%= upload.title %>" style="height: 200px; object-fit: cover;">
            <% } else if (upload.fileUrl.endsWith('.mp4') || upload.fileUrl.endsWith('.webm') || upload.fileUrl.endsWith('.ogg')) { %>
              <div class="card-img-top bg-dark d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-film text-light" style="font-size: 3rem;"></i>
              </div>
            <% } else if (upload.fileUrl.endsWith('.pdf')) { %>
              <div class="card-img-top bg-danger d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-file-pdf text-light" style="font-size: 3rem;"></i>
              </div>
            <% } else { %>
              <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="bi bi-file-earmark text-light" style="font-size: 3rem;"></i>
              </div>
            <% } %>
          <% } else { %>
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
              <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
            </div>
          <% } %>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
              <h5 class="card-title"><%= upload.title %></h5>
              <span class="badge bg-secondary"><%= upload.category %></span>
            </div>
            <p class="card-text"><%= upload.description %></p>
          </div>
          <div class="card-footer bg-white border-top-0">
            <div class="d-flex justify-content-between align-items-center">
              <small class="text-muted">
                Posted by <%= upload.userName %>
              </small>
              <div>
                <a href="<%= upload.fileUrl %>" class="btn btn-sm btn-outline-primary" target="_blank">
                  <i class="bi bi-eye"></i> View
                </a>
                <a href="/uploads/<%= upload.id %>" class="btn btn-sm btn-outline-success">
                  <i class="bi bi-info-circle"></i> Details
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% }); %>
  <% } else { %>
    <div class="col-md-12">
      <div class="text-center py-5">
        <div class="mb-4">
          <i class="bi bi-cloud-upload text-muted" style="font-size: 5rem;"></i>
        </div>
        <h3>No Content Available Yet</h3>
        <p class="text-muted mb-4">Be the first to share content with the community!</p>
        <a href="/uploads/new" class="btn btn-lg btn-success">
          <i class="bi bi-cloud-upload me-2"></i> Upload Your First Content
        </a>
      </div>
    </div>
  <% } %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Category filter functionality
    const categoryLinks = document.querySelectorAll('[data-category]');
    categoryLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const category = this.dataset.category;
        const uploadItems = document.querySelectorAll('.upload-item');
        
        uploadItems.forEach(item => {
          if (category === 'all' || item.dataset.category === category) {
            item.style.display = 'block';
          } else {
            item.style.display = 'none';
          }
        });
        
        // Update dropdown button text
        document.getElementById('categoryDropdown').textContent = category === 'all' ? 'All Categories' : category;
      });
    });
  });
</script>
