// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDUFUPioIiovoGEIlUuP8eMz5m3AmhmWoI",
  authDomain: "sustainablefarming-bf265.firebaseapp.com",
  projectId: "sustainablefarming-bf265",
  storageBucket: "sustainablefarming-bf265.appspot.com", // Fixed storage bucket URL
  messagingSenderId: "89904373415",
  appId: "1:89904373415:web:2b8bbc14c7802554cac582",
  measurementId: "G-LVMEESYTKJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

// Note: Emulators disabled for production Firebase usage
// To enable emulators, set NODE_ENV=emulator and ensure Firebase emulators are running
if (process.env.NODE_ENV === 'emulator') {
  // Import the functions to connect to emulators
  import('firebase/auth').then(({ connectAuthEmulator }) => {
    try {
      connectAuthEmulator(auth, 'http://localhost:9099');
      console.log('Connected to Auth Emulator');
    } catch (error) {
      console.error('Failed to connect to Auth Emulator:', error);
    }
  }).catch(error => console.error('Failed to import connectAuthEmulator:', error));

  import('firebase/firestore').then(({ connectFirestoreEmulator }) => {
    try {
      connectFirestoreEmulator(db, 'localhost', 8080);
      console.log('Connected to Firestore Emulator');
    } catch (error) {
      console.error('Failed to connect to Firestore Emulator:', error);
    }
  }).catch(error => console.error('Failed to import connectFirestoreEmulator:', error));

  import('firebase/storage').then(({ connectStorageEmulator }) => {
    try {
      connectStorageEmulator(storage, 'localhost', 9199);
      console.log('Connected to Storage Emulator');
    } catch (error) {
      console.error('Failed to connect to Storage Emulator:', error);
    }
  }).catch(error => console.error('Failed to import connectStorageEmulator:', error));
} else {
  console.log('Using production Firebase services');
}

// Export the Firebase services
export { app, auth, db, storage };
