/**
 * Facebook-style Profile Page JavaScript
 * Enhances the profile page with interactive features and animations
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize profile page components
  initProfilePictureUpload();
  initCoverPhotoUpload();
  initProfileTabs();
  initPostCreation();
  initPhotoGallery();
  initScrollEffects();
  initTooltips();
  initAnimations();
  
  // Show success or error messages with animation
  showMessages();
});

/**
 * Initialize profile picture upload functionality
 */
function initProfilePictureUpload() {
  const profilePictureInput = document.getElementById('profilePictureInput');
  const profilePictureEdit = document.querySelector('.profile-picture-edit');
  
  if (profilePictureInput && profilePictureEdit) {
    // Show a preview of the selected image before uploading
    profilePictureInput.addEventListener('change', function() {
      if (this.files && this.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
          // Find the profile picture element
          const profilePicture = document.querySelector('.profile-picture');
          const profilePicturePlaceholder = document.querySelector('.profile-picture-placeholder');
          
          if (profilePicture) {
            // Update existing profile picture
            profilePicture.src = e.target.result;
          } else if (profilePicturePlaceholder) {
            // Replace placeholder with actual image
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'profile-picture';
            img.alt = 'Profile Picture';
            
            profilePicturePlaceholder.parentNode.replaceChild(img, profilePicturePlaceholder);
          }
          
          // Show loading indicator
          const loadingIndicator = document.createElement('div');
          loadingIndicator.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50 rounded-circle';
          loadingIndicator.innerHTML = '<div class="spinner-border text-light" role="status"><span class="visually-hidden">Loading...</span></div>';
          
          document.querySelector('.profile-picture-container').appendChild(loadingIndicator);
          
          // Submit the form
          const formData = new FormData();
          formData.append('profilePicture', profilePictureInput.files[0]);
          
          fetch('/profile/upload-photo', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            // Remove loading indicator
            loadingIndicator.remove();
            
            if (data.success) {
              // Show success message
              showNotification('Profile picture updated successfully!', 'success');
            } else {
              // Show error message
              showNotification('Error updating profile picture: ' + data.message, 'error');
            }
          })
          .catch(error => {
            // Remove loading indicator
            loadingIndicator.remove();
            
            // Show error message
            showNotification('Error updating profile picture. Please try again.', 'error');
            console.error('Error:', error);
          });
        };
        
        reader.readAsDataURL(this.files[0]);
      }
    });
    
    // Add hover effect to profile picture
    const profilePictureContainer = document.querySelector('.profile-picture-container');
    if (profilePictureContainer) {
      profilePictureContainer.addEventListener('mouseenter', function() {
        profilePictureEdit.style.opacity = '1';
      });
      
      profilePictureContainer.addEventListener('mouseleave', function() {
        profilePictureEdit.style.opacity = '0.7';
      });
    }
  }
}

/**
 * Initialize cover photo upload functionality
 */
function initCoverPhotoUpload() {
  const coverPhotoInput = document.getElementById('coverPhotoInput');
  const coverPhotoEdit = document.querySelector('.cover-photo-edit');
  
  if (coverPhotoInput && coverPhotoEdit) {
    // Show a preview of the selected image before uploading
    coverPhotoInput.addEventListener('change', function() {
      if (this.files && this.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
          // Update cover photo background
          const profileCover = document.querySelector('.profile-cover');
          profileCover.style.backgroundImage = `url('${e.target.result}')`;
          
          // Remove placeholder if it exists
          const placeholder = profileCover.querySelector('.profile-cover-placeholder');
          if (placeholder) {
            placeholder.remove();
          }
          
          // Show loading indicator
          const loadingIndicator = document.createElement('div');
          loadingIndicator.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50';
          loadingIndicator.innerHTML = '<div class="spinner-border text-light" role="status"><span class="visually-hidden">Loading...</span></div>';
          
          profileCover.appendChild(loadingIndicator);
          
          // Submit the form
          const formData = new FormData();
          formData.append('coverPhoto', coverPhotoInput.files[0]);
          
          fetch('/profile/upload-cover-photo', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            // Remove loading indicator
            loadingIndicator.remove();
            
            if (data.success) {
              // Show success message
              showNotification('Cover photo updated successfully!', 'success');
            } else {
              // Show error message
              showNotification('Error updating cover photo: ' + data.message, 'error');
            }
          })
          .catch(error => {
            // Remove loading indicator
            loadingIndicator.remove();
            
            // Show error message
            showNotification('Error updating cover photo. Please try again.', 'error');
            console.error('Error:', error);
          });
        };
        
        reader.readAsDataURL(this.files[0]);
      }
    });
    
    // Add hover effect to cover photo
    const profileCover = document.querySelector('.profile-cover');
    if (profileCover) {
      profileCover.addEventListener('mouseenter', function() {
        coverPhotoEdit.style.opacity = '1';
      });
      
      profileCover.addEventListener('mouseleave', function() {
        coverPhotoEdit.style.opacity = '0.7';
      });
    }
  }
}

/**
 * Initialize profile tabs functionality
 */
function initProfileTabs() {
  const tabLinks = document.querySelectorAll('.profile-nav .nav-link');
  
  if (tabLinks.length > 0) {
    tabLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs
        tabLinks.forEach(tab => tab.classList.remove('active'));
        
        // Add active class to clicked tab
        this.classList.add('active');
        
        // Show the corresponding tab content
        const target = this.getAttribute('data-bs-target');
        const tabContents = document.querySelectorAll('.tab-pane');
        
        tabContents.forEach(content => {
          content.classList.remove('show', 'active');
        });
        
        document.querySelector(target).classList.add('show', 'active');
        
        // Update URL with hash
        history.pushState(null, null, target);
      });
    });
    
    // Check if URL has a hash and activate the corresponding tab
    if (window.location.hash) {
      const activeTab = document.querySelector(`.profile-nav .nav-link[data-bs-target="${window.location.hash}"]`);
      if (activeTab) {
        activeTab.click();
      }
    }
  }
}

/**
 * Initialize post creation functionality
 */
function initPostCreation() {
  const postTextarea = document.querySelector('textarea[placeholder="What\'s on your mind?"]');
  const postButton = document.querySelector('.post-card .btn-primary');
  const photoButton = document.querySelector('.post-card .btn-light');
  
  if (postTextarea && postButton) {
    // Auto-resize textarea as user types
    postTextarea.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Enable/disable post button based on content
    postTextarea.addEventListener('input', function() {
      postButton.disabled = this.value.trim() === '';
    });
    
    // Initialize with disabled state
    postButton.disabled = true;
    
    // Handle post creation
    postButton.addEventListener('click', function() {
      const text = postTextarea.value.trim();
      
      if (text) {
        // Show loading state
        const originalText = postButton.innerHTML;
        postButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Posting...';
        postButton.disabled = true;
        
        // Simulate post creation (replace with actual API call)
        setTimeout(() => {
          // Reset button state
          postButton.innerHTML = originalText;
          postButton.disabled = false;
          
          // Clear textarea
          postTextarea.value = '';
          postTextarea.style.height = 'auto';
          
          // Show success message
          showNotification('Your post has been published!', 'success');
          
          // Reload page to show new post
          window.location.reload();
        }, 1500);
      }
    });
    
    // Handle photo upload
    if (photoButton) {
      photoButton.addEventListener('click', function() {
        // Create file input if it doesn't exist
        let fileInput = document.getElementById('post-photo-input');
        
        if (!fileInput) {
          fileInput = document.createElement('input');
          fileInput.type = 'file';
          fileInput.id = 'post-photo-input';
          fileInput.accept = 'image/*';
          fileInput.style.display = 'none';
          document.body.appendChild(fileInput);
          
          // Handle file selection
          fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
              const reader = new FileReader();
              
              reader.onload = function(e) {
                // Create preview container if it doesn't exist
                let previewContainer = document.querySelector('.post-photo-preview');
                
                if (!previewContainer) {
                  previewContainer = document.createElement('div');
                  previewContainer.className = 'post-photo-preview mt-3 position-relative';
                  postTextarea.parentNode.appendChild(previewContainer);
                }
                
                // Show preview
                previewContainer.innerHTML = `
                  <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 200px;">
                  <button type="button" class="btn-close position-absolute top-0 end-0 bg-dark bg-opacity-50 rounded-circle p-1 m-1" aria-label="Remove"></button>
                `;
                
                // Enable post button
                postButton.disabled = false;
                
                // Handle remove button
                previewContainer.querySelector('.btn-close').addEventListener('click', function() {
                  previewContainer.remove();
                  fileInput.value = '';
                  
                  // Disable post button if text is empty
                  postButton.disabled = postTextarea.value.trim() === '';
                });
              };
              
              reader.readAsDataURL(this.files[0]);
            }
          });
        }
        
        fileInput.click();
      });
    }
  }
}

/**
 * Initialize photo gallery functionality
 */
function initPhotoGallery() {
  const photoItems = document.querySelectorAll('.photo-item img');
  
  if (photoItems.length > 0) {
    photoItems.forEach(photo => {
      photo.addEventListener('click', function() {
        // Create modal if it doesn't exist
        let photoModal = document.getElementById('photoModal');
        
        if (!photoModal) {
          photoModal = document.createElement('div');
          photoModal.className = 'modal fade';
          photoModal.id = 'photoModal';
          photoModal.tabIndex = '-1';
          photoModal.setAttribute('aria-hidden', 'true');
          
          photoModal.innerHTML = `
            <div class="modal-dialog modal-lg modal-dialog-centered">
              <div class="modal-content bg-dark">
                <div class="modal-body p-0">
                  <img src="" class="img-fluid w-100" id="modalImage">
                </div>
                <button type="button" class="btn-close btn-close-white position-absolute top-0 end-0 m-3" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
            </div>
          `;
          
          document.body.appendChild(photoModal);
        }
        
        // Set image source
        document.getElementById('modalImage').src = this.src;
        
        // Show modal
        const modal = new bootstrap.Modal(photoModal);
        modal.show();
      });
    });
  }
}

/**
 * Initialize scroll effects
 */
function initScrollEffects() {
  // Add shadow to profile info on scroll
  window.addEventListener('scroll', function() {
    const profileInfo = document.querySelector('.profile-info');
    
    if (profileInfo) {
      if (window.scrollY > 100) {
        profileInfo.classList.add('shadow');
      } else {
        profileInfo.classList.remove('shadow');
      }
    }
  });
}

/**
 * Initialize tooltips
 */
function initTooltips() {
  // Initialize Bootstrap tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function(tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

/**
 * Initialize animations
 */
function initAnimations() {
  // Add fade-in animation to profile content
  const profileContent = document.querySelector('.profile-content');
  
  if (profileContent) {
    profileContent.style.opacity = '0';
    profileContent.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
      profileContent.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      profileContent.style.opacity = '1';
      profileContent.style.transform = 'translateY(0)';
    }, 300);
  }
  
  // Add staggered animation to about items
  const aboutItems = document.querySelectorAll('.about-item');
  
  if (aboutItems.length > 0) {
    aboutItems.forEach((item, index) => {
      item.style.opacity = '0';
      item.style.transform = 'translateX(-20px)';
      
      setTimeout(() => {
        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        item.style.opacity = '1';
        item.style.transform = 'translateX(0)';
      }, 300 + (index * 100));
    });
  }
}

/**
 * Show success or error messages with animation
 */
function showMessages() {
  // Check for success or error messages in URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const successMessage = urlParams.get('success');
  const errorMessage = urlParams.get('error');
  
  if (successMessage) {
    showNotification(decodeURIComponent(successMessage), 'success');
  }
  
  if (errorMessage) {
    showNotification(decodeURIComponent(errorMessage), 'error');
  }
}

/**
 * Show a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification ('success' or 'error')
 */
function showNotification(message, type) {
  // Create notification element if it doesn't exist
  let notification = document.querySelector('.profile-notification');
  
  if (!notification) {
    notification = document.createElement('div');
    notification.className = 'profile-notification';
    document.body.appendChild(notification);
  }
  
  // Set notification content and type
  notification.innerHTML = message;
  notification.className = `profile-notification ${type}`;
  
  // Show notification
  notification.style.display = 'block';
  
  // Animate in
  setTimeout(() => {
    notification.style.opacity = '1';
    notification.style.transform = 'translateY(0)';
  }, 10);
  
  // Hide after 3 seconds
  setTimeout(() => {
    notification.style.opacity = '0';
    notification.style.transform = 'translateY(-20px)';
    
    // Remove after animation
    setTimeout(() => {
      notification.style.display = 'none';
    }, 300);
  }, 3000);
}
