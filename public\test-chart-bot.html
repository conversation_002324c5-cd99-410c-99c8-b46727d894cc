<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Bot Function Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/toast-notifications.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Chart Bot Function Test</h1>
        <div class="row">
            <div class="col-md-6">
                <h3>Function Tests</h3>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="testSendMessage()">Test sendMessage</button>
                    <button class="btn btn-secondary" onclick="testSendSuggestion()">Test sendSuggestion</button>
                    <button class="btn btn-success" onclick="testHandleChatKeyPress()">Test handleChatKeyPress</button>
                    <button class="btn btn-info" onclick="testShowSavedCharts()">Test showSavedCharts</button>
                    <button class="btn btn-warning" onclick="testShowNotification()">Test showNotification</button>
                </div>
            </div>
            <div class="col-md-6">
                <h3>Function Status</h3>
                <div id="function-status"></div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Test Input</h3>
            <input type="text" id="test-input" class="form-control" placeholder="Test input" onkeypress="handleChatKeyPress(event)">
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/toast-notifications.js"></script>
    <script src="/js/chart-bot.js"></script>

    <script>
        function checkFunctions() {
            const functions = [
                'sendMessage', 'sendSuggestion', 'handleChatKeyPress', 
                'showSavedCharts', 'initializeChartBot', 'showNotification'
            ];
            
            const statusDiv = document.getElementById('function-status');
            let html = '<ul class="list-group">';
            
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                const status = exists ? 'success' : 'danger';
                const icon = exists ? 'check-circle' : 'x-circle';
                html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                    ${funcName}
                    <span class="badge bg-${status}"><i class="bi bi-${icon}"></i></span>
                </li>`;
            });
            
            html += '</ul>';
            statusDiv.innerHTML = html;
        }

        function testSendMessage() {
            if (typeof sendMessage === 'function') {
                alert('sendMessage function exists and is callable');
            } else {
                alert('sendMessage function not found');
            }
        }

        function testSendSuggestion() {
            if (typeof sendSuggestion === 'function') {
                sendSuggestion('Test suggestion');
            } else {
                alert('sendSuggestion function not found');
            }
        }

        function testHandleChatKeyPress() {
            if (typeof handleChatKeyPress === 'function') {
                alert('handleChatKeyPress function exists');
            } else {
                alert('handleChatKeyPress function not found');
            }
        }

        function testShowSavedCharts() {
            if (typeof showSavedCharts === 'function') {
                showSavedCharts();
            } else {
                alert('showSavedCharts function not found');
            }
        }

        function testShowNotification() {
            if (typeof showNotification === 'function') {
                showNotification('Test notification', 'success', 'Test');
            } else {
                alert('showNotification function not found');
            }
        }

        // Check functions when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkFunctions, 1000);
        });
    </script>
</body>
</html>
