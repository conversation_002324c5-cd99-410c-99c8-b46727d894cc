<%- include('../partials/header') %>

<div class="container mt-4">
  <div class="row">
    <!-- Profile Header -->
    <div class="col-12 mb-4">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <div class="col-md-3 text-center">
              <% if (profileUser.photoURL) { %>
                <img src="<%= profileUser.photoURL %>" class="rounded-circle img-thumbnail mb-3" style="width: 150px; height: 150px; object-fit: cover;">
              <% } else { %>
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 150px; height: 150px;">
                  <i class="bi bi-person-fill text-secondary" style="font-size: 4rem;"></i>
                </div>
              <% } %>
            </div>
            <div class="col-md-9">
              <h2 class="mb-1"><%= profileUser.displayName || 'User' %></h2>
              <p class="text-muted mb-3"><%= profileUser.headline || 'Sustainable Farmer' %></p>
              <p class="mb-2"><i class="bi bi-geo-alt-fill text-success me-2"></i><%= profileUser.location || 'Location not specified' %></p>

              <% if (isCurrentUser) { %>
                <a href="/profile" class="btn btn-outline-success">Edit Profile</a>
              <% } else { %>
                <% if (connectionStatus === 'accepted') { %>
                  <button class="btn btn-outline-danger" id="remove-connection-btn" data-id="<%= connectionId %>">Remove Connection</button>
                  <a href="/messaging/<%= profileUser.uid %>" class="btn btn-outline-success ms-2">Message</a>
                <% } else if (connectionStatus === 'pending' && isRequester) { %>
                  <span class="badge bg-secondary p-2">Connection Request Sent</span>
                <% } else if (connectionStatus === 'pending' && !isRequester) { %>
                  <button class="btn btn-success" id="accept-request-btn" data-id="<%= connectionId %>">Accept Request</button>
                  <button class="btn btn-outline-danger ms-2" id="reject-request-btn" data-id="<%= connectionId %>">Decline</button>
                <% } else { %>
                  <button class="btn btn-success" id="connect-btn" data-id="<%= profileUser.uid %>">Connect</button>
                <% } %>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Profile Content -->
    <div class="col-md-4">
      <!-- About Section -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">About</h5>
        </div>
        <div class="card-body">
          <p><%= profileUser.bio || 'No bio available.' %></p>

          <h6 class="mt-4 mb-3">Farming Details</h6>
          <div class="mb-2">
            <strong>Farming Type:</strong>
            <span class="ms-2"><%= profileUser.farmingType || 'Not specified' %></span>
          </div>
          <div class="mb-2">
            <strong>Experience:</strong>
            <span class="ms-2"><%= profileUser.experience || 'Not specified' %></span>
          </div>
          <div class="mb-2">
            <strong>Farm Size:</strong>
            <span class="ms-2"><%= profileUser.farmSize || 'Not specified' %></span>
          </div>
          <div class="mb-2">
            <strong>Main Crops:</strong>
            <span class="ms-2"><%= profileUser.mainCrops || 'Not specified' %></span>
          </div>
        </div>
      </div>

      <!-- Connections Section -->
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Connections</h5>
        </div>
        <div class="card-body">
          <p class="text-muted text-center">Connection list will be displayed here.</p>
          <div class="text-center">
            <a href="/network/connections" class="btn btn-outline-success">View All Connections</a>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <!-- Posts Section -->
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Posts</h5>
        </div>
        <div class="card-body">
          <p class="text-muted text-center">User's posts will be displayed here.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Connect button functionality
    const connectBtn = document.getElementById('connect-btn');
    if (connectBtn) {
      connectBtn.addEventListener('click', function() {
        const recipientId = this.getAttribute('data-id');
        sendConnectionRequest(recipientId);
      });
    }

    // Accept request button functionality
    const acceptRequestBtn = document.getElementById('accept-request-btn');
    if (acceptRequestBtn) {
      acceptRequestBtn.addEventListener('click', function() {
        const connectionId = this.getAttribute('data-id');
        acceptConnectionRequest(connectionId);
      });
    }

    // Reject request button functionality
    const rejectRequestBtn = document.getElementById('reject-request-btn');
    if (rejectRequestBtn) {
      rejectRequestBtn.addEventListener('click', function() {
        const connectionId = this.getAttribute('data-id');
        rejectConnectionRequest(connectionId);
      });
    }

    // Remove connection button functionality
    const removeConnectionBtn = document.getElementById('remove-connection-btn');
    if (removeConnectionBtn) {
      removeConnectionBtn.addEventListener('click', function() {
        const connectionId = this.getAttribute('data-id');
        removeConnection(connectionId);
      });
    }
  });

  // Function to send a connection request
  function sendConnectionRequest(recipientId) {
    fetch('/network/api/connections/request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ recipientId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Connection request sent!');
        // Reload the page to show updated status
        window.location.reload();
      } else {
        alert('Error: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error sending connection request:', error);
      alert('An error occurred. Please try again.');
    });
  }

  // Function to accept a connection request
  function acceptConnectionRequest(connectionId) {
    fetch('/network/api/connections/accept', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Connection request accepted!');
        // Reload the page to show updated status
        window.location.reload();
      } else {
        alert('Error: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error accepting connection request:', error);
      alert('An error occurred. Please try again.');
    });
  }

  // Function to reject a connection request
  function rejectConnectionRequest(connectionId) {
    fetch('/network/api/connections/reject', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Connection request rejected.');
        // Reload the page to show updated status
        window.location.reload();
      } else {
        alert('Error: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error rejecting connection request:', error);
      alert('An error occurred. Please try again.');
    });
  }

  // Function to remove a connection
  function removeConnection(connectionId) {
    if (!confirm('Are you sure you want to remove this connection?')) {
      return;
    }

    fetch('/network/api/connections/remove', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Connection removed!');
        // Reload the page to show updated status
        window.location.reload();
      } else {
        alert('Error: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error removing connection:', error);
      alert('An error occurred. Please try again.');
    });
  }
</script>

<%- include('../partials/footer') %>
