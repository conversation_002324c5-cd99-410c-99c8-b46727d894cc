/**
 * WhatsApp-style Messaging Enhancements
 * Adds interactive styling and animations to the messaging interface
 */

document.addEventListener('DOMContentLoaded', function() {
  // Elements
  const conversationItems = document.querySelectorAll('.conversation-item');
  const messagesContainer = document.getElementById('messages-container');
  const messageInput = document.getElementById('message-input');
  const sendButton = document.getElementById('send-message-btn');
  const chatFooterIcons = document.querySelectorAll('.chat-footer-icon');
  const headerIcons = document.querySelectorAll('.header-icon, .chat-header-icon');
  
  // Apply hover effects to conversation items
  conversationItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      this.style.transition = 'background-color 0.2s ease';
      this.style.backgroundColor = 'var(--wa-hover-background)';
    });
    
    item.addEventListener('mouseleave', function() {
      this.style.backgroundColor = this.classList.contains('active') 
        ? 'var(--wa-active-background)' 
        : '';
    });
    
    // Add ripple effect on click
    item.addEventListener('click', function(e) {
      const ripple = document.createElement('div');
      ripple.classList.add('ripple-effect');
      
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      
      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${e.clientX - rect.left - size/2}px`;
      ripple.style.top = `${e.clientY - rect.top - size/2}px`;
      
      this.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });
  
  // Add typing indicator animation
  if (messageInput) {
    messageInput.addEventListener('focus', function() {
      this.parentElement.style.boxShadow = '0 0 0 2px var(--wa-teal)';
    });
    
    messageInput.addEventListener('blur', function() {
      this.parentElement.style.boxShadow = 'none';
    });
    
    // Show typing indicator in active chat
    let typingTimeout;
    messageInput.addEventListener('input', function() {
      const typingIndicator = document.querySelector('.typing-indicator');
      
      if (this.value.trim() !== '') {
        // Show send button with animation
        if (sendButton) {
          sendButton.classList.add('active');
        }
        
        // Show typing indicator to other user (simulated)
        if (!typingIndicator && messagesContainer) {
          const indicator = document.createElement('div');
          indicator.className = 'typing-indicator message incoming';
          indicator.innerHTML = `
            <div class="message-bubble">
              <div class="typing-dots">
                <span></span><span></span><span></span>
              </div>
            </div>
          `;
          
          // Only show typing indicator sometimes (simulated)
          if (Math.random() > 0.7) {
            messagesContainer.appendChild(indicator);
            
            // Scroll to the typing indicator
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
          }
          
          // Clear previous timeout
          clearTimeout(typingTimeout);
          
          // Remove typing indicator after a few seconds
          typingTimeout = setTimeout(() => {
            const indicator = document.querySelector('.typing-indicator');
            if (indicator) {
              indicator.classList.add('fade-out');
              setTimeout(() => {
                if (indicator.parentNode) {
                  indicator.parentNode.removeChild(indicator);
                }
              }, 500);
            }
          }, 3000);
        }
      } else {
        // Hide send button
        if (sendButton) {
          sendButton.classList.remove('active');
        }
      }
    });
  }
  
  // Add hover effects to footer icons
  chatFooterIcons.forEach(icon => {
    icon.addEventListener('mouseenter', function() {
      this.style.transition = 'transform 0.2s ease, color 0.2s ease';
      this.style.transform = 'scale(1.1)';
      this.style.color = 'var(--wa-teal)';
    });
    
    icon.addEventListener('mouseleave', function() {
      this.style.transform = 'scale(1)';
      this.style.color = 'var(--wa-icon-color)';
    });
    
    // Add click effect
    icon.addEventListener('click', function() {
      this.style.transform = 'scale(0.95)';
      setTimeout(() => {
        this.style.transform = 'scale(1)';
      }, 100);
    });
  });
  
  // Add hover effects to header icons
  headerIcons.forEach(icon => {
    icon.addEventListener('mouseenter', function() {
      this.style.transition = 'transform 0.2s ease, color 0.2s ease';
      this.style.transform = 'scale(1.1)';
      this.style.color = 'var(--wa-teal)';
    });
    
    icon.addEventListener('mouseleave', function() {
      this.style.transform = 'scale(1)';
      this.style.color = 'var(--wa-icon-color)';
    });
  });
  
  // Add message animations
  if (messagesContainer) {
    // Add staggered animation to existing messages
    const messages = messagesContainer.querySelectorAll('.message');
    messages.forEach((message, index) => {
      message.style.animationDelay = `${index * 0.05}s`;
    });
    
    // Add scroll shadow effect
    messagesContainer.addEventListener('scroll', function() {
      if (this.scrollTop > 20) {
        this.classList.add('scrolled');
      } else {
        this.classList.remove('scrolled');
      }
    });
  }
  
  // Add emoji picker functionality (simulated)
  const emojiButton = document.querySelector('.bi-emoji-smile');
  if (emojiButton && messageInput) {
    emojiButton.addEventListener('click', function() {
      // Common emojis
      const emojis = ['😊', '👍', '❤️', '🙏', '👋', '🔥', '😂', '🎉', '👏', '🌱', '🌿', '🍀', '🌾', '🌻', '🌽'];
      
      // Create emoji picker
      let emojiPicker = document.querySelector('.emoji-picker');
      
      if (!emojiPicker) {
        emojiPicker = document.createElement('div');
        emojiPicker.className = 'emoji-picker';
        
        // Add emojis
        emojis.forEach(emoji => {
          const emojiSpan = document.createElement('span');
          emojiSpan.textContent = emoji;
          emojiSpan.addEventListener('click', function() {
            messageInput.value += emoji;
            messageInput.focus();
            
            // Trigger input event to show send button
            const inputEvent = new Event('input');
            messageInput.dispatchEvent(inputEvent);
          });
          
          emojiPicker.appendChild(emojiSpan);
        });
        
        // Add to DOM
        const chatFooter = document.querySelector('.chat-footer');
        if (chatFooter) {
          chatFooter.appendChild(emojiPicker);
        }
      } else {
        // Toggle visibility
        emojiPicker.classList.toggle('show');
      }
    });
    
    // Close emoji picker when clicking outside
    document.addEventListener('click', function(e) {
      const emojiPicker = document.querySelector('.emoji-picker');
      if (emojiPicker && !emojiPicker.contains(e.target) && e.target !== emojiButton) {
        emojiPicker.classList.remove('show');
      }
    });
  }
  
  // Add search functionality animation
  const searchInput = document.querySelector('.search-box input');
  if (searchInput) {
    searchInput.addEventListener('focus', function() {
      this.parentElement.style.transition = 'all 0.3s ease';
      this.parentElement.style.backgroundColor = '#fff';
      this.parentElement.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
    });
    
    searchInput.addEventListener('blur', function() {
      this.parentElement.style.boxShadow = 'none';
    });
  }
});
