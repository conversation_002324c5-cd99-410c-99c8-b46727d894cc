<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/uploads">Community Content</a></li>
        <li class="breadcrumb-item"><a href="/uploads/<%= upload.id %>"><%= upload.title %></a></li>
        <li class="breadcrumb-item active" aria-current="page">Share</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card shadow-sm">
      <div class="card-header bg-success text-white">
        <h3 class="mb-0">Share Content</h3>
      </div>
      <div class="card-body">
        <div class="mb-4">
          <h4><%= upload.title %></h4>
          <p class="text-muted"><%= upload.description %></p>
        </div>

        <div class="mb-4">
          <h5>Share via:</h5>
          <div class="d-flex flex-wrap gap-2 mt-3">
            <a href="<%= shareLinks.facebook %>" target="_blank" class="btn btn-primary">
              <i class="bi bi-facebook me-2"></i> Facebook
            </a>
            <a href="<%= shareLinks.twitter %>" target="_blank" class="btn btn-info text-white">
              <i class="bi bi-twitter me-2"></i> Twitter
            </a>
            <a href="<%= shareLinks.whatsapp %>" target="_blank" class="btn btn-success">
              <i class="bi bi-whatsapp me-2"></i> WhatsApp
            </a>
            <a href="<%= shareLinks.email %>" class="btn btn-secondary">
              <i class="bi bi-envelope me-2"></i> Email
            </a>
          </div>
        </div>

        <div class="mb-3">
          <h5>Or copy this link:</h5>
          <div class="input-group">
            <input type="text" class="form-control" id="shareLink" value="<%= shareLinks.copyLink %>" readonly>
            <button class="btn btn-outline-secondary" type="button" id="copyLinkBtn">
              <i class="bi bi-clipboard"></i> Copy
            </button>
          </div>
          <div class="form-text" id="copyStatus"></div>
        </div>

        <div class="mt-4">
          <a href="/uploads/<%= upload.id %>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i> Back to Content
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const copyLinkBtn = document.getElementById('copyLinkBtn');
    const shareLink = document.getElementById('shareLink');
    const copyStatus = document.getElementById('copyStatus');

    if (copyLinkBtn && shareLink) {
      copyLinkBtn.addEventListener('click', function() {
        // Select the text
        shareLink.select();
        shareLink.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text
        try {
          navigator.clipboard.writeText(shareLink.value)
            .then(() => {
              copyStatus.textContent = 'Link copied to clipboard!';
              copyStatus.classList.add('text-success');
              
              // Clear the status after 3 seconds
              setTimeout(() => {
                copyStatus.textContent = '';
                copyStatus.classList.remove('text-success');
              }, 3000);
            })
            .catch(err => {
              console.error('Failed to copy: ', err);
              // Fallback for older browsers
              document.execCommand('copy');
              copyStatus.textContent = 'Link copied to clipboard!';
              copyStatus.classList.add('text-success');
            });
        } catch (err) {
          console.error('Failed to copy: ', err);
          copyStatus.textContent = 'Failed to copy. Please select and copy manually.';
          copyStatus.classList.add('text-danger');
        }
      });
    }
  });
</script>
