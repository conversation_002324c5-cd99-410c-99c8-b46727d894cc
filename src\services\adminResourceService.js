import { db, storage } from '../config/initFirebase.js';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  orderBy, 
  serverTimestamp 
} from 'firebase/firestore';
import { ref, uploadString, getDownloadURL, deleteObject } from 'firebase/storage';

// Get all resources for admin
export const getAllResourcesAdmin = async () => {
  try {
    const resourcesSnapshot = await getDocs(
      query(collection(db, 'resources'), orderBy('createdAt', 'desc'))
    );
    
    const resources = [];
    resourcesSnapshot.forEach((doc) => {
      resources.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return resources;
  } catch (error) {
    console.error('Error getting resources for admin:', error);
    throw error;
  }
};

// Create a new resource
export const createResource = async (resourceData) => {
  try {
    // Handle image upload if provided
    if (resourceData.imageDataUrl) {
      const imageRef = ref(storage, `resources/${Date.now()}_${Math.random().toString(36).substring(2, 15)}`);
      await uploadString(imageRef, resourceData.imageDataUrl, 'data_url');
      resourceData.imageUrl = await getDownloadURL(imageRef);
      delete resourceData.imageDataUrl; // Remove the data URL as we now have the storage URL
    }
    
    // Add timestamp
    resourceData.createdAt = serverTimestamp();
    resourceData.updatedAt = serverTimestamp();
    
    // Add to Firestore
    const docRef = await addDoc(collection(db, 'resources'), resourceData);
    
    return {
      id: docRef.id,
      ...resourceData
    };
  } catch (error) {
    console.error('Error creating resource:', error);
    throw error;
  }
};

// Get a resource by ID
export const getResourceById = async (id) => {
  try {
    const resourceDoc = await getDoc(doc(db, 'resources', id));
    
    if (!resourceDoc.exists()) {
      return null;
    }
    
    return {
      id: resourceDoc.id,
      ...resourceDoc.data()
    };
  } catch (error) {
    console.error(`Error getting resource ${id}:`, error);
    throw error;
  }
};

// Update a resource
export const updateResource = async (id, resourceData) => {
  try {
    const resourceRef = doc(db, 'resources', id);
    const existingResource = await getDoc(resourceRef);
    
    if (!existingResource.exists()) {
      throw new Error('Resource not found');
    }
    
    // Handle image upload if provided
    if (resourceData.imageDataUrl) {
      // Delete old image if it exists
      if (existingResource.data().imageUrl) {
        try {
          const oldImageRef = ref(storage, existingResource.data().imageUrl);
          await deleteObject(oldImageRef);
        } catch (error) {
          console.warn('Error deleting old image:', error);
          // Continue even if old image deletion fails
        }
      }
      
      // Upload new image
      const imageRef = ref(storage, `resources/${Date.now()}_${Math.random().toString(36).substring(2, 15)}`);
      await uploadString(imageRef, resourceData.imageDataUrl, 'data_url');
      resourceData.imageUrl = await getDownloadURL(imageRef);
      delete resourceData.imageDataUrl; // Remove the data URL as we now have the storage URL
    }
    
    // Add timestamp
    resourceData.updatedAt = serverTimestamp();
    
    // Update in Firestore
    await updateDoc(resourceRef, resourceData);
    
    return {
      id,
      ...resourceData
    };
  } catch (error) {
    console.error(`Error updating resource ${id}:`, error);
    throw error;
  }
};

// Delete a resource
export const deleteResource = async (id) => {
  try {
    const resourceRef = doc(db, 'resources', id);
    const existingResource = await getDoc(resourceRef);
    
    if (!existingResource.exists()) {
      throw new Error('Resource not found');
    }
    
    // Delete image if it exists
    if (existingResource.data().imageUrl) {
      try {
        const imageRef = ref(storage, existingResource.data().imageUrl);
        await deleteObject(imageRef);
      } catch (error) {
        console.warn('Error deleting image:', error);
        // Continue even if image deletion fails
      }
    }
    
    // Delete from Firestore
    await deleteDoc(resourceRef);
    
    return { success: true };
  } catch (error) {
    console.error(`Error deleting resource ${id}:`, error);
    throw error;
  }
};

// Toggle resource published status
export const toggleResourcePublished = async (id, isPublished) => {
  try {
    const resourceRef = doc(db, 'resources', id);
    
    await updateDoc(resourceRef, {
      isPublished: isPublished,
      updatedAt: serverTimestamp()
    });
    
    return { success: true, isPublished };
  } catch (error) {
    console.error(`Error toggling published status for resource ${id}:`, error);
    throw error;
  }
};
