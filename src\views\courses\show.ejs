<div class="course-container">
  <!-- Breadcrumb Navigation -->
  <nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="/" class="text-decoration-none">Home</a></li>
      <li class="breadcrumb-item"><a href="/courses" class="text-decoration-none">Courses</a></li>
      <% if (course && course.category) { %>
        <li class="breadcrumb-item"><a href="/courses/category/<%= course.category %>" class="text-decoration-none"><%= course.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></a></li>
      <% } %>
      <li class="breadcrumb-item active" aria-current="page"><%= course ? course.title : 'Course Details' %></li>
    </ol>
  </nav>

  <% if (typeof error !== 'undefined') { %>
    <div class="course-alert course-alert-warning">
      <i class="bi bi-exclamation-triangle me-2"></i>
      <%= error %>
    </div>
  <% } %>

  <% if (!course) { %>
    <div class="course-alert course-alert-warning text-center">
      <i class="bi bi-exclamation-triangle fs-1 mb-3 d-block"></i>
      <h4>Course Not Found</h4>
      <p class="mb-3">The course you're looking for doesn't exist or an error occurred.</p>
      <a href="/courses" class="course-btn course-btn-primary">
        <i class="bi bi-arrow-left"></i>
        Back to Courses
      </a>
    </div>
  <% } else { %>
    <!-- Modern Course Header -->
    <div class="course-header">
      <div class="row align-items-center">
        <div class="col-lg-8">
          <div class="d-flex align-items-center mb-3">
            <a href="/courses" class="course-btn course-btn-outline me-3" style="background: rgba(255,255,255,0.2); border-color: rgba(255,255,255,0.3); color: white;">
              <i class="bi bi-arrow-left"></i>
              Back to Courses
            </a>
            <span class="course-badge <%= course.level %>" style="background: rgba(255,255,255,0.2); color: white;">
              <%= course.level %>
            </span>
            <% if (course.isPremium) { %>
              <span class="course-badge premium" style="background: linear-gradient(135deg, #FFD700, #FFA500); color: #333; font-weight: bold;">
                <i class="bi bi-gem"></i> Premium
              </span>
            <% } %>
          </div>
          <h1>
            <%= course.title %>
            <% if (course.isPremium) { %>
              <i class="bi bi-star-fill text-warning ms-2" title="Premium Course"></i>
            <% } %>
          </h1>
          <p class="lead"><%= course.description %></p>
          <% if (course.isPremium) { %>
            <div class="course-price-info mb-3" style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 8px; border-left: 4px solid #FFD700;">
              <div class="d-flex align-items-center justify-content-between">
                <div>
                  <span class="course-price" style="font-size: 1.5rem; font-weight: bold; color: #FFD700;">
                    $<%= course.price %> <%= course.currency %>
                  </span>
                  <div class="course-price-note" style="color: rgba(255,255,255,0.8); font-size: 0.875rem;">
                    <i class="bi bi-shield-check me-1"></i>
                    One-time payment • Admin approval required
                  </div>
                </div>
                <div class="text-end">
                  <i class="bi bi-credit-card" style="font-size: 2rem; color: rgba(255,255,255,0.3);"></i>
                </div>
              </div>
            </div>
          <% } %>
          <div class="course-meta">
            <div class="course-meta-item">
              <i class="bi bi-person-circle"></i>
              <span><%= course.authorName %></span>
            </div>
            <div class="course-meta-item">
              <i class="bi bi-clock"></i>
              <span><%= Math.round(course.duration / 60) %> hours</span>
            </div>
            <div class="course-meta-item">
              <i class="bi bi-collection"></i>
              <span><%= course.moduleCount %> modules</span>
            </div>
            <div class="course-meta-item">
              <i class="bi bi-people"></i>
              <span><%= course.enrollmentCount %> enrolled</span>
            </div>
            <div class="course-meta-item">
              <i class="bi bi-tag"></i>
              <span><%= course.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></span>
            </div>
          </div>
        </div>
        <div class="col-lg-4 text-center">
          <div class="course-card-image" style="height: 200px; border-radius: 12px; margin-bottom: 20px;">
            <% if (course.imageUrl) { %>
              <img src="<%= course.imageUrl %>" alt="<%= course.title %>" style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;">
            <% } else { %>
              <% if (course.category === 'organic-farming') { %>
                <i class="bi bi-flower1"></i>
              <% } else if (course.category === 'water-conservation') { %>
                <i class="bi bi-droplet"></i>
              <% } else if (course.category === 'renewable-energy') { %>
                <i class="bi bi-sun"></i>
              <% } else if (course.category === 'soil-health') { %>
                <i class="bi bi-layers"></i>
              <% } else { %>
                <i class="bi bi-book"></i>
              <% } %>
            <% } %>
          </div>

          <% if (!course.isPublished) { %>
            <div class="course-alert course-alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <strong>Course in Development</strong><br>
              This course is currently being developed and will be available soon.
            </div>
          <% } else if (typeof user !== 'undefined' && user) { %>
            <% if (progress) { %>
              <div class="mb-3">
                <div class="course-progress">
                  <div class="course-progress-bar" style="width: <%= progress.progress %>%;"></div>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                  <small style="color: rgba(255,255,255,0.8);">Progress: <%= progress.progress %>% complete</small>
                  <% if (progress.isCompleted) { %>
                    <span class="course-badge beginner" style="background: rgba(255,255,255,0.2); color: white;">Completed</span>
                  <% } %>
                </div>
              </div>
              <div class="d-grid gap-2">
                <% if (progress.isCompleted) { %>
                  <a href="/courses/<%= course.id %>/complete" class="course-btn course-btn-primary w-100" style="background: white; color: var(--course-primary);">
                    <i class="bi bi-award"></i>
                    View Certificate
                  </a>
                <% } else if (progress.currentModuleId) { %>
                  <a href="/courses/<%= course.id %>/module/<%= progress.currentModuleId %>" class="course-btn course-btn-primary w-100" style="background: white; color: var(--course-primary);">
                    <i class="bi bi-play-fill"></i>
                    Continue Learning
                  </a>
                <% } else { %>
                  <% if (modules && modules.length > 0) { %>
                    <a href="/courses/<%= course.id %>/module/<%= modules[0].id %>" class="course-btn course-btn-primary w-100" style="background: white; color: var(--course-primary);">
                      <i class="bi bi-play-fill"></i>
                      Start Learning
                    </a>
                  <% } else { %>
                    <div class="course-btn course-btn-primary w-100" style="background: rgba(255,255,255,0.5); color: var(--course-primary); cursor: not-allowed; opacity: 0.6;">
                      <i class="bi bi-clock"></i>
                      Modules Coming Soon
                    </div>
                  <% } %>
                <% } %>
                <a href="/courses/my-courses" class="course-btn course-btn-outline w-100" style="background: rgba(255,255,255,0.1); border-color: rgba(255,255,255,0.3); color: white;">
                  <i class="bi bi-collection-play"></i>
                  My Courses
                </a>
              </div>
            <% } else { %>
              <% if (course.isPremium) { %>
                <% if (typeof enrollment !== 'undefined' && enrollment) { %>
                  <% if (enrollment.status === 'pending_approval') { %>
                    <div class="course-alert course-alert-info">
                      <i class="bi bi-clock me-2"></i>
                      <strong>Enrollment Pending</strong><br>
                      Your payment has been processed. Waiting for admin approval.
                    </div>
                  <% } else if (enrollment.status === 'rejected') { %>
                    <div class="course-alert course-alert-error">
                      <i class="bi bi-x-circle me-2"></i>
                      <strong>Enrollment Rejected</strong><br>
                      <%= enrollment.rejectionReason || 'Please contact support for more information.' %>
                    </div>
                  <% } %>
                <% } else { %>
                  <a href="/courses/<%= course.id %>/purchase" class="course-btn course-btn-primary w-100" style="background: linear-gradient(135deg, #FFD700, #FFA500); color: #333; font-weight: bold;">
                    <i class="bi bi-credit-card"></i>
                    Purchase Course - $<%= course.price %>
                  </a>
                  <div class="mt-2 text-center">
                    <small style="color: rgba(255,255,255,0.7);">
                      <i class="bi bi-shield-check me-1"></i>
                      Secure payment • Admin approval required
                    </small>
                  </div>
                <% } %>
              <% } else { %>
                <form action="/courses/<%= course.id %>/enroll" method="POST" class="d-grid">
                  <button type="submit" class="course-btn course-btn-primary w-100" style="background: white; color: var(--course-primary);">
                    <i class="bi bi-journal-plus"></i>
                    Enroll in Course (Free)
                  </button>
                </form>
              <% } %>
            <% } %>
          <% } else { %>
            <a href="/auth/login" class="course-btn course-btn-primary w-100" style="background: white; color: var(--course-primary);">
              <i class="bi bi-box-arrow-in-right"></i>
              Login to Enroll
            </a>
          <% } %>
        </div>
      </div>
    </div>
    <!-- Course Content Section -->
    <div class="row">
      <div class="col-lg-8">
        <!-- What You'll Learn -->
        <% if (course.objectives && course.objectives.length > 0) { %>
          <div class="course-modules mb-4">
            <div class="course-module-item">
              <div class="course-module-header" style="background: var(--course-light);">
                <h4 class="course-module-title">
                  <i class="bi bi-lightbulb me-2"></i>
                  What You'll Learn
                </h4>
              </div>
              <div class="course-module-content">
                <div class="row">
                  <% course.objectives.forEach((objective, index) => { %>
                    <div class="col-md-6 mb-2">
                      <div class="d-flex align-items-start">
                        <i class="bi bi-check-circle-fill text-success me-2 mt-1"></i>
                        <span><%= objective %></span>
                      </div>
                    </div>
                  <% }); %>
                </div>
              </div>
            </div>
          </div>
        <% } %>

        <!-- Course Modules -->
        <div class="course-modules">
          <div class="course-module-item">
            <div class="course-module-header" style="background: var(--course-light);">
              <h4 class="course-module-title">
                <i class="bi bi-collection me-2"></i>
                Course Modules
              </h4>
              <span class="course-module-duration">
                <%= (modules && modules.length) ? modules.length : 0 %> modules
              </span>
            </div>
          </div>
          <% if (modules && modules.length > 0) { %>
            <% modules.forEach((module, index) => { %>
              <div class="course-module-item">
                <button class="course-module-header" onclick="toggleModule('<%= module.id %>')">
                  <div class="course-module-title">
                    <span class="fw-bold">Module <%= index + 1 %>:</span> <%= module.title %>
                  </div>
                  <div class="d-flex align-items-center gap-2">
                    <% if (progress && progress.completedModules && progress.completedModules.includes(module.id)) { %>
                      <span class="course-badge beginner">
                        <i class="bi bi-check-circle"></i> Completed
                      </span>
                    <% } %>
                    <span class="course-module-duration">
                      <i class="bi bi-clock"></i> <%= module.duration %> min
                    </span>
                    <i class="bi bi-chevron-down" id="chevron-<%= module.id %>"></i>
                  </div>
                </button>
                <div class="course-module-content" id="module-<%= module.id %>" style="display: none;">
                  <p><%= module.description %></p>
                  <% if (progress && progress.completedModules && progress.completedModules.includes(module.id)) { %>
                    <div class="d-flex justify-content-between align-items-center">
                      <span class="text-success">
                        <i class="bi bi-check-circle-fill me-1"></i>
                        Module completed successfully
                      </span>
                      <a href="/courses/<%= course.id %>/module/<%= module.id %>" class="course-btn course-btn-outline">
                        <i class="bi bi-arrow-repeat"></i>
                        Review Module
                      </a>
                    </div>
                  <% } else if (progress) { %>
                    <div class="text-end">
                      <a href="/courses/<%= course.id %>/module/<%= module.id %>" class="course-btn course-btn-primary">
                        <i class="bi bi-play-fill"></i>
                        Start Module
                      </a>
                    </div>
                  <% } %>
                </div>
              </div>
            <% }); %>
          <% } else { %>
            <div class="course-module-item">
              <div class="course-module-content">
                <div class="course-alert course-alert-info text-center">
                  <i class="bi bi-info-circle fs-1 mb-3 d-block"></i>
                  <h5>Modules Coming Soon</h5>
                  <p class="mb-0">Course modules are being developed and will be available soon.</p>
                </div>
              </div>
            </div>
          <% } %>
        </div>
            </div>
          </div>
        </div>

        <% if (course.prerequisites && course.prerequisites.length > 0) { %>
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
              <h4 class="mb-0">Prerequisites</h4>
            </div>
            <div class="card-body">
              <ul class="list-group list-group-flush">
                <% course.prerequisites.forEach(prerequisite => { %>
                  <li class="list-group-item bg-transparent">
                    <i class="bi bi-arrow-right text-success me-2"></i> <%= prerequisite %>
                  </li>
                <% }); %>
              </ul>
            </div>
          </div>
        <% } %>
      </div>

      <!-- Sidebar -->
      <div class="col-md-4">
        <% if (typeof user !== 'undefined' && user && progress) { %>
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
              <h5 class="mb-0">Your Progress</h5>
            </div>
            <div class="card-body">
              <div class="progress mb-3" style="height: 10px;">
                <div class="progress-bar bg-success" role="progressbar" style="width: <%= progress.progress %>%;" aria-valuenow="<%= progress.progress %>" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <div class="d-flex justify-content-between align-items-center mb-3">
                <span><%= progress.progress %>% complete</span>
                <% if (progress.isCompleted) { %>
                  <span class="badge bg-success">Completed</span>
                <% } %>
              </div>

              <div class="mb-3">
                <h6>Modules Completed:</h6>
                <p><%= progress.completedModules ? progress.completedModules.length : 0 %> of <%= (modules && modules.length) ? modules.length : 0 %></p>
              </div>

              <% if (progress.lastAccessedAt) { %>
                <div class="mb-3">
                  <h6>Last Accessed:</h6>
                  <p><%= new Date(progress.lastAccessedAt).toLocaleDateString() %></p>
                </div>
              <% } %>

              <% if (progress.enrolledAt) { %>
                <div class="mb-3">
                  <h6>Enrolled On:</h6>
                  <p><%= new Date(progress.enrolledAt).toLocaleDateString() %></p>
                </div>
              <% } %>

              <% if (progress.isCompleted && progress.completedAt) { %>
                <div class="mb-3">
                  <h6>Completed On:</h6>
                  <p><%= new Date(progress.completedAt).toLocaleDateString() %></p>
                </div>
              <% } %>

              <% if (!progress.isCompleted && progress.currentModuleId) { %>
                <div class="d-grid">
                  <a href="/courses/<%= course.id %>/module/<%= progress.currentModuleId %>" class="btn btn-success">
                    <i class="bi bi-play-fill"></i> Continue Learning
                  </a>
                </div>
              <% } %>
            </div>
          </div>

          <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
              <h5 class="mb-0">My Notes</h5>
            </div>
            <div class="card-body">
              <form action="/courses/<%= course.id %>/notes" method="POST">
                <div class="mb-3">
                  <textarea class="form-control" name="notes" rows="5" placeholder="Add your personal notes about this course here..."><%= progress.notes || '' %></textarea>
                </div>
                <div class="d-grid">
                  <button type="submit" class="btn btn-success">
                    <i class="bi bi-save"></i> Save Notes
                  </button>
                </div>
              </form>
            </div>
          </div>
        <% } %>

        <div class="card shadow-sm mb-4">
          <div class="card-header bg-white">
            <h5 class="mb-0">Related Courses</h5>
          </div>
          <div class="card-body">
            <div class="list-group list-group-flush">
              <% if (course.category === 'organic-farming') { %>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Natural Pest Control</h6>
                    <small class="text-muted">Intermediate</small>
                  </div>
                  <small class="text-muted">Learn effective methods for controlling pests without chemicals</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Organic Certification</h6>
                    <small class="text-muted">Beginner</small>
                  </div>
                  <small class="text-muted">Navigate the process of getting your farm certified organic</small>
                </a>
              <% } else if (course.category === 'water-conservation') { %>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Drip Irrigation Systems</h6>
                    <small class="text-muted">Intermediate</small>
                  </div>
                  <small class="text-muted">Design and implement efficient drip irrigation</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Rainwater Harvesting</h6>
                    <small class="text-muted">Beginner</small>
                  </div>
                  <small class="text-muted">Collect and store rainwater for agricultural use</small>
                </a>
              <% } else if (course.category === 'soil-health') { %>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Composting Fundamentals</h6>
                    <small class="text-muted">Beginner</small>
                  </div>
                  <small class="text-muted">Create nutrient-rich compost for your farm</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Cover Crops Guide</h6>
                    <small class="text-muted">Intermediate</small>
                  </div>
                  <small class="text-muted">Use cover crops to improve soil health</small>
                </a>
              <% } else { %>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Related Course 1</h6>
                    <small class="text-muted">Beginner</small>
                  </div>
                  <small class="text-muted">Description of related course</small>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Related Course 2</h6>
                    <small class="text-muted">Intermediate</small>
                  </div>
                  <small class="text-muted">Description of related course</small>
                </a>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% } %>

  <div class="row mt-4">
    <div class="col-md-12 text-center">
      <a href="/courses" class="course-btn course-btn-outline">
        <i class="bi bi-arrow-left"></i>
        Back to All Courses
      </a>
    </div>
  </div>
</div>

<script>
function toggleModule(moduleId) {
  const content = document.getElementById('module-' + moduleId);
  const chevron = document.getElementById('chevron-' + moduleId);
  const header = chevron.closest('.course-module-header');

  if (content.style.display === 'none' || content.style.display === '') {
    content.style.display = 'block';
    header.classList.add('active');
  } else {
    content.style.display = 'none';
    header.classList.remove('active');
  }
}
</script>
