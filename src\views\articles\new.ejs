<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/articles">Articles</a></li>
        <li class="breadcrumb-item active" aria-current="page">Write New Article</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row justify-content-center">
  <div class="col-md-10">
    <div class="card shadow-sm">
      <div class="card-header bg-success text-white">
        <h3 class="mb-0">Write New Article</h3>
      </div>
      <div class="card-body">
        <% if (typeof error !== 'undefined') { %>
          <div class="alert alert-danger" role="alert">
            <%= error %>
          </div>
        <% } %>

        <form action="/articles" method="POST">
          <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" required value="<%= typeof formData !== 'undefined' && formData.title ? formData.title : '' %>">
          </div>

          <div class="mb-3">
            <label for="summary" class="form-label">Summary</label>
            <textarea class="form-control" id="summary" name="summary" rows="2" required><%= typeof formData !== 'undefined' && formData.summary ? formData.summary : '' %></textarea>
            <div class="form-text">A brief summary of your article (will be displayed in article listings).</div>
          </div>

          <div class="mb-3">
            <label for="content" class="form-label">Content</label>
            <textarea class="form-control" id="content" name="content" rows="15" required><%= typeof formData !== 'undefined' && formData.content ? formData.content : '' %></textarea>
            <div class="form-text">Use the rich text editor to format your content. Works on both mobile and desktop.</div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="category" class="form-label">Category</label>
              <select class="form-select" id="category" name="category" required>
                <option value="" disabled <%= typeof formData === 'undefined' || !formData.category ? 'selected' : '' %>>Select category</option>
                <option value="Sustainable Practices" <%= typeof formData !== 'undefined' && formData.category === 'Sustainable Practices' ? 'selected' : '' %>>Sustainable Practices</option>
                <option value="Pest Control" <%= typeof formData !== 'undefined' && formData.category === 'Pest Control' ? 'selected' : '' %>>Pest Control</option>
                <option value="Soil Health" <%= typeof formData !== 'undefined' && formData.category === 'Soil Health' ? 'selected' : '' %>>Soil Health</option>
                <option value="Water Conservation" <%= typeof formData !== 'undefined' && formData.category === 'Water Conservation' ? 'selected' : '' %>>Water Conservation</option>
                <option value="Crop Rotation" <%= typeof formData !== 'undefined' && formData.category === 'Crop Rotation' ? 'selected' : '' %>>Crop Rotation</option>
                <option value="Other" <%= typeof formData !== 'undefined' && formData.category === 'Other' ? 'selected' : '' %>>Other</option>
              </select>
            </div>

            <div class="col-md-6 mb-3">
              <label for="tags" class="form-label">Tags</label>
              <input type="text" class="form-control" id="tags" name="tags" value="<%= typeof formData !== 'undefined' && formData.tags ? formData.tags : '' %>">
              <div class="form-text">Comma-separated tags (e.g., organic, sustainable, farming).</div>
            </div>
          </div>

          <div class="mb-3 image-upload-container">
            <label for="imageUrl" class="form-label">Featured Image</label>
            <div class="input-group mb-2">
              <input type="url" class="form-control image-url-input" id="imageUrl" name="imageUrl" placeholder="Enter image URL or use buttons below" value="<%= typeof formData !== 'undefined' && formData.imageUrl ? formData.imageUrl : '' %>">
              <button class="btn btn-outline-secondary clear-image-btn <%= typeof formData !== 'undefined' && formData.imageUrl ? '' : 'd-none' %>" type="button">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
            <div class="d-flex gap-2 mb-2">
              <button type="button" class="btn btn-outline-success take-photo-btn d-none flex-grow-1">
                <i class="bi bi-camera"></i> Take Photo
              </button>
              <button type="button" class="btn btn-outline-success select-image-btn flex-grow-1">
                <i class="bi bi-image"></i> Select Image
              </button>
            </div>
            <input type="file" class="d-none file-input" accept="image/*">
            <div class="image-preview-container <%= typeof formData !== 'undefined' && formData.imageUrl ? '' : 'd-none' %> mt-2">
              <img src="<%= typeof formData !== 'undefined' && formData.imageUrl ? formData.imageUrl : '' %>" class="image-preview img-fluid rounded" alt="Article preview">
            </div>
            <div class="form-text">Add a featured image to make your article more engaging.</div>
          </div>

          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-success">Publish Article</button>
            <a href="/articles" class="btn btn-outline-secondary">Cancel</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Include TinyMCE and image upload script -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script src="/js/image-upload.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE with mobile-friendly settings
    tinymce.init({
      selector: '#content',
      plugins: 'autoresize anchor autolink charmap code codesample directionality fullscreen help image insertdatetime link lists media nonbreaking pagebreak preview searchreplace table template visualblocks visualchars wordcount',
      toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | removeformat',
      menubar: 'file edit view insert format tools table help',
      toolbar_mode: 'sliding',
      contextmenu: 'link image table',
      height: 400,
      mobile: {
        menubar: true,
        plugins: 'autoresize anchor autolink charmap code codesample directionality fullscreen help image insertdatetime link lists media nonbreaking pagebreak preview searchreplace table template visualblocks visualchars wordcount',
        toolbar: 'undo redo | bold italic underline | link image | bullist numlist | styleselect'
      },
      setup: function(editor) {
        editor.on('change', function() {
          editor.save(); // Save content to textarea
        });
      },
      content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 16px; }'
    });

    // Add form validation to ensure TinyMCE content is saved
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
      // Make sure TinyMCE content is saved to the textarea
      if (tinymce.activeEditor) {
        tinymce.activeEditor.save();
      }
    });
  });
</script>
