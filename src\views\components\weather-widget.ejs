<div class="card mb-4">
  <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
    <h5 class="mb-0">Weather Forecast</h5>
    <button class="btn btn-sm btn-outline-light" id="refresh-weather">
      <i class="bi bi-arrow-clockwise"></i>
    </button>
  </div>
  <div class="card-body">
    <div id="weather-container">
      <div class="text-center py-3" id="weather-loading">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading weather data...</p>
      </div>
      
      <div id="weather-content" style="display: none;">
        <div class="d-flex align-items-center mb-3">
          <div class="weather-icon me-3">
            <i class="bi bi-cloud-sun fs-1"></i>
          </div>
          <div>
            <h3 class="mb-0" id="weather-location">Your Location</h3>
            <div class="text-muted" id="weather-date">Today</div>
          </div>
          <div class="ms-auto">
            <span class="fs-2" id="weather-temp">--°C</span>
          </div>
        </div>
        
        <div class="row text-center mb-3">
          <div class="col-4">
            <div class="weather-detail">
              <i class="bi bi-droplet"></i>
              <div>Humidity</div>
              <strong id="weather-humidity">--%</strong>
            </div>
          </div>
          <div class="col-4">
            <div class="weather-detail">
              <i class="bi bi-wind"></i>
              <div>Wind</div>
              <strong id="weather-wind">-- km/h</strong>
            </div>
          </div>
          <div class="col-4">
            <div class="weather-detail">
              <i class="bi bi-umbrella"></i>
              <div>Precip.</div>
              <strong id="weather-precip">-- mm</strong>
            </div>
          </div>
        </div>
        
        <div class="forecast-days">
          <div class="row" id="forecast-container">
            <!-- Forecast days will be inserted here -->
          </div>
        </div>
      </div>
      
      <div id="weather-error" class="alert alert-warning" style="display: none;">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <span id="weather-error-message">Unable to load weather data. Please try again later.</span>
      </div>
    </div>
    
    <div class="mt-3">
      <div class="input-group">
        <input type="text" class="form-control" placeholder="Enter location" id="weather-location-input">
        <button class="btn btn-primary" type="button" id="weather-search-btn">
          <i class="bi bi-search"></i>
        </button>
      </div>
      <div class="form-text">Enter city name or postal code</div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Sample weather data for demonstration
    const sampleWeatherData = {
      location: "Sample Farm Location",
      current: {
        temp_c: 22,
        humidity: 65,
        wind_kph: 12,
        precip_mm: 0,
        condition: {
          text: "Partly cloudy",
          icon: "partly-cloudy"
        }
      },
      forecast: [
        { date: "Mon", temp_c: 22, icon: "sun" },
        { date: "Tue", temp_c: 24, icon: "cloud-sun" },
        { date: "Wed", temp_c: 20, icon: "cloud" },
        { date: "Thu", temp_c: 19, icon: "cloud-rain" },
        { date: "Fri", temp_c: 21, icon: "sun" }
      ]
    };
    
    // Function to display weather data
    function displayWeatherData(data) {
      document.getElementById('weather-loading').style.display = 'none';
      document.getElementById('weather-content').style.display = 'block';
      document.getElementById('weather-error').style.display = 'none';
      
      // Update current weather
      document.getElementById('weather-location').textContent = data.location;
      document.getElementById('weather-date').textContent = new Date().toLocaleDateString();
      document.getElementById('weather-temp').textContent = `${data.current.temp_c}°C`;
      document.getElementById('weather-humidity').textContent = `${data.current.humidity}%`;
      document.getElementById('weather-wind').textContent = `${data.current.wind_kph} km/h`;
      document.getElementById('weather-precip').textContent = `${data.current.precip_mm} mm`;
      
      // Update forecast
      const forecastContainer = document.getElementById('forecast-container');
      forecastContainer.innerHTML = '';
      
      data.forecast.forEach(day => {
        const dayElement = document.createElement('div');
        dayElement.className = 'col';
        dayElement.innerHTML = `
          <div class="forecast-day">
            <div>${day.date}</div>
            <i class="bi bi-${day.icon}"></i>
            <div>${day.temp_c}°C</div>
          </div>
        `;
        forecastContainer.appendChild(dayElement);
      });
    }
    
    // Simulate loading weather data
    setTimeout(() => {
      displayWeatherData(sampleWeatherData);
    }, 1500);
    
    // Handle refresh button
    document.getElementById('refresh-weather').addEventListener('click', function() {
      document.getElementById('weather-loading').style.display = 'block';
      document.getElementById('weather-content').style.display = 'none';
      document.getElementById('weather-error').style.display = 'none';
      
      // Simulate refreshing data
      setTimeout(() => {
        displayWeatherData(sampleWeatherData);
      }, 1000);
    });
    
    // Handle location search
    document.getElementById('weather-search-btn').addEventListener('click', function() {
      const location = document.getElementById('weather-location-input').value.trim();
      
      if (location) {
        document.getElementById('weather-loading').style.display = 'block';
        document.getElementById('weather-content').style.display = 'none';
        document.getElementById('weather-error').style.display = 'none';
        
        // Simulate searching for a location
        setTimeout(() => {
          const newData = {...sampleWeatherData};
          newData.location = location;
          displayWeatherData(newData);
        }, 1000);
      }
    });
    
    // Handle enter key in search input
    document.getElementById('weather-location-input').addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        document.getElementById('weather-search-btn').click();
      }
    });
  });
</script>

<style>
  .weather-detail {
    padding: 10px;
  }
  
  .weather-detail i {
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: #0d6efd;
  }
  
  .forecast-day {
    text-align: center;
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fa;
    margin: 5px 0;
  }
  
  .forecast-day i {
    font-size: 1.25rem;
    margin: 5px 0;
    color: #0d6efd;
  }
</style>
