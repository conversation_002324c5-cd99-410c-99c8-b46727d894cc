/**
 * Facebook-style Dashboard Interactions
 * Adds interactive elements and animations to mimic Facebook's behavior
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize Facebook-style interactions
  initFacebookDashboard();

  // Add agricultural theme elements
  addAgriculturalElements();

  // Initialize post interactions
  initPostInteractions();

  // Initialize scrolling effects
  initScrollEffects();

  // Initialize time-based greeting
  updateTimeGreeting();

  // Initialize content category filter
  initCategoryFilter();
});

/**
 * Initialize the Facebook-style dashboard
 */
function initFacebookDashboard() {
  // Add ripple effect to menu items
  const menuItems = document.querySelectorAll('.fb-menu-item');

  menuItems.forEach(item => {
    // Add click effect
    item.addEventListener('click', function(e) {
      // Create ripple effect
      const ripple = document.createElement('div');
      ripple.classList.add('fb-ripple');

      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);

      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${e.clientX - rect.left - size/2}px`;
      ripple.style.top = `${e.clientY - rect.top - size/2}px`;

      this.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Create post input focus effect
  const createPostInput = document.querySelector('.fb-create-post-input');
  if (createPostInput) {
    createPostInput.addEventListener('click', function() {
      // In a real implementation, this would open a modal
      // For now, just add a visual effect
      this.style.backgroundColor = '#fff';
      this.style.boxShadow = '0 0 0 2px var(--agri-green)';

      setTimeout(() => {
        this.style.backgroundColor = '';
        this.style.boxShadow = '';
      }, 300);
    });
  }
}

/**
 * Add agricultural theme elements to the page
 */
function addAgriculturalElements() {
  // Add decorative leaf icons
  const body = document.body;

  const leaf1 = document.createElement('i');
  leaf1.className = 'bi bi-flower1 agri-leaf-icon agri-leaf-1';
  body.appendChild(leaf1);

  const leaf2 = document.createElement('i');
  leaf2.className = 'bi bi-flower2 agri-leaf-icon agri-leaf-2';
  body.appendChild(leaf2);

  // Add wheat icon to the header
  const headerLogo = document.querySelector('.fb-header-logo');
  if (headerLogo) {
    const wheatIcon = document.createElement('i');
    wheatIcon.className = 'bi bi-flower3 me-2';
    wheatIcon.style.fontSize = '24px';
    wheatIcon.style.color = '#F5DEB3';
    headerLogo.prepend(wheatIcon);
  }

  // Add seasonal effects based on current month
  const currentMonth = new Date().getMonth();
  let seasonalClass = '';

  // 0-2: Winter, 3-5: Spring, 6-8: Summer, 9-11: Fall
  if (currentMonth >= 2 && currentMonth <= 4) {
    // Spring
    seasonalClass = 'spring-theme';
    document.body.classList.add(seasonalClass);
  } else if (currentMonth >= 5 && currentMonth <= 7) {
    // Summer
    seasonalClass = 'summer-theme';
    document.body.classList.add(seasonalClass);
  } else if (currentMonth >= 8 && currentMonth <= 10) {
    // Fall
    seasonalClass = 'fall-theme';
    document.body.classList.add(seasonalClass);
  } else {
    // Winter
    seasonalClass = 'winter-theme';
    document.body.classList.add(seasonalClass);
  }
}

/**
 * Initialize post interactions like like, comment, share
 */
function initPostInteractions() {
  // Like button functionality
  const likeButtons = document.querySelectorAll('.fb-post-action-button.like');
  likeButtons.forEach(button => {
    button.addEventListener('click', function() {
      this.classList.toggle('liked');

      const icon = this.querySelector('i');
      const text = this.querySelector('span');

      if (this.classList.contains('liked')) {
        icon.classList.remove('bi-hand-thumbs-up');
        icon.classList.add('bi-hand-thumbs-up-fill');
        icon.style.color = 'var(--agri-green)';
        text.style.color = 'var(--agri-green)';
        text.textContent = 'Liked';

        // Add like animation
        const likeAnimation = document.createElement('div');
        likeAnimation.className = 'like-animation';
        this.appendChild(likeAnimation);

        setTimeout(() => {
          likeAnimation.remove();
        }, 700);
      } else {
        icon.classList.remove('bi-hand-thumbs-up-fill');
        icon.classList.add('bi-hand-thumbs-up');
        icon.style.color = '';
        text.style.color = '';
        text.textContent = 'Like';
      }
    });
  });

  // Comment button functionality
  const commentButtons = document.querySelectorAll('.fb-post-action-button.comment');
  commentButtons.forEach(button => {
    button.addEventListener('click', function() {
      const postId = this.closest('.fb-post').getAttribute('data-post-id');
      const commentInput = document.querySelector(`.fb-comment-input[data-post-id="${postId}"]`);

      if (commentInput) {
        commentInput.focus();

        // Scroll to comment input
        commentInput.scrollIntoView({ behavior: 'smooth' });
      }
    });
  });

  // Share button functionality
  const shareButtons = document.querySelectorAll('.fb-post-action-button.share');
  shareButtons.forEach(button => {
    button.addEventListener('click', function() {
      const postId = this.closest('.fb-post').getAttribute('data-post-id');

      // In a real implementation, this would open a share modal
      // For now, just add a visual effect
      this.style.backgroundColor = 'var(--fb-light-gray)';

      setTimeout(() => {
        this.style.backgroundColor = '';
        alert('Share functionality would open here!');
      }, 300);
    });
  });
}

/**
 * Initialize scrolling effects
 */
function initScrollEffects() {
  // Add scroll event listener
  window.addEventListener('scroll', function() {
    // Get scroll position
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Add shadow to header on scroll
    const header = document.querySelector('.fb-header');
    if (header) {
      if (scrollTop > 10) {
        header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
      } else {
        header.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      }
    }

    // Lazy load posts as they come into view
    const posts = document.querySelectorAll('.fb-post');
    posts.forEach(post => {
      const rect = post.getBoundingClientRect();

      // If post is in viewport
      if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
        post.classList.add('fb-post-visible');
      }
    });
  });
}

/**
 * Time-based greeting (Good morning/afternoon/evening)
 */
function updateTimeGreeting() {
  const greetingElement = document.getElementById('time-greeting');
  if (!greetingElement) return;

  const hour = new Date().getHours();
  let greeting = 'Good day';

  if (hour < 12) {
    greeting = 'Good morning';
  } else if (hour < 18) {
    greeting = 'Good afternoon';
  } else {
    greeting = 'Good evening';
  }

  greetingElement.textContent = greeting;
}

/**
 * Initialize content category filter
 */
function initCategoryFilter() {
  // Handle filter tabs
  const filterTabs = document.querySelectorAll('.filter-tab');
  if (filterTabs.length > 0) {
    filterTabs.forEach(tab => {
      tab.addEventListener('click', function() {
        // Remove active class from all tabs
        filterTabs.forEach(t => t.classList.remove('active'));

        // Add active class to clicked tab
        this.classList.add('active');

        // Get filter value
        const selectedCategory = this.getAttribute('data-filter');

        // Filter posts
        filterPosts(selectedCategory);
      });
    });
  }

  // Also handle the dropdown filter if it exists (for backward compatibility)
  const categoryFilter = document.getElementById('content-category-filter');
  if (categoryFilter) {
    categoryFilter.addEventListener('change', function() {
      filterPosts(this.value);
    });
  }
}

/**
 * Filter posts based on category
 */
function filterPosts(selectedCategory) {
  const posts = document.querySelectorAll('.fb-post');

  posts.forEach(post => {
    const postCategory = post.querySelector('.badge').textContent.trim().toLowerCase();

    if (selectedCategory === 'all' ||
        (selectedCategory === 'crops' && postCategory.includes('crop')) ||
        (selectedCategory === 'farming_techniques' && postCategory.includes('technique')) ||
        (selectedCategory === 'equipment' && postCategory.includes('equipment')) ||
        (selectedCategory === 'other' &&
         !postCategory.includes('crop') &&
         !postCategory.includes('technique') &&
         !postCategory.includes('equipment'))) {
      post.style.display = 'block';
    } else {
      post.style.display = 'none';
    }
  });

  // Add animation to visible posts
  const visiblePosts = document.querySelectorAll('.fb-post[style="display: block;"]');
  visiblePosts.forEach((post, index) => {
    post.style.animationDelay = `${index * 0.1}s`;
    post.style.animationName = 'none';
    setTimeout(() => {
      post.style.animationName = 'fadeIn';
    }, 10);
  });
}
