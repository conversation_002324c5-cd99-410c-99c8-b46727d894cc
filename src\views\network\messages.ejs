<%- include('../partials/header') %>

<div class="container mt-4">
  <div class="row">
    <!-- Left Sidebar -->
    <div class="col-lg-3 d-none d-lg-block">
      <!-- Quick Links -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Network</h5>
        </div>
        <div class="card-body p-0">
          <ul class="list-group list-group-flush">
            <li class="list-group-item">
              <a href="/network" class="text-decoration-none text-dark">
                <i class="bi bi-house-door-fill me-2 text-success"></i> Network Home
              </a>
            </li>
            <li class="list-group-item">
              <a href="/network/connections" class="text-decoration-none text-dark">
                <i class="bi bi-people-fill me-2 text-success"></i> My Connections
              </a>
            </li>
            <li class="list-group-item active bg-light">
              <a href="/network/messages" class="text-decoration-none text-dark">
                <i class="bi bi-chat-dots-fill me-2 text-success"></i> Messages
              </a>
            </li>
            <li class="list-group-item">
              <a href="/network/search" class="text-decoration-none text-dark">
                <i class="bi bi-search me-2 text-success"></i> Find Farmers
              </a>
            </li>
            <li class="list-group-item">
              <a href="/network/profile/<%= user.uid %>" class="text-decoration-none text-dark">
                <i class="bi bi-person-fill me-2 text-success"></i> My Profile
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-lg-9">
      <div class="card">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Messages</h5>
          <a href="/network/connections" class="btn btn-light btn-sm">
            <i class="bi bi-people-fill me-1"></i> My Connections
          </a>
        </div>
        <div class="card-body p-0">
          <div class="row g-0">
            <!-- Conversations list -->
            <div class="col-md-4 border-end">
              <div class="conversations-header p-3 border-bottom">
                <h6 class="mb-0">Conversations</h6>
              </div>
              
              <div class="conversations-list">
                <% if (conversations && conversations.length > 0) { %>
                  <% conversations.forEach(conversation => { %>
                    <a href="/network/messages/<%= conversation.id %>" class="conversation-item d-flex align-items-center p-3 border-bottom text-decoration-none text-dark <%= activeConversation && activeConversation.id === conversation.id ? 'bg-light' : '' %>">
                      <div class="me-3 position-relative">
                        <% if (conversation.otherUser.photoURL) { %>
                          <img src="<%= conversation.otherUser.photoURL %>" class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                        <% } else { %>
                          <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="bi bi-person-fill text-secondary" style="font-size: 1.5rem;"></i>
                          </div>
                        <% } %>
                        <% if (conversation.unreadCount > 0) { %>
                          <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <%= conversation.unreadCount %>
                          </span>
                        <% } %>
                      </div>
                      <div class="flex-grow-1 min-width-0">
                        <div class="d-flex justify-content-between align-items-center">
                          <h6 class="mb-0 text-truncate"><%= conversation.otherUser.displayName || 'User' %></h6>
                          <% if (conversation.lastMessageTimestamp) { %>
                            <small class="text-muted">
                              <%= new Date(conversation.lastMessageTimestamp).toLocaleDateString() %>
                            </small>
                          <% } %>
                        </div>
                        <p class="mb-0 small text-truncate text-muted">
                          <%= conversation.lastMessage || 'Start a conversation' %>
                        </p>
                      </div>
                    </a>
                  <% }); %>
                <% } else { %>
                  <div class="text-center py-5">
                    <i class="bi bi-chat-dots text-muted" style="font-size: 2rem;"></i>
                    <p class="mt-3 text-muted">No conversations yet</p>
                    <a href="/network/connections" class="btn btn-success btn-sm mt-2">
                      <i class="bi bi-people-fill me-1"></i> Find Connections
                    </a>
                  </div>
                <% } %>
              </div>
            </div>
            
            <!-- Message content area -->
            <div class="col-md-8">
              <% if (activeConversation) { %>
                <div class="message-header p-3 border-bottom d-flex align-items-center">
                  <% if (activeConversation.otherUser.photoURL) { %>
                    <img src="<%= activeConversation.otherUser.photoURL %>" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                  <% } else { %>
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                      <i class="bi bi-person-fill text-secondary"></i>
                    </div>
                  <% } %>
                  <div>
                    <h6 class="mb-0"><%= activeConversation.otherUser.displayName || 'User' %></h6>
                    <small class="text-muted">
                      <% if (activeConversation.otherUser.headline) { %>
                        <%= activeConversation.otherUser.headline %>
                      <% } else if (activeConversation.otherUser.location) { %>
                        <%= activeConversation.otherUser.location %>
                      <% } %>
                    </small>
                  </div>
                  <div class="ms-auto">
                    <a href="/network/profile/<%= activeConversation.otherUser.uid %>" class="btn btn-outline-success btn-sm">
                      <i class="bi bi-person-fill"></i>
                    </a>
                  </div>
                </div>
                
                <div class="message-content p-3" style="height: 400px; overflow-y: auto;" id="message-content">
                  <% if (messages && messages.length > 0) { %>
                    <% messages.reverse().forEach(message => { %>
                      <div class="message-bubble mb-3 <%= message.isCurrentUser ? 'text-end' : '' %>">
                        <div class="d-inline-block p-3 rounded <%= message.isCurrentUser ? 'bg-success text-white' : 'bg-light' %>" style="max-width: 75%;">
                          <p class="mb-0"><%= message.text %></p>
                          <small class="text-<%= message.isCurrentUser ? 'light' : 'muted' %>">
                            <%= new Date(message.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) %>
                          </small>
                        </div>
                      </div>
                    <% }); %>
                  <% } else { %>
                    <div class="text-center py-5">
                      <i class="bi bi-chat-text text-muted" style="font-size: 3rem;"></i>
                      <h5 class="mt-3">No messages yet</h5>
                      <p class="text-muted">Send a message to start the conversation</p>
                    </div>
                  <% } %>
                </div>
                
                <div class="message-input p-3 border-top">
                  <form id="message-form" data-conversation-id="<%= activeConversation.id %>">
                    <div class="input-group">
                      <input type="text" class="form-control" id="message-text" placeholder="Type a message..." required>
                      <button class="btn btn-success" type="submit">
                        <i class="bi bi-send"></i>
                      </button>
                    </div>
                  </form>
                </div>
              <% } else { %>
                <div class="text-center py-5">
                  <i class="bi bi-chat-square-text text-muted" style="font-size: 4rem;"></i>
                  <h5 class="mt-3">Select a conversation</h5>
                  <p class="text-muted">Choose a conversation from the list or start a new one</p>
                  <a href="/network/connections" class="btn btn-success mt-2">
                    <i class="bi bi-person-plus-fill me-1"></i> Connect with Farmers
                  </a>
                </div>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/network-messages.js"></script>

<%- include('../partials/footer') %>
