<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Bot Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Chart Bot Debug Tool</h1>
        
        <div class="debug-section">
            <h3>Script Loading Status</h3>
            <div id="script-status"></div>
        </div>
        
        <div class="debug-section">
            <h3>Function Availability</h3>
            <div id="function-status"></div>
        </div>
        
        <div class="debug-section">
            <h3>DOM Elements</h3>
            <div id="dom-status"></div>
        </div>
        
        <div class="debug-section">
            <h3>Console Output</h3>
            <div id="console-output"></div>
        </div>
        
        <div class="debug-section">
            <h3>Test Functions</h3>
            <button class="btn btn-primary me-2" onclick="testSendMessage()">Test sendMessage</button>
            <button class="btn btn-secondary me-2" onclick="testSendSuggestion()">Test sendSuggestion</button>
            <button class="btn btn-success me-2" onclick="testEventListeners()">Test Event Listeners</button>
            <button class="btn btn-info" onclick="refreshDebug()">Refresh Debug</button>
        </div>
    </div>

    <!-- Load the same scripts as chart-bot page -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/toast-notifications.js"></script>
    <script src="/js/chart-bot.js"></script>

    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = [];
        
        console.log = function(...args) {
            consoleOutput.push({type: 'log', message: args.join(' ')});
            originalLog.apply(console, args);
            updateConsoleOutput();
        };
        
        console.error = function(...args) {
            consoleOutput.push({type: 'error', message: args.join(' ')});
            originalError.apply(console, args);
            updateConsoleOutput();
        };
        
        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            output.innerHTML = consoleOutput.slice(-10).map(entry => 
                `<div class="${entry.type}">${entry.message}</div>`
            ).join('');
        }
        
        function checkScripts() {
            const scripts = [
                'bootstrap.bundle.min.js',
                'chart.js',
                'toast-notifications.js',
                'chart-bot.js'
            ];
            
            let html = '<ul>';
            scripts.forEach(script => {
                const loaded = Array.from(document.scripts).some(s => s.src.includes(script));
                html += `<li class="${loaded ? 'success' : 'error'}">${script}: ${loaded ? 'Loaded' : 'Not Found'}</li>`;
            });
            html += '</ul>';
            
            document.getElementById('script-status').innerHTML = html;
        }
        
        function checkFunctions() {
            const functions = [
                'initializeChartBot', 'setupEventListeners', 'sendMessage', 'sendSuggestion', 
                'handleChatKeyPress', 'showSavedCharts', 'generateSampleChart', 
                'showMarketData', 'exportChatHistory', 'clearChatHistory', 'showNotification'
            ];
            
            let html = '<ul>';
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                html += `<li class="${exists ? 'success' : 'error'}">${funcName}: ${exists ? 'Available' : 'Missing'}</li>`;
            });
            html += '</ul>';
            
            document.getElementById('function-status').innerHTML = html;
        }
        
        function checkDOMElements() {
            const elements = [
                'chat-input', 'send-button', 'clear-history-btn', 'show-charts-btn',
                'generate-chart-btn', 'market-data-btn', 'export-history-btn'
            ];
            
            let html = '<ul>';
            elements.forEach(id => {
                const element = document.getElementById(id);
                html += `<li class="${element ? 'success' : 'warning'}">${id}: ${element ? 'Found' : 'Not Found'}</li>`;
            });
            html += '</ul>';
            
            document.getElementById('dom-status').innerHTML = html;
        }
        
        function testSendMessage() {
            try {
                if (typeof sendMessage === 'function') {
                    console.log('sendMessage function is available');
                    alert('sendMessage function is available and callable');
                } else {
                    console.error('sendMessage function not found');
                    alert('sendMessage function not found');
                }
            } catch (error) {
                console.error('Error testing sendMessage:', error);
                alert('Error testing sendMessage: ' + error.message);
            }
        }
        
        function testSendSuggestion() {
            try {
                if (typeof sendSuggestion === 'function') {
                    console.log('sendSuggestion function is available');
                    alert('sendSuggestion function is available');
                } else {
                    console.error('sendSuggestion function not found');
                    alert('sendSuggestion function not found');
                }
            } catch (error) {
                console.error('Error testing sendSuggestion:', error);
                alert('Error testing sendSuggestion: ' + error.message);
            }
        }
        
        function testEventListeners() {
            const chatInput = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            
            let result = 'Event Listener Test Results:\n';
            result += `Chat Input: ${chatInput ? 'Found' : 'Not Found'}\n`;
            result += `Send Button: ${sendButton ? 'Found' : 'Not Found'}\n`;
            
            if (typeof setupEventListeners === 'function') {
                result += 'setupEventListeners function: Available\n';
                try {
                    setupEventListeners();
                    result += 'setupEventListeners executed successfully';
                } catch (error) {
                    result += 'setupEventListeners error: ' + error.message;
                }
            } else {
                result += 'setupEventListeners function: Not Available';
            }
            
            alert(result);
        }
        
        function refreshDebug() {
            checkScripts();
            checkFunctions();
            checkDOMElements();
        }
        
        // Initialize debug on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded');
            setTimeout(refreshDebug, 1000);
        });
    </script>
</body>
</html>
