import express from 'express';
import { 
  getArticles, 
  getArticleById, 
  getArticlesByUser,
  addArticle,
  updateArticle,
  deleteArticle
} from '../services/localAuthService.js';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// Get all articles
router.get('/', (req, res) => {
  try {
    const articles = getArticles();
    res.render('articles/index', { articles });
  } catch (error) {
    console.error('Error getting articles:', error);
    res.render('articles/index', { 
      articles: [],
      error: 'Error loading articles: ' + error.message
    });
  }
});

// Get article details
router.get('/:id', (req, res) => {
  try {
    const article = getArticleById(req.params.id);
    
    if (!article) {
      return res.status(404).render('error', { 
        error: 'Article not found',
        message: 'The article you are looking for does not exist.'
      });
    }
    
    res.render('articles/show', { article });
  } catch (error) {
    console.error('Error getting article:', error);
    res.render('error', { 
      error: 'Error loading article',
      message: error.message
    });
  }
});

// Get articles by category
router.get('/category/:category', (req, res) => {
  try {
    const { category } = req.params;
    const allArticles = getArticles();
    const articles = allArticles.filter(article => article.category.toLowerCase() === category.toLowerCase());
    
    res.render('articles/category', { 
      articles,
      category
    });
  } catch (error) {
    console.error('Error getting articles by category:', error);
    res.render('articles/category', { 
      articles: [],
      category: req.params.category,
      error: 'Error loading articles: ' + error.message
    });
  }
});

// Get new article form
router.get('/new', isAuthenticated, (req, res) => {
  res.render('articles/new');
});

// Create new article
router.post('/', isAuthenticated, (req, res) => {
  try {
    const { 
      title, 
      summary, 
      content, 
      category,
      imageUrl,
      tags
    } = req.body;
    
    const newArticle = addArticle({
      title,
      summary,
      content,
      category,
      imageUrl: imageUrl || '',
      tags: tags ? tags.split(',').map(tag => tag.trim()) : []
    });
    
    res.redirect(`/articles/${newArticle.id}`);
  } catch (error) {
    console.error('Error creating article:', error);
    res.render('articles/new', { 
      error: 'Error creating article: ' + error.message,
      formData: req.body
    });
  }
});

// Get edit article form
router.get('/:id/edit', isAuthenticated, (req, res) => {
  try {
    const article = getArticleById(req.params.id);
    
    if (!article) {
      return res.status(404).render('error', { 
        error: 'Article not found',
        message: 'The article you are trying to edit does not exist.'
      });
    }
    
    res.render('articles/edit', { article });
  } catch (error) {
    console.error('Error getting article for edit:', error);
    res.render('error', { 
      error: 'Error loading article',
      message: error.message
    });
  }
});

// Update article
router.post('/:id', isAuthenticated, (req, res) => {
  try {
    const { 
      title, 
      summary, 
      content, 
      category,
      imageUrl,
      tags
    } = req.body;
    
    const updatedArticle = updateArticle(req.params.id, {
      title,
      summary,
      content,
      category,
      imageUrl: imageUrl || '',
      tags: tags ? tags.split(',').map(tag => tag.trim()) : []
    });
    
    res.redirect(`/articles/${updatedArticle.id}`);
  } catch (error) {
    console.error('Error updating article:', error);
    res.render('articles/edit', { 
      article: { ...req.body, id: req.params.id },
      error: 'Error updating article: ' + error.message
    });
  }
});

// Delete article
router.post('/:id/delete', isAuthenticated, (req, res) => {
  try {
    deleteArticle(req.params.id);
    res.redirect('/articles');
  } catch (error) {
    console.error('Error deleting article:', error);
    res.render('error', { 
      error: 'Error deleting article',
      message: error.message
    });
  }
});

// Get my articles
router.get('/user/my-articles', isAuthenticated, (req, res) => {
  try {
    const articles = getArticlesByUser(req.user.uid);
    res.render('articles/my-articles', { articles });
  } catch (error) {
    console.error('Error getting my articles:', error);
    res.render('articles/my-articles', { 
      articles: [],
      error: 'Error loading your articles: ' + error.message
    });
  }
});

export default router;
