import express from 'express';
import multer from 'multer';
import {
  getCurrentUser,
  updateUserProfile,
  getUserData,
  changeUserPassword,
  updateProfilePicture
} from '../services/firebaseService.js';
import { isAuthenticated } from '../middleware/auth.js';

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

const router = express.Router();

// Get profile data
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Get user from Firebase Auth
    const user = getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get additional user data from Firestore
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    // Render the Facebook-style profile page
    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true
    });
  } catch (error) {
    console.error('Error getting profile:', error);
    res.redirect('/dashboard');
  }
});

// View another user's profile
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // Get the current user (if logged in)
    const currentUser = getCurrentUser();

    // Get the requested user's data (try Firestore first, then sample data)
    let userData = null;

    try {
      userData = await getUserData(userId);
      console.log('User found in Firestore:', userData?.displayName);
    } catch (error) {
      console.log('User not found in Firestore, checking sample data...');
      userData = null; // Ensure it's null for fallback
    }

    // If not found in Firestore, check sample users data
    if (!userData) {
      const sampleUsers = [
        {
          uid: 'local-user1',
          email: '<EMAIL>',
          displayName: 'John Farmer',
          firstName: 'John',
          lastName: 'Farmer',
          farmName: 'Green Valley Farm',
          location: 'Iowa, USA',
          bio: 'Sustainable farming enthusiast with 15 years of experience',
          photoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-01-01T00:00:00Z').toISOString()
        },
        {
          uid: 'local-user2',
          email: '<EMAIL>',
          displayName: 'Sarah Green',
          firstName: 'Sarah',
          lastName: 'Green',
          farmName: 'Organic Harvest Co.',
          location: 'California, USA',
          bio: 'Organic farming specialist focusing on vegetables and herbs',
          photoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-01-15T00:00:00Z').toISOString()
        },
        {
          uid: 'local-user3',
          email: '<EMAIL>',
          displayName: 'Mike Waters',
          firstName: 'Mike',
          lastName: 'Waters',
          farmName: 'Waters Grain Farm',
          location: 'Nebraska, USA',
          bio: 'Grain farmer specializing in water conservation techniques',
          photoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          createdAt: new Date('2024-02-01T00:00:00Z').toISOString()
        }
      ];

      userData = sampleUsers.find(user => user.uid === userId);
    }

    // If still not found, create a basic user object from uploads data
    if (!userData) {
      // Try to get user info from uploads
      const { getUploads } = await import('../services/firebaseService.js');
      const uploadsResult = await getUploads({ limit: 100 });
      const userUpload = uploadsResult.uploads?.find(upload => upload.userId === userId);

      if (userUpload) {
        userData = {
          uid: userId,
          displayName: userUpload.userName || 'User',
          firstName: userUpload.userName?.split(' ')[0] || 'User',
          lastName: userUpload.userName?.split(' ').slice(1).join(' ') || '',
          photoURL: userUpload.userPhotoURL,
          farmName: 'Farm',
          location: 'Unknown',
          bio: 'Sustainable farmer',
          createdAt: userUpload.createdAt
        };
      }
    }

    if (!userData) {
      throw new Error('User not found');
    }

    // Get the user's uploads
    let userUploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      userUploads = await getUploadsByUser(userId);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    // Check if this is the current user's profile
    const isOwnProfile = currentUser && currentUser.uid === userId;

    // Get current user data if logged in
    let currentUserData = null;
    if (currentUser) {
      currentUserData = await getUserData(currentUser.uid);
    }

    res.render('facebook-profile', {
      user: {
        uid: userId,
        displayName: userData.displayName || 'User',
        ...userData
      },
      userData: userData,
      uploads: userUploads || [],
      isOwnProfile
    });
  } catch (error) {
    console.error('Error viewing user profile:', error);

    // Check if the error is related to Firebase indexes
    if (error.message && error.message.includes('query requires an index')) {
      return res.render('error', {
        error: 'Database Configuration Issue',
        message: 'There is a configuration issue with the database. Please try again later or contact support.'
      });
    }

    res.render('error', {
      error: 'Error loading user profile',
      message: error.message
    });
  }
});

// Update profile
router.post('/update', isAuthenticated, async (req, res) => {
  try {
    const { displayName, farmName, location, bio } = req.body;

    // Split displayName into first and last name
    const nameParts = displayName.split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

    // Update user profile with Firebase
    await updateUserProfile({
      displayName,
      firstName,
      lastName,
      farmName,
      location,
      bio
    });

    // Get updated user data
    const user = getCurrentUser();
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      success: 'Profile updated successfully!'
    });
  } catch (error) {
    console.error('Error updating profile:', error);

    const user = getCurrentUser();
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      error: 'Error updating profile: ' + error.message
    });
  }
});

// Change password
router.post('/change-password', isAuthenticated, async (req, res) => {
  try {
    const user = getCurrentUser();
    const { currentPassword, newPassword, confirmNewPassword } = req.body;

    // Check if new passwords match
    if (newPassword !== confirmNewPassword) {
      throw new Error('New passwords do not match');
    }

    // Check password strength
    if (newPassword.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    // Change password using Firebase
    await changeUserPassword(currentPassword, newPassword);

    // Get user data
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      success: 'Password changed successfully!'
    });
  } catch (error) {
    console.error('Error changing password:', error);

    // Handle Firebase specific errors
    let errorMessage = error.message;
    if (error.code === 'auth/wrong-password') {
      errorMessage = 'Current password is incorrect';
    } else if (error.code === 'auth/weak-password') {
      errorMessage = 'New password is too weak. Please use a stronger password.';
    } else if (error.code === 'auth/requires-recent-login') {
      errorMessage = 'For security reasons, please log out and log back in before changing your password.';
    }

    const user = getCurrentUser();
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      error: 'Error changing password: ' + errorMessage
    });
  }
});

// Upload profile picture
router.post('/upload-photo', isAuthenticated, upload.single('profilePicture'), async (req, res) => {
  try {
    // Check if a file was uploaded
    if (!req.file) {
      throw new Error('No file was uploaded');
    }

    const user = getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Create a data URL from the file buffer
    const fileBuffer = req.file.buffer;
    const fileType = req.file.mimetype;
    const base64Data = fileBuffer.toString('base64');
    const dataUrl = `data:${fileType};base64,${base64Data}`;

    // Update the user's profile picture
    await updateProfilePicture(user.uid, dataUrl);

    // Get updated user data
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      success: 'Profile picture updated successfully!'
    });
  } catch (error) {
    console.error('Error uploading profile picture:', error);

    const user = getCurrentUser();
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      error: 'Error uploading profile picture: ' + error.message
    });
  }
});

// Upload cover photo
router.post('/upload-cover-photo', isAuthenticated, upload.single('coverPhoto'), async (req, res) => {
  try {
    // Check if a file was uploaded
    if (!req.file) {
      throw new Error('No file was uploaded');
    }

    const user = getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Create a data URL from the file buffer
    const fileBuffer = req.file.buffer;
    const fileType = req.file.mimetype;
    const base64Data = fileBuffer.toString('base64');
    const dataUrl = `data:${fileType};base64,${base64Data}`;

    // Update the user's cover photo
    await updateDoc(doc(db, 'users', user.uid), {
      coverPhotoURL: dataUrl
    });

    // Get updated user data
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      success: 'Cover photo updated successfully!'
    });
  } catch (error) {
    console.error('Error uploading cover photo:', error);

    const user = getCurrentUser();
    const userData = await getUserData(user.uid);

    // Get the user's uploads
    let uploads = [];
    try {
      const { getUploadsByUser } = await import('../services/firebaseService.js');
      uploads = await getUploadsByUser(user.uid);
    } catch (uploadsError) {
      console.error('Error getting user uploads:', uploadsError);
      // Continue with empty uploads array
    }

    res.render('facebook-profile', {
      user: user,
      userData: userData || {},
      uploads: uploads || [],
      isOwnProfile: true,
      error: 'Error uploading cover photo: ' + error.message
    });
  }
});

export default router;
