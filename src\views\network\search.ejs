<%- include('../partials/header') %>

<!-- Agricultural-themed Network CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="linkedin-container">
  <div class="container">
    <div class="row">
      <!-- Left Sidebar -->
      <div class="col-lg-3">
        <!-- User Profile Card -->
        <div class="linkedin-card profile-card">
          <div class="profile-background"></div>
          <% if (userData && userData.photoURL) { %>
            <img src="<%= userData.photoURL %>" class="profile-photo">
          <% } else { %>
            <div class="profile-photo d-flex align-items-center justify-content-center">
              <i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>
            </div>
          <% } %>
          <h5 class="profile-name"><%= user.displayName || 'User' %></h5>
          <p class="profile-headline">Sustainable Farmer</p>
          <a href="/network/profile/<%= user.uid %>" class="linkedin-btn linkedin-btn-outline w-100">View Profile</a>
        </div>

        <!-- Manage My Network -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">Manage my network</h5>
          </div>
          <div class="p-0">
            <a href="/network/connections" class="connection-card text-decoration-none">
              <i class="bi bi-people-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Connections</span>
              </div>
            </a>

            <a href="/network/search" class="connection-card text-decoration-none active">
              <i class="bi bi-person-plus-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-primary);">Find Farmers</span>
              </div>
            </a>

            <a href="/network" class="connection-card text-decoration-none">
              <i class="bi bi-house-door-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Home</span>
              </div>
            </a>

            <a href="/messaging" class="connection-card text-decoration-none">
              <i class="bi bi-chat-dots-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Messages</span>
              </div>
            </a>
          </div>
        </div>

        <!-- Search Filters -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">Search Filters</h5>
          </div>
          <div class="linkedin-card-body">
            <form action="/network/search" method="GET">
              <div class="mb-3">
                <label class="form-label">Location</label>
                <select class="form-select form-select-sm" name="location">
                  <option value="">All Locations</option>
                  <option value="urban">Urban</option>
                  <option value="rural">Rural</option>
                  <option value="suburban">Suburban</option>
                </select>
              </div>

              <div class="mb-3">
                <label class="form-label">Farming Type</label>
                <select class="form-select form-select-sm" name="farmingType">
                  <option value="">All Types</option>
                  <option value="organic">Organic</option>
                  <option value="conventional">Conventional</option>
                  <option value="hydroponic">Hydroponic</option>
                  <option value="permaculture">Permaculture</option>
                </select>
              </div>

              <div class="d-grid">
                <button type="submit" class="linkedin-btn linkedin-btn-primary">Apply Filters</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="col-lg-9">
        <!-- Search Bar -->
        <div class="linkedin-card mb-4">
          <div class="linkedin-card-body">
            <form action="/network/search" method="GET">
              <div class="input-group">
                <input type="text" class="form-control" name="q" placeholder="Search by name, location, or farming type" value="<%= searchTerm %>">
                <button class="linkedin-btn linkedin-btn-primary" type="submit">Search</button>
              </div>
            </form>
          </div>
        </div>

        <!-- Search Results -->
        <div class="linkedin-card mb-4">
          <div class="linkedin-card-header">
            <% if (searchTerm) { %>
              <h5 class="linkedin-card-title">Results for "<%= searchTerm %>"</h5>
            <% } else { %>
              <h5 class="linkedin-card-title">Discover Farmers</h5>
            <% } %>
          </div>
          <div class="p-0">
            <% if (results && results.length > 0) { %>
              <% results.forEach(farmer => { %>
                <div class="connection-card">
                  <% if (farmer.photoURL) { %>
                    <img src="<%= farmer.photoURL %>" class="connection-photo" alt="Profile photo">
                  <% } else { %>
                    <div class="connection-photo d-flex align-items-center justify-content-center">
                      <i class="bi bi-person-fill text-secondary"></i>
                    </div>
                  <% } %>
                  <div class="connection-info">
                    <h6 class="connection-name"><%= farmer.displayName || 'User' %></h6>
                    <p class="connection-headline"><%= farmer.headline || 'Sustainable Farmer' %></p>
                    <% if (farmer.location) { %>
                      <div class="connection-location">
                        <i class="bi bi-geo-alt-fill"></i> <%= farmer.location %>
                      </div>
                    <% } %>
                    <div class="connection-actions">
                      <a href="/network/profile/<%= farmer.uid %>" class="linkedin-btn linkedin-btn-outline">View Profile</a>
                      <% if (farmer.connectionStatus === 'none') { %>
                        <button class="linkedin-btn linkedin-btn-primary connect-btn" data-id="<%= farmer.uid %>">
                          <i class="bi bi-person-plus-fill"></i> Connect
                        </button>
                      <% } else if (farmer.connectionStatus === 'pending' && farmer.isRequester) { %>
                        <span class="linkedin-badge">Invitation Sent</span>
                      <% } else if (farmer.connectionStatus === 'pending' && !farmer.isRequester) { %>
                        <button class="linkedin-btn linkedin-btn-primary accept-request-btn" data-id="<%= farmer.connectionId %>">Accept Invitation</button>
                      <% } else if (farmer.connectionStatus === 'accepted') { %>
                        <a href="/messaging/<%= farmer.uid %>" class="linkedin-btn linkedin-btn-primary">
                          <i class="bi bi-chat-dots-fill"></i> Message
                        </a>
                      <% } %>
                    </div>
                  </div>
                </div>
              <% }); %>
            <% } else { %>
              <div class="text-center py-5">
                <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                <% if (searchTerm) { %>
                  <h5 class="mt-3">No Results Found</h5>
                  <p class="text-muted">Try a different search term or browse suggested farmers below.</p>
                <% } else { %>
                  <h5 class="mt-3">Find Farmers to Connect With</h5>
                  <p class="text-muted">Search for farmers by name, location, or farming type.</p>
                <% } %>
              </div>
            <% } %>
          </div>
        </div>

        <!-- Suggested Farmers -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">People you may know</h5>
          </div>
          <div class="linkedin-card-body p-0">
            <div class="row m-0" id="suggested-connections-container">
              <!-- Suggested connections will be loaded here -->
              <div class="text-center py-4 col-12">
                <div class="spinner-border" style="color: var(--network-primary);" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Suggested Connection Template (Hidden) -->
<template id="suggested-connection-template">
  <div class="col-md-4 mb-3">
    <div class="linkedin-card h-100">
      <div class="p-3">
        <div class="text-center mb-3">
          <img class="connection-photo" src="" alt="Profile photo" style="width: 72px; height: 72px;">
          <h6 class="connection-name mt-2 mb-1"></h6>
          <p class="connection-headline small text-muted"></p>
        </div>
        <div class="d-grid">
          <button class="linkedin-btn linkedin-btn-outline connect-btn" data-id="">
            <i class="bi bi-person-plus-fill"></i> Connect
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="/js/toast-notifications.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Load suggested connections
    loadSuggestedConnections();

    // Set up event delegation for connection actions
    document.addEventListener('click', function(event) {
      // Connect button
      if (event.target.classList.contains('connect-btn') ||
          event.target.parentElement.classList.contains('connect-btn')) {
        const button = event.target.classList.contains('connect-btn') ?
                      event.target :
                      event.target.parentElement;
        const recipientId = button.dataset.id;
        sendConnectionRequest(recipientId, button);
      }

      // Accept request button
      if (event.target.classList.contains('accept-request-btn')) {
        const connectionId = event.target.dataset.id;
        acceptConnectionRequest(connectionId, event.target);
      }
    });
  });

  /**
   * Load suggested connections (people you may know)
   */
  function loadSuggestedConnections() {
    fetch('/network/api/connections/suggestions')
      .then(response => response.json())
      .then(data => {
        const container = document.getElementById('suggested-connections-container');

        // Clear loading spinner
        container.innerHTML = '';

        if (data.success && data.suggestions && data.suggestions.length > 0) {
          // Get the template
          const template = document.getElementById('suggested-connection-template');

          // Display up to 6 suggestions
          const suggestionsToShow = data.suggestions.slice(0, 6);

          suggestionsToShow.forEach(farmer => {
            // Clone the template
            const suggestionElement = template.content.cloneNode(true);

            // Set profile photo
            const photoElement = suggestionElement.querySelector('.connection-photo');
            if (farmer.photoURL) {
              photoElement.src = farmer.photoURL;
            } else {
              // Replace img with a div containing an icon for users without photos
              const parentElement = photoElement.parentElement;
              const placeholderDiv = document.createElement('div');
              placeholderDiv.className = 'connection-photo d-flex align-items-center justify-content-center bg-light';
              placeholderDiv.style.width = '72px';
              placeholderDiv.style.height = '72px';
              placeholderDiv.style.borderRadius = '50%';
              placeholderDiv.style.margin = '0 auto';
              placeholderDiv.innerHTML = '<i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>';
              parentElement.replaceChild(placeholderDiv, photoElement);
            }

            // Set name and headline
            suggestionElement.querySelector('.connection-name').textContent = farmer.displayName || 'User';
            suggestionElement.querySelector('.connection-headline').textContent = farmer.headline || 'Sustainable Farmer';

            // Set button data attribute
            suggestionElement.querySelector('.connect-btn').dataset.id = farmer.uid;

            // Append to container
            container.appendChild(suggestionElement);
          });
        } else {
          // No suggestions or error
          container.innerHTML = '<div class="col-12 p-4 text-center text-muted">No suggestions available at this time</div>';
        }
      })
      .catch(error => {
        console.error('Error loading suggested connections:', error);
        document.getElementById('suggested-connections-container').innerHTML =
          '<div class="col-12 p-4 text-center text-danger">Failed to load suggestions. Please try again later.</div>';
      });
  }

  /**
   * Send a connection request
   */
  function sendConnectionRequest(recipientId, buttonElement) {
    // Disable button to prevent multiple clicks
    buttonElement.disabled = true;

    // Show loading state
    const originalButtonText = buttonElement.innerHTML;
    buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Sending...';

    // Get the card element
    const card = buttonElement.closest('.linkedin-card') || buttonElement.closest('.connection-card');

    // Get user name if available
    let userName = 'this user';
    if (card) {
      const nameElement = card.querySelector('.connection-name');
      if (nameElement) {
        userName = nameElement.textContent;
      }
    }

    fetch('/network/api/connections/request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ recipientId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update the button
        buttonElement.innerHTML = '<i class="bi bi-check2 me-1"></i> Invitation sent';
        buttonElement.classList.remove('linkedin-btn-primary', 'linkedin-btn-outline');
        buttonElement.classList.add('linkedin-btn-text');
        buttonElement.disabled = true;

        // Add a "pending" badge to the card if it exists
        if (card) {
          const badgeContainer = document.createElement('div');
          badgeContainer.className = 'position-absolute top-0 end-0 m-2';
          badgeContainer.innerHTML = `
            <span class="badge rounded-pill" style="background-color: var(--network-primary);">
              <i class="bi bi-clock me-1"></i> Pending
            </span>
          `;
          card.style.position = 'relative';
          card.appendChild(badgeContainer);
        }

        // Show success toast notification
        toast.success(
          `Your invitation has been sent to ${userName}. You'll be notified when they respond.`,
          'Invitation Sent',
          { duration: 5000 }
        );
      } else {
        // Reset button
        buttonElement.innerHTML = originalButtonText;
        buttonElement.disabled = false;

        // Show error toast
        toast.error(
          data.message || 'Failed to send invitation. Please try again.',
          'Error',
          { duration: 5000 }
        );
      }
    })
    .catch(error => {
      console.error('Error sending connection request:', error);

      // Reset button
      buttonElement.innerHTML = originalButtonText;
      buttonElement.disabled = false;

      // Show error toast
      toast.error(
        'An error occurred while sending the invitation. Please try again.',
        'Error',
        { duration: 5000 }
      );
    });
  }

  /**
   * Accept a connection request
   */
  function acceptConnectionRequest(connectionId, buttonElement) {
    // Disable button to prevent multiple clicks
    buttonElement.disabled = true;

    // Show loading state
    buttonElement.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Accepting...';

    // Get user name if available
    let userName = 'this user';
    const parentCard = buttonElement.closest('.connection-card');
    if (parentCard) {
      const nameElement = parentCard.querySelector('.connection-name');
      if (nameElement) {
        userName = nameElement.textContent;
      }
    }

    fetch('/network/api/connections/accept', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ connectionId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update the UI
        parentCard.innerHTML = `
          <div class="p-3 text-center text-success">
            <i class="bi bi-check-circle-fill me-2"></i>
            Connection accepted!
          </div>
        `;

        // Show success toast notification
        toast.success(
          `You are now connected with ${userName}. You can now message each other and view each other's content.`,
          'Connection Accepted',
          { duration: 5000 }
        );

        // Fade out and remove after a delay
        setTimeout(() => {
          parentCard.style.opacity = '0';
          parentCard.style.transition = 'opacity 0.5s';
          setTimeout(() => {
            parentCard.remove();

            // Reload the page after a short delay to update the connections list
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }, 500);
        }, 1000);
      } else {
        // Reset button
        buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> Accept';
        buttonElement.disabled = false;

        // Show error toast
        toast.error(
          data.message || 'Failed to accept connection. Please try again.',
          'Error',
          { duration: 5000 }
        );
      }
    })
    .catch(error => {
      console.error('Error accepting connection request:', error);

      // Reset button
      buttonElement.innerHTML = '<i class="bi bi-check-lg me-1"></i> Accept';
      buttonElement.disabled = false;

      // Show error toast
      toast.error(
        'An error occurred while accepting the connection. Please try again.',
        'Error',
        { duration: 5000 }
      );
    });
  }
</script>

<%- include('../partials/footer') %>
