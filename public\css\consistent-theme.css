/* Consistent Theme CSS - Ensuring lemon green color throughout the application */

:root {
  /* Lemon green color palette */
  --lemon-green: #4CAF50;
  --lemon-green-dark: #388E3C;
  --lemon-green-light: #8BC34A;
  --lemon-green-lighter: #C5E1A5;
  --lemon-green-lightest: #F1F8E9;
  --lemon-green-hover: rgba(76, 175, 80, 0.1);
}

/* Override any dark blue/purple backgrounds with lemon green */
.card {
  background-color: white !important;
}

/* Feature cards on home page */
.feature-card {
  background-color: white !important;
  border: 1px solid var(--lemon-green-light) !important;
  transition: all 0.3s ease;
}

.feature-card:hover {
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2) !important;
  transform: translateY(-5px);
}

.feature-icon {
  background-color: var(--lemon-green-lightest) !important;
  color: var(--lemon-green) !important;
}

/* Feature card buttons */
.feature-card .btn-outline {
  background-color: white !important;
  color: var(--lemon-green) !important;
  border-color: var(--lemon-green) !important;
}

.feature-card .btn-outline:hover {
  background-color: var(--lemon-green) !important;
  color: white !important;
}

/* Get Started buttons */
.feature-card .btn-primary {
  background-color: var(--lemon-green) !important;
  color: white !important;
  border-color: var(--lemon-green) !important;
  font-weight: bold;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.feature-card .btn-primary:hover {
  background-color: var(--lemon-green-dark) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Override any dark backgrounds in the application */
[style*="background-color: #1c1e21"],
[style*="background-color: #191919"],
[style*="background-color: #2d2d52"],
[style*="background-color: #1a1a2e"],
[style*="background-color: #242444"],
[style*="background-color: #2b2d42"],
[style*="background-color: #212529"],
[style*="background-color: #343a40"] {
  background-color: var(--lemon-green) !important;
  color: white !important;
}

/* Override specific dark blue/purple cards shown in the image */
.card {
  background-color: white !important;
}

.card .card-body {
  background-color: white !important;
}

/* Target the specific cards shown in the image */
.card-body {
  background-color: white !important;
}

/* Override any inline styles with !important */
[style*="background-color"] {
  background-color: white !important;
}

/* Exception for elements that should have the lemon green background */
.fb-header,
.fb-header-logo,
.fb-header-left,
.fb-header-right,
footer,
.btn-primary,
.btn-success,
.badge-primary,
.badge-success {
  background-color: var(--lemon-green) !important;
}

/* Override specific feature cards on home page */
.feature-card-eco,
.feature-card-community,
.feature-card-resources {
  background-color: white !important;
  color: var(--theme-text-dark) !important;
}

/* Ensure all buttons have consistent styling */
.btn-primary,
.btn-success,
button[class*="primary"] {
  background-color: var(--lemon-green) !important;
  border-color: var(--lemon-green) !important;
  color: white !important;
}

.btn-primary:hover,
.btn-success:hover,
button[class*="primary"]:hover {
  background-color: var(--lemon-green-dark) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.btn-outline-primary,
.btn-outline-success,
button[class*="outline"] {
  background-color: transparent !important;
  border-color: var(--lemon-green) !important;
  color: var(--lemon-green) !important;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
button[class*="outline"]:hover {
  background-color: var(--lemon-green) !important;
  color: white !important;
}

/* Main hero Get Started button */
.hero-get-started {
  transition: all 0.3s ease !important;
}

.hero-get-started:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2) !important;
  background-color: var(--lemon-green-dark) !important;
}

/* Ensure all icons have consistent coloring */
i[class*="bi-"] {
  color: inherit;
}

.feature-icon i {
  color: var(--lemon-green) !important;
}

/* Override any dark backgrounds in cards */
.card[style*="background-color"] {
  background-color: white !important;
  color: var(--theme-text-dark) !important;
}

/* Specific override for the feature cards shown in the image */
.card .card-body {
  background-color: white !important;
}

/* Dark background cards with icons */
.dark-bg-card {
  background-color: white !important;
  border: 1px solid var(--lemon-green-light) !important;
  color: var(--theme-text-dark) !important;
}

.dark-bg-card .card-title,
.dark-bg-card .card-text {
  color: var(--theme-text-dark) !important;
}

.dark-bg-card .btn {
  color: var(--lemon-green) !important;
  border-color: var(--lemon-green) !important;
}

.dark-bg-card .btn:hover {
  background-color: var(--lemon-green) !important;
  color: white !important;
}

/* Ensure consistent icon coloring */
.dark-bg-card i {
  color: var(--lemon-green) !important;
}

/* Specifically target the cards shown in the image */
.card[style*="background-color: #2b2d42"],
.card[style*="background-color: #1a1a2e"],
.card[style*="background-color: #242444"],
.card[style*="background-color: #2d2d52"] {
  background-color: white !important;
  color: var(--theme-text-dark) !important;
  border: 1px solid var(--lemon-green-light) !important;
}

/* Target any card with a dark background */
.card[style*="background-color"] {
  background-color: white !important;
  color: var(--theme-text-dark) !important;
}

/* Make sure all icons in cards have the lemon green color */
.card i {
  color: var(--lemon-green) !important;
}

/* Make sure all buttons in cards have consistent styling */
.card .btn {
  color: var(--lemon-green) !important;
  border-color: var(--lemon-green) !important;
  background-color: white !important;
}

.card .btn:hover {
  background-color: var(--lemon-green) !important;
  color: white !important;
}
