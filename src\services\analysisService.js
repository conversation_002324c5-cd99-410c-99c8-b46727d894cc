/**
 * Analysis Service
 * Handles historical data analysis and forecasting
 */

/**
 * Perform trend analysis on market data
 * @param {Array} data - Market data to analyze
 * @param {Object} options - Analysis options
 * @returns {Object} Trend analysis results
 */
export function analyzeTrend(data, options = {}) {
  try {
    // Extract time series data
    const timeSeriesData = extractTimeSeriesData(data, options);
    
    // Calculate linear regression
    const regression = calculateLinearRegression(timeSeriesData);
    
    // Calculate moving average
    const movingAverage = calculateMovingAverage(timeSeriesData, options.windowSize || 7);
    
    // Calculate trend strength
    const trendStrength = calculateTrendStrength(timeSeriesData, regression);
    
    return {
      timeSeriesData,
      regression,
      movingAverage,
      trendStrength
    };
  } catch (error) {
    console.error('Error analyzing trend:', error);
    throw new Error('Failed to analyze trend');
  }
}

/**
 * Perform seasonal analysis on market data
 * @param {Array} data - Market data to analyze
 * @param {Object} options - Analysis options
 * @returns {Object} Seasonal analysis results
 */
export function analyzeSeasonality(data, options = {}) {
  try {
    // Extract time series data
    const timeSeriesData = extractTimeSeriesData(data, options);
    
    // Calculate seasonal indices
    const seasonalIndices = calculateSeasonalIndices(timeSeriesData, options.seasonalPeriod || 7);
    
    // Calculate deseasonalized data
    const deseasonalizedData = calculateDeseasonalizedData(timeSeriesData, seasonalIndices);
    
    // Calculate seasonality strength
    const seasonalityStrength = calculateSeasonalityStrength(timeSeriesData, deseasonalizedData);
    
    return {
      timeSeriesData,
      seasonalIndices,
      deseasonalizedData,
      seasonalityStrength
    };
  } catch (error) {
    console.error('Error analyzing seasonality:', error);
    throw new Error('Failed to analyze seasonality');
  }
}

/**
 * Perform volatility analysis on market data
 * @param {Array} data - Market data to analyze
 * @param {Object} options - Analysis options
 * @returns {Object} Volatility analysis results
 */
export function analyzeVolatility(data, options = {}) {
  try {
    // Extract time series data
    const timeSeriesData = extractTimeSeriesData(data, options);
    
    // Calculate daily returns
    const returns = calculateReturns(timeSeriesData);
    
    // Calculate volatility
    const volatility = calculateVolatility(returns, options.windowSize || 7);
    
    // Calculate volatility bands
    const volatilityBands = calculateVolatilityBands(timeSeriesData, volatility);
    
    return {
      timeSeriesData,
      returns,
      volatility,
      volatilityBands
    };
  } catch (error) {
    console.error('Error analyzing volatility:', error);
    throw new Error('Failed to analyze volatility');
  }
}

/**
 * Generate price forecast
 * @param {Array} data - Market data to forecast
 * @param {Object} options - Forecast options
 * @returns {Object} Forecast results
 */
export function generateForecast(data, options = {}) {
  try {
    // Extract time series data
    const timeSeriesData = extractTimeSeriesData(data, options);
    
    // Perform trend analysis
    const trendAnalysis = analyzeTrend(data, options);
    
    // Perform seasonal analysis
    const seasonalAnalysis = analyzeSeasonality(data, options);
    
    // Generate forecast
    const forecastHorizon = options.forecastHorizon || 7;
    const forecast = calculateForecast(timeSeriesData, trendAnalysis, seasonalAnalysis, forecastHorizon);
    
    // Calculate confidence intervals
    const confidenceIntervals = calculateConfidenceIntervals(forecast, options.confidenceLevel || 0.95);
    
    return {
      timeSeriesData,
      forecast,
      confidenceIntervals
    };
  } catch (error) {
    console.error('Error generating forecast:', error);
    throw new Error('Failed to generate forecast');
  }
}

/**
 * Extract time series data from market data
 * @param {Array} data - Market data
 * @param {Object} options - Options
 * @returns {Array} Time series data
 */
function extractTimeSeriesData(data, options = {}) {
  // If data is already in time series format, return it
  if (Array.isArray(data) && data.length > 0 && data[0].date && data[0].price) {
    return data;
  }
  
  // Extract time series data from market data
  const timeSeriesData = [];
  
  data.forEach(item => {
    if (!item.timeSeriesData) return;
    
    // Filter by crop and location if specified
    if (options.crop && options.crop !== 'all' && item.crop !== options.crop) return;
    if (options.location && options.location !== 'all' && item.location !== options.location) return;
    
    item.timeSeriesData.forEach(point => {
      timeSeriesData.push({
        date: point.date,
        price: point.price,
        crop: item.crop,
        location: item.location
      });
    });
  });
  
  // Sort by date
  timeSeriesData.sort((a, b) => new Date(a.date) - new Date(b.date));
  
  return timeSeriesData;
}

/**
 * Calculate linear regression
 * @param {Array} data - Time series data
 * @returns {Object} Linear regression results
 */
function calculateLinearRegression(data) {
  // Convert dates to numeric values (days since first date)
  const firstDate = new Date(data[0].date);
  const x = data.map(point => (new Date(point.date) - firstDate) / (1000 * 60 * 60 * 24));
  const y = data.map(point => point.price);
  
  // Calculate means
  const n = data.length;
  const meanX = x.reduce((sum, val) => sum + val, 0) / n;
  const meanY = y.reduce((sum, val) => sum + val, 0) / n;
  
  // Calculate coefficients
  let numerator = 0;
  let denominator = 0;
  
  for (let i = 0; i < n; i++) {
    numerator += (x[i] - meanX) * (y[i] - meanY);
    denominator += (x[i] - meanX) ** 2;
  }
  
  const slope = denominator !== 0 ? numerator / denominator : 0;
  const intercept = meanY - slope * meanX;
  
  // Calculate trend line
  const trendLine = data.map((point, i) => ({
    date: point.date,
    price: intercept + slope * x[i]
  }));
  
  // Calculate R-squared
  const yPred = x.map(xi => intercept + slope * xi);
  const ssTotal = y.reduce((sum, yi) => sum + (yi - meanY) ** 2, 0);
  const ssResidual = y.reduce((sum, yi, i) => sum + (yi - yPred[i]) ** 2, 0);
  const rSquared = 1 - (ssResidual / ssTotal);
  
  return {
    slope,
    intercept,
    trendLine,
    rSquared
  };
}

/**
 * Calculate moving average
 * @param {Array} data - Time series data
 * @param {number} windowSize - Window size for moving average
 * @returns {Array} Moving average data
 */
function calculateMovingAverage(data, windowSize) {
  const movingAverage = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < windowSize - 1) {
      // Not enough data points yet
      movingAverage.push({
        date: data[i].date,
        price: null
      });
    } else {
      // Calculate average of last windowSize points
      let sum = 0;
      for (let j = 0; j < windowSize; j++) {
        sum += data[i - j].price;
      }
      
      movingAverage.push({
        date: data[i].date,
        price: sum / windowSize
      });
    }
  }
  
  return movingAverage;
}

/**
 * Calculate trend strength
 * @param {Array} data - Time series data
 * @param {Object} regression - Linear regression results
 * @returns {number} Trend strength
 */
function calculateTrendStrength(data, regression) {
  return regression.rSquared;
}

/**
 * Calculate seasonal indices
 * @param {Array} data - Time series data
 * @param {number} seasonalPeriod - Seasonal period
 * @returns {Array} Seasonal indices
 */
function calculateSeasonalIndices(data, seasonalPeriod) {
  // Group data by season
  const seasonalGroups = Array(seasonalPeriod).fill().map(() => []);
  
  data.forEach((point, i) => {
    const seasonIndex = i % seasonalPeriod;
    seasonalGroups[seasonIndex].push(point.price);
  });
  
  // Calculate average for each season
  const seasonalAverages = seasonalGroups.map(group => {
    if (group.length === 0) return 0;
    return group.reduce((sum, price) => sum + price, 0) / group.length;
  });
  
  // Calculate overall average
  const overallAverage = data.reduce((sum, point) => sum + point.price, 0) / data.length;
  
  // Calculate seasonal indices
  const seasonalIndices = seasonalAverages.map(avg => avg / overallAverage);
  
  return seasonalIndices;
}

/**
 * Calculate deseasonalized data
 * @param {Array} data - Time series data
 * @param {Array} seasonalIndices - Seasonal indices
 * @returns {Array} Deseasonalized data
 */
function calculateDeseasonalizedData(data, seasonalIndices) {
  return data.map((point, i) => ({
    date: point.date,
    price: point.price / seasonalIndices[i % seasonalIndices.length]
  }));
}

/**
 * Calculate seasonality strength
 * @param {Array} data - Time series data
 * @param {Array} deseasonalizedData - Deseasonalized data
 * @returns {number} Seasonality strength
 */
function calculateSeasonalityStrength(data, deseasonalizedData) {
  const varOriginal = calculateVariance(data.map(point => point.price));
  const varDeseasonalized = calculateVariance(deseasonalizedData.map(point => point.price));
  
  return 1 - (varDeseasonalized / varOriginal);
}

/**
 * Calculate variance
 * @param {Array} data - Data array
 * @returns {number} Variance
 */
function calculateVariance(data) {
  const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
  return data.reduce((sum, val) => sum + (val - mean) ** 2, 0) / data.length;
}

/**
 * Calculate returns
 * @param {Array} data - Time series data
 * @returns {Array} Returns
 */
function calculateReturns(data) {
  const returns = [];
  
  for (let i = 1; i < data.length; i++) {
    returns.push({
      date: data[i].date,
      return: (data[i].price - data[i - 1].price) / data[i - 1].price
    });
  }
  
  return returns;
}

/**
 * Calculate volatility
 * @param {Array} returns - Returns data
 * @param {number} windowSize - Window size for volatility calculation
 * @returns {Array} Volatility
 */
function calculateVolatility(returns, windowSize) {
  const volatility = [];
  
  for (let i = 0; i < returns.length; i++) {
    if (i < windowSize - 1) {
      // Not enough data points yet
      volatility.push({
        date: returns[i].date,
        volatility: null
      });
    } else {
      // Calculate standard deviation of returns in window
      const windowReturns = returns.slice(i - windowSize + 1, i + 1).map(r => r.return);
      const stdDev = calculateStandardDeviation(windowReturns);
      
      volatility.push({
        date: returns[i].date,
        volatility: stdDev
      });
    }
  }
  
  return volatility;
}

/**
 * Calculate standard deviation
 * @param {Array} data - Data array
 * @returns {number} Standard deviation
 */
function calculateStandardDeviation(data) {
  const variance = calculateVariance(data);
  return Math.sqrt(variance);
}

/**
 * Calculate volatility bands
 * @param {Array} data - Time series data
 * @param {Array} volatility - Volatility data
 * @returns {Object} Volatility bands
 */
function calculateVolatilityBands(data, volatility) {
  const upperBand = [];
  const lowerBand = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i >= volatility.length || volatility[i].volatility === null) {
      upperBand.push({
        date: data[i].date,
        price: null
      });
      
      lowerBand.push({
        date: data[i].date,
        price: null
      });
    } else {
      const vol = volatility[i].volatility;
      
      upperBand.push({
        date: data[i].date,
        price: data[i].price * (1 + 2 * vol)
      });
      
      lowerBand.push({
        date: data[i].date,
        price: data[i].price * (1 - 2 * vol)
      });
    }
  }
  
  return {
    upperBand,
    lowerBand
  };
}

/**
 * Calculate forecast
 * @param {Array} data - Time series data
 * @param {Object} trendAnalysis - Trend analysis results
 * @param {Object} seasonalAnalysis - Seasonal analysis results
 * @param {number} forecastHorizon - Forecast horizon
 * @returns {Array} Forecast
 */
function calculateForecast(data, trendAnalysis, seasonalAnalysis, forecastHorizon) {
  const forecast = [];
  const lastDate = new Date(data[data.length - 1].date);
  const { slope, intercept } = trendAnalysis.regression;
  const seasonalIndices = seasonalAnalysis.seasonalIndices;
  
  // Convert dates to numeric values (days since first date)
  const firstDate = new Date(data[0].date);
  const lastX = (lastDate - firstDate) / (1000 * 60 * 60 * 24);
  
  for (let i = 1; i <= forecastHorizon; i++) {
    const forecastDate = new Date(lastDate);
    forecastDate.setDate(forecastDate.getDate() + i);
    
    const x = lastX + i;
    const trendForecast = intercept + slope * x;
    const seasonalIndex = seasonalIndices[(data.length + i - 1) % seasonalIndices.length];
    const forecastPrice = trendForecast * seasonalIndex;
    
    forecast.push({
      date: forecastDate.toISOString().split('T')[0],
      price: forecastPrice
    });
  }
  
  return forecast;
}

/**
 * Calculate confidence intervals
 * @param {Array} forecast - Forecast data
 * @param {number} confidenceLevel - Confidence level
 * @returns {Object} Confidence intervals
 */
function calculateConfidenceIntervals(forecast, confidenceLevel) {
  // Z-score for the given confidence level
  const zScore = getZScore(confidenceLevel);
  
  // Assume forecast error increases with forecast horizon
  const upperBound = forecast.map((point, i) => ({
    date: point.date,
    price: point.price * (1 + zScore * 0.02 * (i + 1))
  }));
  
  const lowerBound = forecast.map((point, i) => ({
    date: point.date,
    price: point.price * (1 - zScore * 0.02 * (i + 1))
  }));
  
  return {
    upperBound,
    lowerBound
  };
}

/**
 * Get Z-score for confidence level
 * @param {number} confidenceLevel - Confidence level
 * @returns {number} Z-score
 */
function getZScore(confidenceLevel) {
  // Common z-scores
  if (confidenceLevel === 0.90) return 1.645;
  if (confidenceLevel === 0.95) return 1.96;
  if (confidenceLevel === 0.99) return 2.576;
  
  // Default to 95% confidence level
  return 1.96;
}
