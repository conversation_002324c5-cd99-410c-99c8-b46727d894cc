/**
 * Utility functions for handling Firebase and application errors
 */

/**
 * Check if an error is a Firestore permission error
 */
export const isPermissionError = (error) => {
  return error.code === 'permission-denied' || 
         error.message.includes('Missing or insufficient permissions') ||
         error.message.includes('permission-denied');
};

/**
 * Check if an error is a Firestore index error
 */
export const isIndexError = (error) => {
  return error.message && error.message.includes('requires an index');
};

/**
 * Check if an error is a Firestore unavailable error
 */
export const isUnavailableError = (error) => {
  return error.code === 'unavailable' || 
         error.message.includes('UNAVAILABLE') ||
         error.message.includes('No connection established');
};

/**
 * Handle Firestore errors and return appropriate fallback data
 */
export const handleFirestoreError = (error, fallbackData = []) => {
  console.error('Firestore error:', error);

  if (isPermissionError(error)) {
    console.warn('Permission denied - using fallback data');
    return {
      success: false,
      data: fallbackData,
      error: 'permission-denied',
      message: 'Database permissions are being configured. Please try again in a few minutes.'
    };
  }

  if (isIndexError(error)) {
    console.warn('Index not available - using fallback data');
    return {
      success: false,
      data: fallbackData,
      error: 'index-building',
      message: 'Database index is being built. Please try again in a few minutes.'
    };
  }

  if (isUnavailableError(error)) {
    console.warn('Firestore unavailable - using fallback data');
    return {
      success: false,
      data: fallbackData,
      error: 'service-unavailable',
      message: 'Service temporarily unavailable. Please try again later.'
    };
  }

  // For other errors, rethrow
  throw error;
};

/**
 * Wrapper function for Firestore operations with error handling
 */
export const withErrorHandling = async (operation, fallbackData = []) => {
  try {
    const result = await operation();
    return {
      success: true,
      data: result,
      error: null,
      message: null
    };
  } catch (error) {
    return handleFirestoreError(error, fallbackData);
  }
};

/**
 * Create a safe Firestore operation that returns empty data on permission errors
 */
export const safeFirestoreOperation = async (operation, fallbackData = []) => {
  try {
    return await operation();
  } catch (error) {
    if (isPermissionError(error) || isIndexError(error) || isUnavailableError(error)) {
      console.warn('Firestore operation failed, returning fallback data:', error.message);
      return fallbackData;
    }
    throw error;
  }
};

export default {
  isPermissionError,
  isIndexError,
  isUnavailableError,
  handleFirestoreError,
  withErrorHandling,
  safeFirestoreOperation
};
