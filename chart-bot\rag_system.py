import google.generativeai as genai
from typing import List, Dict, Any
import logging
import re
import json
from config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgriculturalRAGSystem:
    def __init__(self):
        """Initialize the RAG system with Gemini and simple text search"""
        self.setup_gemini()
        self.knowledge_base = []

    def setup_gemini(self):
        """Configure Gemini API"""
        genai.configure(api_key=GEMINI_API_KEY)
        self.gemini_model = genai.GenerativeModel(GEMINI_MODEL)
        logger.info("Gemini API configured successfully")

    def add_documents(self, documents: List[Dict[str, Any]]):
        """Add documents to the knowledge base"""
        for doc in documents:
            self.knowledge_base.append({
                'content': doc.get('content', ''),
                'metadata': doc.get('metadata', {})
            })
        logger.info(f"Added {len(documents)} documents to knowledge base")

    def retrieve_relevant_docs(self, query: str, top_k: int = 5) -> List[Dict]:
        """Retrieve relevant documents using simple text matching"""
        query_lower = query.lower()
        scored_docs = []

        for doc in self.knowledge_base:
            content = doc['content'].lower()
            metadata = doc['metadata']

            # Simple scoring based on keyword matches
            score = 0
            query_words = query_lower.split()

            for word in query_words:
                if len(word) > 2:  # Skip very short words
                    score += content.count(word) * len(word)

            if score > 0:
                scored_docs.append({
                    'content': doc['content'],
                    'score': score,
                    'category': metadata.get('category', 'general'),
                    'source': metadata.get('source', 'unknown'),
                    'title': metadata.get('title', 'Untitled')
                })

        # Sort by score and return top_k
        scored_docs.sort(key=lambda x: x['score'], reverse=True)
        return scored_docs[:top_k]
        
    def generate_response(self, query: str, context_docs: List[Dict]) -> str:
        """Generate response using Gemini with retrieved context"""
        # Prepare context from retrieved documents
        context = "\n\n".join([
            f"Source: {doc['title']} (Category: {doc['category']})\n{doc['content']}"
            for doc in context_docs
        ])
        
        # Create prompt for Gemini
        prompt = f"""You are an expert agricultural advisor helping farmers with their questions. 
Use the following agricultural knowledge to answer the farmer's question accurately and helpfully.

Context Information:
{context}

Farmer's Question: {query}

Please provide a comprehensive, practical answer based on the context above. If the context doesn't contain enough information to fully answer the question, say so and provide general guidance. Focus on actionable advice that farmers can implement.

Answer:"""

        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I'm having trouble generating a response right now. Please try again later."
            
    def chat(self, query: str) -> Dict[str, Any]:
        """Main chat function that combines retrieval and generation"""
        try:
            # Retrieve relevant documents
            relevant_docs = self.retrieve_relevant_docs(query)
            
            # Generate response
            response = self.generate_response(query, relevant_docs)
            
            return {
                'query': query,
                'response': response,
                'sources': [
                    {
                        'title': doc['title'],
                        'category': doc['category'],
                        'relevance_score': doc['score']
                    }
                    for doc in relevant_docs
                ],
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error in chat function: {e}")
            return {
                'query': query,
                'response': "I'm sorry, I encountered an error while processing your question. Please try again.",
                'sources': [],
                'status': 'error',
                'error': str(e)
            }
