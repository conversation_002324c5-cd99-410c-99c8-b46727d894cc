/**
 * Chart Bot JavaScript
 * Handles chat interface, chart generation, and data visualization
 */

let currentChart = null;
let chartBotStatus = 'unknown';

/**
 * Show notification using the toast system
 */
function showNotification(message, type = 'info', title = '') {
  if (typeof toast !== 'undefined') {
    switch (type) {
      case 'success':
        toast.success(message, title || 'Success');
        break;
      case 'error':
        toast.error(message, title || 'Error');
        break;
      case 'warning':
        toast.warning(message, title || 'Warning');
        break;
      case 'info':
      default:
        toast.info(message, title || 'Information');
        break;
    }
  } else {
    // Fallback to alert if toast is not available
    alert(`${title ? title + ': ' : ''}${message}`);
  }
}

/**
 * Initialize the chart bot interface
 */
function initializeChartBot() {
  console.log('Initializing Chart Bot...');
  
  // Check bot service health
  checkBotHealth();
  
  // Auto-scroll to bottom of chat
  scrollToBottom();
  
  // Focus on input
  const chatInput = document.getElementById('chat-input');
  if (chatInput) {
    chatInput.focus();
  }
  
  console.log('Chart Bot initialized');
}

/**
 * Check the health of the chart bot service
 */
async function checkBotHealth() {
  try {
    const response = await fetch('/chart-bot/api/health');
    const data = await response.json();
    
    const statusElement = document.getElementById('bot-status');
    if (statusElement) {
      if (data.success && data.chartBotStatus === 'healthy') {
        statusElement.innerHTML = '<span class="badge bg-success">Online</span>';
        chartBotStatus = 'healthy';
      } else {
        statusElement.innerHTML = '<span class="badge bg-warning">Limited</span>';
        chartBotStatus = 'limited';
      }
    }
  } catch (error) {
    console.error('Error checking bot health:', error);
    const statusElement = document.getElementById('bot-status');
    if (statusElement) {
      statusElement.innerHTML = '<span class="badge bg-danger">Offline</span>';
    }
    chartBotStatus = 'offline';
  }
}

/**
 * Handle chat input key press
 */
function handleChatKeyPress(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

/**
 * Send a message to the chat bot
 */
async function sendMessage() {
  const chatInput = document.getElementById('chat-input');
  const sendButton = document.getElementById('send-button');
  
  if (!chatInput || !sendButton) {
    console.error('Chat input or send button not found');
    return;
  }
  
  const message = chatInput.value.trim();
  if (!message) {
    return;
  }
  
  // Disable input and show loading
  chatInput.disabled = true;
  sendButton.disabled = true;
  sendButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
  
  try {
    // Add user message to chat
    addMessageToChat(message, 'user');
    
    // Clear input
    chatInput.value = '';
    
    // Send to API
    const response = await fetch('/chart-bot/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: message,
        maxSources: 5
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      // Add bot response to chat
      addMessageToChat(data.response, 'bot', data.sources);
      
      // Check if response contains chart data or visualization request
      if (shouldGenerateChart(message, data.response)) {
        generateChartFromResponse(data.response, message);
      }
    } else {
      addMessageToChat('Sorry, I encountered an error processing your request. Please try again.', 'bot');
    }
    
  } catch (error) {
    console.error('Error sending message:', error);
    addMessageToChat('Sorry, I\'m having trouble connecting right now. Please try again later.', 'bot');
  } finally {
    // Re-enable input
    chatInput.disabled = false;
    sendButton.disabled = false;
    sendButton.innerHTML = '<i class="bi bi-send"></i>';
    chatInput.focus();
  }
}

/**
 * Send a suggested message
 */
function sendSuggestion(suggestion) {
  const chatInput = document.getElementById('chat-input');
  if (chatInput) {
    chatInput.value = suggestion;
    sendMessage();
  }
}

/**
 * Add a message to the chat interface
 */
function addMessageToChat(message, sender, sources = []) {
  const chatMessages = document.getElementById('chat-messages');
  if (!chatMessages) {
    console.error('Chat messages container not found');
    return;
  }
  
  const messageDiv = document.createElement('div');
  messageDiv.className = `chat-message ${sender}-message`;
  
  const avatarDiv = document.createElement('div');
  avatarDiv.className = 'message-avatar';
  
  if (sender === 'user') {
    // User avatar - you can customize this based on user data
    avatarDiv.innerHTML = '<i class="bi bi-person-fill"></i>';
  } else {
    // Bot avatar
    avatarDiv.innerHTML = '<i class="bi bi-robot"></i>';
  }
  
  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';
  
  const textDiv = document.createElement('div');
  textDiv.className = 'message-text';
  textDiv.textContent = message;
  
  const timeDiv = document.createElement('div');
  timeDiv.className = 'message-time';
  timeDiv.textContent = new Date().toLocaleTimeString();
  
  contentDiv.appendChild(textDiv);
  
  // Add sources if available
  if (sources && sources.length > 0) {
    const sourcesDiv = document.createElement('div');
    sourcesDiv.className = 'message-sources';
    sourcesDiv.innerHTML = '<small class="text-muted">Sources:</small>';
    
    sources.forEach(source => {
      const badge = document.createElement('span');
      badge.className = 'badge bg-light text-dark ms-1';
      badge.textContent = source.title || 'Agricultural Knowledge';
      sourcesDiv.appendChild(badge);
    });
    
    contentDiv.appendChild(sourcesDiv);
  }
  
  contentDiv.appendChild(timeDiv);
  
  messageDiv.appendChild(avatarDiv);
  messageDiv.appendChild(contentDiv);
  
  chatMessages.appendChild(messageDiv);
  
  // Scroll to bottom
  scrollToBottom();
}

/**
 * Scroll chat to bottom
 */
function scrollToBottom() {
  const chatMessages = document.getElementById('chat-messages');
  if (chatMessages) {
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }
}

/**
 * Check if a chart should be generated based on the message and response
 */
function shouldGenerateChart(userMessage, botResponse) {
  const chartKeywords = [
    'chart', 'graph', 'plot', 'visualize', 'show data', 'trend', 'price',
    'statistics', 'analysis', 'compare', 'growth', 'yield', 'production'
  ];
  
  const combinedText = (userMessage + ' ' + botResponse).toLowerCase();
  return chartKeywords.some(keyword => combinedText.includes(keyword));
}

/**
 * Generate a chart from the bot response
 */
function generateChartFromResponse(response, userQuery) {
  // This is a simplified chart generation
  // In a real implementation, you would parse the response for data
  const sampleData = generateSampleChartData(userQuery);
  
  if (sampleData) {
    showChartModal(sampleData);
  }
}

/**
 * Generate sample chart data based on user query
 */
function generateSampleChartData(query) {
  const lowerQuery = query.toLowerCase();
  
  if (lowerQuery.includes('corn') || lowerQuery.includes('maize')) {
    return {
      type: 'line',
      title: 'Corn Price Trends',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Corn Price ($/bushel)',
          data: [4.2, 4.5, 4.8, 4.3, 4.6, 4.9],
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          tension: 0.3
        }]
      }
    };
  } else if (lowerQuery.includes('tomato')) {
    return {
      type: 'bar',
      title: 'Tomato Yield by Month',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Yield (tons/hectare)',
          data: [12, 15, 18, 22, 25, 20],
          backgroundColor: '#FF6384',
          borderColor: '#FF6384',
          borderWidth: 1
        }]
      }
    };
  } else if (lowerQuery.includes('weather') || lowerQuery.includes('temperature')) {
    return {
      type: 'line',
      title: 'Temperature Trends',
      data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
          label: 'Temperature (°C)',
          data: [22, 25, 28, 24],
          borderColor: '#FF9800',
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          tension: 0.3
        }]
      }
    };
  }
  
  // Default chart
  return {
    type: 'doughnut',
    title: 'Agricultural Data Overview',
    data: {
      labels: ['Crops', 'Livestock', 'Equipment', 'Land'],
      datasets: [{
        data: [40, 25, 20, 15],
        backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0']
      }]
    }
  };
}

/**
 * Show chart in modal
 */
function showChartModal(chartData) {
  const modal = new bootstrap.Modal(document.getElementById('chartModal'));
  const container = document.getElementById('chart-container');
  
  if (!container) {
    console.error('Chart container not found');
    return;
  }
  
  // Clear previous chart
  container.innerHTML = '<canvas id="chart-canvas"></canvas>';
  
  const canvas = document.getElementById('chart-canvas');
  if (!canvas) {
    console.error('Chart canvas not found');
    return;
  }
  
  // Create chart
  currentChart = new Chart(canvas, {
    type: chartData.type,
    data: chartData.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: chartData.title
        }
      }
    }
  });
  
  modal.show();
}

/**
 * Save the current chart
 */
async function saveCurrentChart() {
  if (!currentChart) {
    showNotification('No chart to save', 'warning');
    return;
  }
  
  const title = prompt('Enter a title for this chart:');
  if (!title) {
    return;
  }
  
  const description = prompt('Enter a description (optional):') || '';
  
  try {
    const response = await fetch('/chart-bot/api/save-chart', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: title,
        description: description,
        chartData: currentChart.data,
        chartConfig: currentChart.config,
        chartType: currentChart.config.type
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      showNotification('Chart saved successfully!', 'success');
      // Refresh saved charts list
      loadSavedCharts();
    } else {
      showNotification('Failed to save chart', 'error');
    }
  } catch (error) {
    console.error('Error saving chart:', error);
    showNotification('Error saving chart', 'error');
  }
}

/**
 * Generate a sample chart for demonstration
 */
function generateSampleChart() {
  const sampleData = {
    type: 'bar',
    title: 'Sample Agricultural Data',
    data: {
      labels: ['Wheat', 'Corn', 'Rice', 'Soybeans', 'Barley'],
      datasets: [{
        label: 'Production (Million Tons)',
        data: [765, 1134, 756, 341, 156],
        backgroundColor: [
          '#4CAF50',
          '#2196F3', 
          '#FF9800',
          '#9C27B0',
          '#F44336'
        ]
      }]
    }
  };
  
  showChartModal(sampleData);
}

/**
 * Show market data
 */
function showMarketData() {
  window.open('/market-trends', '_blank');
}

/**
 * Export chat history
 */
async function exportChatHistory() {
  try {
    const response = await fetch('/chart-bot/api/chat-history?limit=100');
    const data = await response.json();
    
    if (data.success) {
      const chatData = data.history.map(chat => ({
        timestamp: new Date(chat.timestamp?.toDate ? chat.timestamp.toDate() : chat.timestamp).toISOString(),
        userMessage: chat.userMessage,
        botResponse: chat.botResponse,
        sources: chat.sources?.length || 0
      }));
      
      const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      showNotification('Chat history exported successfully!', 'success');
    } else {
      showNotification('Failed to export chat history', 'error');
    }
  } catch (error) {
    console.error('Error exporting chat history:', error);
    showNotification('Error exporting chat history', 'error');
  }
}

/**
 * Clear chat history
 */
async function clearChatHistory() {
  if (!confirm('Are you sure you want to clear your chat history? This action cannot be undone.')) {
    return;
  }
  
  try {
    // Clear the UI
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
      // Keep only the welcome message
      const welcomeMessage = chatMessages.querySelector('.bot-message');
      chatMessages.innerHTML = '';
      if (welcomeMessage) {
        chatMessages.appendChild(welcomeMessage);
      }
    }
    
    showNotification('Chat history cleared!', 'success');
  } catch (error) {
    console.error('Error clearing chat history:', error);
    showNotification('Error clearing chat history', 'error');
  }
}

/**
 * Load saved charts
 */
async function loadSavedCharts() {
  try {
    const response = await fetch('/chart-bot/api/charts');
    const data = await response.json();
    
    if (data.success) {
      const chartsList = document.getElementById('saved-charts-list');
      if (chartsList && data.charts.length > 0) {
        // Update the charts list
        // This would require updating the DOM structure
        console.log('Loaded charts:', data.charts);
      }
    }
  } catch (error) {
    console.error('Error loading saved charts:', error);
  }
}

/**
 * Show all saved charts
 */
function showAllCharts() {
  showNotification('Showing all charts feature coming soon!', 'info');
}

/**
 * Show saved charts modal
 */
function showSavedCharts() {
  showNotification('Saved charts modal coming soon!', 'info');
}

/**
 * Load a specific chart
 */
function loadChart(chartId) {
  showNotification(`Loading chart ${chartId}...`, 'info');
}

/**
 * Delete a chart
 */
async function deleteChart(chartId) {
  if (!confirm('Are you sure you want to delete this chart?')) {
    return;
  }
  
  try {
    const response = await fetch(`/chart-bot/api/charts/${chartId}`, {
      method: 'DELETE'
    });
    
    const data = await response.json();
    
    if (data.success) {
      showNotification('Chart deleted successfully!', 'success');
      // Remove from UI
      const chartElement = document.querySelector(`[data-chart-id="${chartId}"]`);
      if (chartElement) {
        chartElement.remove();
      }
    } else {
      showNotification('Failed to delete chart', 'error');
    }
  } catch (error) {
    console.error('Error deleting chart:', error);
    showNotification('Error deleting chart', 'error');
  }
}

// Make functions available globally with error handling
try {
  window.initializeChartBot = initializeChartBot;
  window.handleChatKeyPress = handleChatKeyPress;
  window.sendMessage = sendMessage;
  window.sendSuggestion = sendSuggestion;
  window.generateSampleChart = generateSampleChart;
  window.showMarketData = showMarketData;
  window.exportChatHistory = exportChatHistory;
  window.clearChatHistory = clearChatHistory;
  window.showSavedCharts = showSavedCharts;
  window.showAllCharts = showAllCharts;
  window.loadChart = loadChart;
  window.deleteChart = deleteChart;
  window.saveCurrentChart = saveCurrentChart;
  window.showNotification = showNotification;

  console.log('Chart Bot functions loaded successfully');
} catch (error) {
  console.error('Error loading Chart Bot functions:', error);
}

// Ensure functions are available immediately
if (typeof window !== 'undefined') {
  // Double-check that all functions are properly attached
  const requiredFunctions = [
    'initializeChartBot', 'handleChatKeyPress', 'sendMessage', 'sendSuggestion',
    'generateSampleChart', 'showMarketData', 'exportChatHistory', 'clearChatHistory',
    'showSavedCharts', 'showAllCharts', 'loadChart', 'deleteChart', 'saveCurrentChart'
  ];

  requiredFunctions.forEach(funcName => {
    if (typeof window[funcName] !== 'function') {
      console.error(`Function ${funcName} is not properly attached to window`);
    }
  });
}

// Additional safety check - ensure functions are available when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('Chart Bot script DOM ready event');

  // Re-attach functions if they're missing
  const functionsToAttach = {
    sendMessage,
    sendSuggestion,
    handleChatKeyPress,
    showSavedCharts,
    initializeChartBot,
    generateSampleChart,
    showMarketData,
    exportChatHistory,
    clearChatHistory,
    showAllCharts,
    loadChart,
    deleteChart,
    saveCurrentChart,
    showNotification
  };

  Object.entries(functionsToAttach).forEach(([name, func]) => {
    if (typeof window[name] !== 'function') {
      window[name] = func;
      console.log(`Attached function: ${name}`);
    }
  });

  console.log('Chart Bot DOM ready - functions verified and attached');
});

// Immediate function attachment (backup)
(function() {
  const functionsToAttach = {
    sendMessage,
    sendSuggestion,
    handleChatKeyPress,
    showSavedCharts,
    initializeChartBot,
    generateSampleChart,
    showMarketData,
    exportChatHistory,
    clearChatHistory,
    showAllCharts,
    loadChart,
    deleteChart,
    saveCurrentChart,
    showNotification
  };

  Object.entries(functionsToAttach).forEach(([name, func]) => {
    window[name] = func;
  });

  console.log('Chart Bot functions attached immediately');
})();
