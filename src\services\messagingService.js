import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc,
  arrayUnion,
  arrayRemove,
  startAfter
} from 'firebase/firestore';
import { db, storage } from '../config/initFirebase.js';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getCurrentUser, getUserData } from './firebaseService.js';
import { safeFirestoreOperation } from '../utils/errorHandler.js';

// Collection names
const USERS_COLLECTION = 'users';
const CONVERSATIONS_COLLECTION = 'conversations';
const MESSAGES_COLLECTION = 'messages';

/**
 * Get or create a conversation between two users
 */
export const getOrCreateConversation = async (otherUserId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to start a conversation');
    }

    if (user.uid === otherUserId) {
      throw new Error('You cannot start a conversation with yourself');
    }

    // Check if a conversation already exists
    const existingConversation = await findConversationBetweenUsers(user.uid, otherUserId);

    if (existingConversation) {
      return existingConversation;
    }

    // Get other user data
    const otherUserData = await getUserData(otherUserId);

    if (!otherUserData) {
      throw new Error('User not found');
    }

    // Create a new conversation
    const conversationRef = doc(collection(db, CONVERSATIONS_COLLECTION));

    const conversationData = {
      participants: [user.uid, otherUserId],
      participantInfo: {
        [user.uid]: {
          displayName: user.displayName || 'User',
          photoURL: user.photoURL || null
        },
        [otherUserId]: {
          displayName: otherUserData.displayName || 'User',
          photoURL: otherUserData.photoURL || null
        }
      },
      lastMessage: null,
      lastMessageTimestamp: serverTimestamp(),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    await setDoc(conversationRef, conversationData);

    return {
      id: conversationRef.id,
      ...conversationData
    };
  } catch (error) {
    console.error('Error getting or creating conversation:', error);
    throw error;
  }
};

/**
 * Find a conversation between two users
 */
export const findConversationBetweenUsers = async (userId1, userId2) => {
  try {
    const q = query(
      collection(db, CONVERSATIONS_COLLECTION),
      where('participants', 'array-contains', userId1)
    );

    const snapshot = await getDocs(q);

    for (const doc of snapshot.docs) {
      const conversationData = doc.data();
      if (conversationData.participants.includes(userId2)) {
        return {
          id: doc.id,
          ...conversationData
        };
      }
    }

    return null;
  } catch (error) {
    console.error('Error finding conversation:', error);
    throw error;
  }
};

/**
 * Send a message in a conversation
 */
export const sendMessage = async (conversationId, messageText, attachmentFile = null) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to send a message');
    }

    // Get the conversation
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data();

    // Verify that the current user is a participant
    if (!conversationData.participants.includes(user.uid)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Handle attachment if provided
    let attachmentURL = null;
    if (attachmentFile) {
      // Create a storage reference
      const fileName = `${Date.now()}_${attachmentFile.name}`;
      const storageRef = ref(storage, `message_attachments/${conversationId}/${fileName}`);

      // Upload the file
      await uploadBytes(storageRef, attachmentFile);

      // Get the download URL
      attachmentURL = await getDownloadURL(storageRef);
    }

    // Create a new message
    const messageRef = doc(collection(db, MESSAGES_COLLECTION));

    const messageData = {
      conversationId,
      senderId: user.uid,
      text: messageText,
      attachmentURL,
      read: {
        [user.uid]: true
      },
      createdAt: serverTimestamp()
    };

    await setDoc(messageRef, messageData);

    // Update the conversation with the last message
    await updateDoc(conversationRef, {
      lastMessage: {
        id: messageRef.id,
        text: messageText,
        senderId: user.uid,
        hasAttachment: !!attachmentURL
      },
      lastMessageTimestamp: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      messageId: messageRef.id,
      message: 'Message sent successfully'
    };
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

/**
 * Get messages for a conversation
 */
export const getConversationMessages = async (conversationId, messageLimit = 20, lastMessageDoc = null) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to view messages');
    }

    // Get the conversation
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data();

    // Verify that the current user is a participant
    if (!conversationData.participants.includes(user.uid)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Build the query
    let messagesQuery = query(
      collection(db, MESSAGES_COLLECTION),
      where('conversationId', '==', conversationId),
      orderBy('createdAt', 'desc'),
      limit(messageLimit)
    );

    // If we have a last document, start after it
    if (lastMessageDoc) {
      messagesQuery = query(messagesQuery, startAfter(lastMessageDoc));
    }

    // Get the messages
    const messagesSnapshot = await getDocs(messagesQuery);

    const messages = messagesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Get the last visible document for pagination
    const lastVisible = messagesSnapshot.docs.length > 0
      ? messagesSnapshot.docs[messagesSnapshot.docs.length - 1]
      : null;

    return {
      messages: messages.reverse(), // Reverse to get chronological order
      lastVisible
    };
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    throw error;
  }
};

/**
 * Get all conversations for the current user
 */
export const getUserConversations = async () => {
  const user = getCurrentUser();

  if (!user) {
    return []; // Return empty array instead of throwing error
  }

  return await safeFirestoreOperation(async () => {
    try {
      // Get conversations where user is a participant
      const conversationsQuery = query(
        collection(db, CONVERSATIONS_COLLECTION),
        where('participants', 'array-contains', user.uid),
        orderBy('lastMessageTimestamp', 'desc')
      );

      const conversationsSnapshot = await getDocs(conversationsQuery);

      const conversations = conversationsSnapshot.docs.map(doc => {
        const conversationData = doc.data();

        // Find the other participant
        const otherParticipantId = conversationData.participants.find(id => id !== user.uid);
        const otherParticipantInfo = conversationData.participantInfo[otherParticipantId];

        return {
          id: doc.id,
          ...conversationData,
          otherParticipant: {
            uid: otherParticipantId,
            ...otherParticipantInfo
          }
        };
      });

      return conversations;
    } catch (indexError) {
      // Check if this is an index error
      if (indexError.message && indexError.message.includes('requires an index')) {
        console.warn('Index for conversations query is still being built. Falling back to simpler query.');

        // Fallback to a simpler query without ordering
        const simpleQuery = query(
          collection(db, CONVERSATIONS_COLLECTION),
          where('participants', 'array-contains', user.uid)
        );

        const conversationsSnapshot = await getDocs(simpleQuery);

        const conversations = conversationsSnapshot.docs.map(doc => {
          const conversationData = doc.data();

          // Find the other participant
          const otherParticipantId = conversationData.participants.find(id => id !== user.uid);
          const otherParticipantInfo = conversationData.participantInfo[otherParticipantId];

          return {
            id: doc.id,
            ...conversationData,
            otherParticipant: {
              uid: otherParticipantId,
              ...otherParticipantInfo
            }
          };
        });

        // Sort manually by lastMessageTimestamp
        conversations.sort((a, b) => {
          const timeA = a.lastMessageTimestamp ? a.lastMessageTimestamp.seconds : 0;
          const timeB = b.lastMessageTimestamp ? b.lastMessageTimestamp.seconds : 0;
          return timeB - timeA; // descending order
        });

        return conversations;
      } else {
        // If it's not an index error, rethrow
        throw indexError;
      }
    }
  }, []); // Return empty array as fallback
};

/**
 * Mark messages in a conversation as read
 */
export const markMessagesAsRead = async (conversationId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to mark messages as read');
    }

    // Get the conversation
    const conversationRef = doc(db, CONVERSATIONS_COLLECTION, conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data();

    // Verify that the current user is a participant
    if (!conversationData.participants.includes(user.uid)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Get unread messages
    const unreadMessagesQuery = query(
      collection(db, MESSAGES_COLLECTION),
      where('conversationId', '==', conversationId),
      where('senderId', '!=', user.uid)
    );

    const unreadMessagesSnapshot = await getDocs(unreadMessagesQuery);

    // Update each message
    const updatePromises = unreadMessagesSnapshot.docs.map(doc => {
      const messageData = doc.data();

      // Skip if already read
      if (messageData.read && messageData.read[user.uid]) {
        return Promise.resolve();
      }

      return updateDoc(doc.ref, {
        [`read.${user.uid}`]: true
      });
    });

    await Promise.all(updatePromises);

    return {
      success: true,
      message: 'Messages marked as read'
    };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

export default {
  getOrCreateConversation,
  findConversationBetweenUsers,
  sendMessage,
  getConversationMessages,
  getUserConversations,
  markMessagesAsRead
};
