/* Main Styles */
:root {
  /* Agricultural theme colors - MAIN APP COLOR DEFINITIONS */
  --agri-green: #4CAF50;
  --agri-dark-green: #388E3C;
  --agri-light-green: #8BC34A;
  --agri-brown: #795548;
  --agri-light-brown: #A1887F;
  --agri-beige: #F5F5DC;
  --agri-wheat: #F5DEB3;
  --agri-soil: #8B4513;
  --agri-light-gray: #f0f2f5;
  --agri-gray: #666666;
  --agri-dark-gray: #333333;

  /* Standardized theme colors for consistent application styling */
  --theme-primary: var(--agri-green);
  --theme-primary-dark: var(--agri-dark-green);
  --theme-primary-light: var(--agri-light-green);
  --theme-secondary: var(--agri-brown);
  --theme-secondary-light: var(--agri-light-brown);
  --theme-accent: var(--agri-wheat);
  --theme-background: var(--agri-light-gray);
  --theme-card-bg: white;
  --theme-text-dark: var(--agri-dark-gray);
  --theme-text-light: white;
  --theme-text-light-muted: rgba(255, 255, 255, 0.8);
  --theme-text-muted: var(--agri-gray);
  --theme-border: var(--agri-light-green);
  --theme-hover: rgba(76, 175, 80, 0.1);

  /* Ensure these variables are used throughout the application */
  --fb-light-gray: var(--agri-light-gray);
  --fb-black: var(--agri-dark-gray);
  --network-primary: var(--theme-primary);
  --network-primary-dark: var(--theme-primary-dark);
  --network-primary-light: var(--theme-primary-light);
  --network-background: var(--theme-background);
  --network-border: var(--theme-border);
  --wa-teal: var(--theme-primary);
  --weather-primary: var(--theme-primary);
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--theme-background);
  color: var(--theme-text-dark);
}

/* Animation for like button */
@keyframes likeAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.btn-animate {
  animation: likeAnimation 0.3s ease;
}

/* Comment section styling */
.comments-container {
  max-height: 300px;
  overflow-y: auto;
}

.comment {
  transition: background-color 0.2s ease;
}

.comment:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* Share modal styling */
.share-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.share-option {
  transition: transform 0.2s ease;
}

.share-option:hover {
  transform: translateY(-3px);
}

/* Category filter styling */
.category-filter-bar {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.75rem;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.category-tab {
  color: #6c757d;
  text-decoration: none;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  position: relative;
}

.category-tab:hover {
  color: var(--theme-primary);
  background-color: rgba(76, 175, 80, 0.1);
}

.category-tab.active {
  color: var(--theme-primary);
  background-color: rgba(76, 175, 80, 0.1);
  font-weight: 600;
}

.category-tab.active::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--theme-primary);
  border-radius: 3px 3px 0 0;
}

@media (max-width: 768px) {
  .category-filter-bar {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 1rem;
  }

  .category-filter-bar::-webkit-scrollbar {
    height: 4px;
  }

  .category-filter-bar::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .category-filter-bar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  .category-tabs {
    flex-wrap: nowrap;
  }
}

/* News Feed Styling */
.news-feed {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.news-feed-item {
  width: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.news-feed-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

.news-feed-item .card-img-top {
  height: 300px;
  object-fit: cover;
}

.news-feed-item .card-body {
  padding: 1.5rem;
}

.news-feed-item .card-title {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

.news-feed-item .card-text {
  font-size: 1rem;
  margin-bottom: 1rem;
}

/* Interaction icons styling */
.interaction-icons {
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 0;
  border-top: 1px solid rgba(0,0,0,0.05);
  margin-top: 0.75rem;
}

.interaction-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  width: 100%;
  padding: 0.25rem 0;
  margin-bottom: 0.25rem;
}

.interaction-icon {
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6c757d;
  padding: 0.3rem;
  border-radius: 50%;
  margin-right: 0.25rem;
}

.interaction-icon:hover {
  transform: scale(1.1);
  background-color: rgba(0,0,0,0.05);
}

.interaction-count {
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  display: block;
  text-align: left;
  padding-left: 0.3rem;
  color: #6c757d;
}

/* Like icon styling */
.like-icon.active {
  color: var(--theme-primary);
}

.like-icon:hover {
  color: var(--theme-primary);
}

/* Comment icon styling */
.comment-icon:hover {
  color: #0d6efd;
}

.comment-icon.active {
  color: #0d6efd;
}

/* Share icon styling */
.share-icon:hover {
  color: #fd7e14;
}

.share-icon.active {
  color: #fd7e14;
}

/* Disabled icon */
.interaction-icon.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.interaction-icon.disabled:hover {
  transform: none;
  background-color: transparent;
}

/* Loading indicator for infinite scroll */
.loading-indicator {
  text-align: center;
  padding: 2rem;
  display: none;
}

.loading-indicator.active {
  display: block;
}

/* Header Styles */
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: bold;
  font-size: 1.5rem;
}

/* Card Styles */
.card {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  border: none;
}

.card-header {
  border-radius: 8px 8px 0 0 !important;
  padding: 15px 20px;
}

.card-body {
  padding: 25px;
}

/* Form Styles */
.form-control {
  border-radius: 4px;
  padding: 10px 15px;
  border: 1px solid #ced4da;
}

.form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.btn-success {
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
  padding: 10px 20px;
  font-weight: 500;
}

.btn-success:hover {
  background-color: var(--theme-primary-dark) !important;
  border-color: var(--theme-primary-dark) !important;
}

.btn-outline-success {
  color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
  padding: 10px 20px;
  font-weight: 500;
}

.btn-outline-success:hover {
  background-color: var(--theme-primary) !important;
  color: var(--theme-text-light) !important;
}

/* Override Bootstrap bg-success class */
.bg-success {
  background-color: var(--theme-primary) !important;
}

/* Override Bootstrap text-success class */
.text-success {
  color: var(--theme-primary) !important;
}

/* Home Page Styles */
.display-4 {
  font-weight: 600;
  color: var(--theme-primary);
}

/* Footer Styles */
footer {
  border-top: 1px solid #e9ecef;
  color: #6c757d;
}

/* Floating Action Button */
#addContentFAB {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

#addContentFAB:hover {
  transform: scale(1.1);
}

/* Article and Crop Cards */
.card-img-top {
  height: 200px;
  object-fit: cover;
}

/* Form Improvements for Mobile */
.tox-tinymce {
  min-height: 300px;
}

/* User Profile Link Styling */
.user-profile-link {
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.user-profile-link:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

.user-profile-link:active {
  background-color: rgba(76, 175, 80, 0.2);
  transform: scale(0.98);
}

.user-profile-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  transition: box-shadow 0.3s ease;
}

.user-profile-link:hover::after {
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

/* Comment Styling */
.direct-comment-form {
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.direct-comment-form .input-group {
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.direct-comment-form .form-control {
  border-radius: 1.25rem 0 0 1.25rem;
  border: none;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.direct-comment-form .btn {
  border-radius: 0 1.25rem 1.25rem 0;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.comments-display {
  padding: 0 0.5rem;
}

.comment {
  background-color: #ffffff;
  border-radius: 0.375rem;
  padding: 0.5rem !important;
  margin-bottom: 0.5rem !important;
  border: 1px solid rgba(0,0,0,0.05) !important;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.comment:hover {
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.delete-comment-button {
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.comment:hover .delete-comment-button {
  opacity: 1;
}

/* Button animation */
.btn-animate {
  animation: btn-pulse 0.3s ease;
}

@keyframes btn-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1);
  }
}



/* Responsive Adjustments */
@media (max-width: 768px) {
  .display-4 {
    font-size: 2.5rem;
  }

  .btn-lg {
    padding: 8px 16px;
    font-size: 1rem;
  }

  .card-body {
    padding: 15px;
  }

  .card-img-top {
    height: 150px;
  }

  /* Adjust form elements for better mobile experience */
  .form-control, .form-select {
    font-size: 16px; /* Prevents iOS zoom on focus */
    padding: 12px 15px;
  }

  /* Make buttons easier to tap */
  .btn {
    padding: 12px 20px;
    font-size: 16px;
  }

  /* Improve table display on mobile */
  .table-responsive {
    font-size: 14px;
  }

  /* Adjust spacing for mobile */
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* Ensure content doesn't get hidden behind the FAB */
  footer {
    margin-bottom: 70px;
  }
}

/* Small phones */
@media (max-width: 576px) {
  .display-4 {
    font-size: 2rem;
  }

  h1 {
    font-size: 1.8rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  /* Stack grid items on very small screens */
  .col-md-3, .col-md-4, .col-md-6 {
    margin-bottom: 15px;
  }
}
