<!-- Market Trends Main Page -->
<div class="fb-content-area">
  <div class="fb-content-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="fb-section-title">
        <i class="bi bi-graph-up-arrow text-success me-2"></i>
        Market Trends
      </h2>
      <div>
        <button id="refreshData" class="btn btn-outline-primary">
          <i class="bi bi-arrow-clockwise me-2"></i>Refresh
        </button>
        <button id="exportDataBtn" class="btn btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#exportModal">
          <i class="bi bi-download me-2"></i>Export
        </button>
        <button id="preferencesBtn" class="btn btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#preferencesModal">
          <i class="bi bi-gear me-2"></i>Preferences
        </button>
      </div>
    </div>
  </div>

  <!-- Search & Filter Section -->
  <div class="fb-content-section mb-4">
    <div class="fb-card">
      <div class="fb-card-header">
        <h5 class="mb-0">Filter Market Data</h5>
      </div>
      <div class="fb-card-body">
        <form id="filterForm" class="row g-3">
          <div class="col-md-4">
            <label for="cropSelect" class="form-label">Crop</label>
            <select class="form-select" id="cropSelect">
              <option value="all" selected>All Crops</option>
              <!-- Crops will be populated dynamically -->
            </select>
          </div>
          <div class="col-md-4">
            <label for="locationSelect" class="form-label">Market Location</label>
            <select class="form-select" id="locationSelect">
              <option value="all" selected>All Locations</option>
              <!-- Locations will be populated dynamically -->
            </select>
          </div>
          <div class="col-md-4">
            <label for="timeRangeSelect" class="form-label">Time Range</label>
            <select class="form-select" id="timeRangeSelect">
              <option value="1day">Last 24 Hours</option>
              <option value="7days" selected>Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="1year">Last Year</option>
              <option value="5years">Last 5 Years</option>
            </select>
          </div>
          <div class="col-12 text-end">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-search me-2"></i>Apply Filters
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Market Price Overview -->
  <div class="fb-content-section mb-4">
    <div class="fb-card">
      <div class="fb-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Current Market Prices</h5>
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-outline-secondary btn-sm active" id="lineChartBtn">Line</button>
          <button type="button" class="btn btn-outline-secondary btn-sm" id="barChartBtn">Bar</button>
          <button type="button" class="btn btn-outline-secondary btn-sm" id="heatmapBtn">Heatmap</button>
          <button type="button" class="btn btn-outline-secondary btn-sm" id="analysisBtn">Analysis</button>
          <button type="button" class="btn btn-outline-secondary btn-sm" id="forecastBtn">Forecast</button>
        </div>
      </div>
      <div class="fb-card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover align-middle mb-0">
            <thead>
              <tr>
                <th>Crop</th>
                <th>Market Location</th>
                <th>Current Price ($/unit)</th>
                <th>Change</th>
                <th>Last Updated</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="marketPriceTableBody">
              <!-- Table rows will be populated dynamically -->
              <tr>
                <td colspan="6" class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <p class="mt-2">Loading market data...</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Price Trend Charts -->
  <div class="fb-content-section mb-4">
    <div class="row">
      <div class="col-lg-8">
        <div class="fb-card h-100">
          <div class="fb-card-header">
            <h5 class="mb-0">Price Trends</h5>
          </div>
          <div class="fb-card-body">
            <div id="priceChart" style="height: 400px;"></div>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="fb-card h-100">
          <div class="fb-card-header">
            <h5 class="mb-0">Market Comparison</h5>
          </div>
          <div class="fb-card-body">
            <div id="marketComparisonChart" style="height: 400px;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Price Alerts Section -->
  <div class="fb-content-section mb-4">
    <div class="fb-card">
      <div class="fb-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Price Alerts</h5>
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addAlertModal">
          <i class="bi bi-plus-circle me-2"></i>Add Alert
        </button>
      </div>
      <div class="fb-card-body">
        <div id="alertsContainer">
          <!-- Alerts will be populated dynamically -->
          <div class="text-center py-4" id="noAlertsMessage">
            <i class="bi bi-bell text-muted" style="font-size: 2rem;"></i>
            <p class="mt-2">No price alerts set. Add an alert to get notified when crop prices change significantly.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Alert Modal -->
<div class="modal fade" id="addAlertModal" tabindex="-1" aria-labelledby="addAlertModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content fb-modal-content">
      <div class="modal-header fb-modal-header">
        <h5 class="modal-title" id="addAlertModalLabel">Add Price Alert</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body fb-modal-body">
        <form id="alertForm">
          <div class="mb-3">
            <label for="alertCrop" class="form-label">Crop</label>
            <select class="form-select" id="alertCrop" required>
              <option value="" selected disabled>Select a crop</option>
              <!-- Options will be populated dynamically -->
            </select>
          </div>
          <div class="mb-3">
            <label for="alertCondition" class="form-label">Condition</label>
            <select class="form-select" id="alertCondition" required>
              <option value="above">Price rises above</option>
              <option value="below">Price falls below</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="alertPrice" class="form-label">Price Threshold ($)</label>
            <input type="number" class="form-control" id="alertPrice" min="0" step="0.01" required>
          </div>
        </form>
      </div>
      <div class="modal-footer fb-modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveAlertBtn">Save Alert</button>
      </div>
    </div>
  </div>
</div>

<!-- Market Details Modal -->
<div class="modal fade" id="marketDetailsModal" tabindex="-1" aria-labelledby="marketDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content fb-modal-content">
      <div class="modal-header fb-modal-header">
        <h5 class="modal-title" id="marketDetailsModalLabel">Market Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body fb-modal-body">
        <div id="detailsChart" style="height: 300px;"></div>
        <div class="row mt-4">
          <div class="col-md-6">
            <h6 class="fb-section-subtitle">Price Statistics</h6>
            <table class="table table-sm">
              <tbody id="priceStatsTable">
                <!-- Will be populated dynamically -->
              </tbody>
            </table>
          </div>
          <div class="col-md-6">
            <h6 class="fb-section-subtitle">Market Information</h6>
            <table class="table table-sm">
              <tbody id="marketInfoTable">
                <!-- Will be populated dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer fb-modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Preferences Modal -->
<div class="modal fade" id="preferencesModal" tabindex="-1" aria-labelledby="preferencesModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content fb-modal-content">
      <div class="modal-header fb-modal-header">
        <h5 class="modal-title" id="preferencesModalLabel">Market Trends Preferences</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body fb-modal-body">
        <form id="preferencesForm">
          <div class="mb-3">
            <label for="defaultCrop" class="form-label">Default Crop</label>
            <select class="form-select" id="defaultCrop">
              <option value="all" selected>All Crops</option>
              <!-- Options will be populated dynamically -->
            </select>
          </div>
          <div class="mb-3">
            <label for="defaultLocation" class="form-label">Default Location</label>
            <select class="form-select" id="defaultLocation">
              <option value="all" selected>All Locations</option>
              <!-- Options will be populated dynamically -->
            </select>
          </div>
          <div class="mb-3">
            <label for="defaultTimeRange" class="form-label">Default Time Range</label>
            <select class="form-select" id="defaultTimeRange">
              <option value="1day">Last 24 Hours</option>
              <option value="7days" selected>Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="1year">Last Year</option>
              <option value="5years">Last 5 Years</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="chartType" class="form-label">Default Chart Type</label>
            <select class="form-select" id="chartType">
              <option value="line" selected>Line Chart</option>
              <option value="bar">Bar Chart</option>
              <option value="heatmap">Heatmap</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer fb-modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="savePreferencesBtn">Save Preferences</button>
      </div>
    </div>
  </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content fb-modal-content">
      <div class="modal-header fb-modal-header">
        <h5 class="modal-title" id="exportModalLabel">Export Market Data</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body fb-modal-body">
        <form id="exportForm">
          <div class="mb-3">
            <label class="form-label">Export Format</label>
            <div class="d-flex gap-3">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="exportFormat" id="formatCSV" value="csv" checked>
                <label class="form-check-label" for="formatCSV">CSV</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="exportFormat" id="formatJSON" value="json">
                <label class="form-check-label" for="formatJSON">JSON</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="exportFormat" id="formatExcel" value="excel">
                <label class="form-check-label" for="formatExcel">Excel</label>
              </div>
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label">Export Options</label>
            <div class="form-check mb-2">
              <input class="form-check-input" type="checkbox" id="includeMetadata" checked>
              <label class="form-check-label" for="includeMetadata">Include Metadata</label>
            </div>
            <div class="form-check mb-2">
              <input class="form-check-input" type="checkbox" id="includeStatistics" checked>
              <label class="form-check-label" for="includeStatistics">Include Statistics</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="chartDataOnly">
              <label class="form-check-label" for="chartDataOnly">Chart Data Only</label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer fb-modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="downloadExportBtn">Download</button>
      </div>
    </div>
  </div>
</div>

<!-- Heatmap View -->
<div class="card shadow-sm h-100 heatmap-card d-none" id="heatmapCard">
  <div class="card-header bg-white d-flex justify-content-between align-items-center">
    <h5 class="mb-0">Price Heatmap</h5>
    <div class="btn-group" role="group">
      <button type="button" class="btn btn-outline-secondary btn-sm" id="heatmapByLocationBtn">By Location</button>
      <button type="button" class="btn btn-outline-secondary btn-sm active" id="heatmapByCropBtn">By Crop</button>
    </div>
  </div>
  <div class="card-body">
    <div id="heatmapChart" style="height: 400px;"></div>
    <div class="mt-3">
      <div class="d-flex justify-content-between align-items-center">
        <small class="text-muted">Lower Prices</small>
        <small class="text-muted">Higher Prices</small>
      </div>
      <div class="heatmap-legend">
        <div class="heatmap-legend-gradient"></div>
      </div>
    </div>
  </div>
</div>

<!-- Load the required scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="https://cdn.jsdelivr.net/npm/papaparse@5.3.0/papaparse.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.17.0/dist/xlsx.full.min.js"></script>
<script src="/js/market-trends.js"></script>
<script src="/js/market-trends-analysis.js"></script>
