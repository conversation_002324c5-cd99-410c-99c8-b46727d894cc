<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/crops">Crops</a></li>
        <li class="breadcrumb-item active" aria-current="page">Add New Crop</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card shadow-sm">
      <div class="card-header bg-success text-white">
        <h3 class="mb-0">Add New Crop for Sale</h3>
      </div>
      <div class="card-body">
        <% if (typeof error !== 'undefined') { %>
          <div class="alert alert-danger" role="alert">
            <%= error %>
          </div>
        <% } %>

        <form action="/crops" method="POST">
          <div class="mb-3">
            <label for="name" class="form-label">Crop Name</label>
            <input type="text" class="form-control" id="name" name="name" required value="<%= typeof formData !== 'undefined' && formData.name ? formData.name : '' %>">
          </div>

          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" name="description" rows="3" required><%= typeof formData !== 'undefined' && formData.description ? formData.description : '' %></textarea>
          </div>

          <div class="row">
            <div class="col-sm-6 col-md-4 mb-3">
              <label for="price" class="form-label">Price</label>
              <div class="input-group">
                <span class="input-group-text">$</span>
                <input type="number" inputmode="decimal" class="form-control" id="price" name="price" step="0.01" min="0" required value="<%= typeof formData !== 'undefined' && formData.price ? formData.price : '' %>">
              </div>
            </div>

            <div class="col-sm-6 col-md-4 mb-3">
              <label for="unit" class="form-label">Unit</label>
              <select class="form-select" id="unit" name="unit" required>
                <option value="" disabled <%= typeof formData === 'undefined' || !formData.unit ? 'selected' : '' %>>Select unit</option>
                <option value="kg" <%= typeof formData !== 'undefined' && formData.unit === 'kg' ? 'selected' : '' %>>Kilogram (kg)</option>
                <option value="lb" <%= typeof formData !== 'undefined' && formData.unit === 'lb' ? 'selected' : '' %>>Pound (lb)</option>
                <option value="bushel" <%= typeof formData !== 'undefined' && formData.unit === 'bushel' ? 'selected' : '' %>>Bushel</option>
                <option value="dozen" <%= typeof formData !== 'undefined' && formData.unit === 'dozen' ? 'selected' : '' %>>Dozen</option>
                <option value="piece" <%= typeof formData !== 'undefined' && formData.unit === 'piece' ? 'selected' : '' %>>Piece</option>
                <option value="basket" <%= typeof formData !== 'undefined' && formData.unit === 'basket' ? 'selected' : '' %>>Basket</option>
                <option value="bunch" <%= typeof formData !== 'undefined' && formData.unit === 'bunch' ? 'selected' : '' %>>Bunch</option>
              </select>
            </div>

            <div class="col-sm-12 col-md-4 mb-3">
              <label for="quantity" class="form-label">Available Quantity</label>
              <input type="number" inputmode="numeric" class="form-control" id="quantity" name="quantity" min="1" required value="<%= typeof formData !== 'undefined' && formData.quantity ? formData.quantity : '' %>">
            </div>
          </div>

          <div class="mb-3">
            <label for="category" class="form-label">Category</label>
            <select class="form-select" id="category" name="category" required>
              <option value="" disabled <%= typeof formData === 'undefined' || !formData.category ? 'selected' : '' %>>Select category</option>
              <option value="Vegetables" <%= typeof formData !== 'undefined' && formData.category === 'Vegetables' ? 'selected' : '' %>>Vegetables</option>
              <option value="Fruits" <%= typeof formData !== 'undefined' && formData.category === 'Fruits' ? 'selected' : '' %>>Fruits</option>
              <option value="Grains" <%= typeof formData !== 'undefined' && formData.category === 'Grains' ? 'selected' : '' %>>Grains</option>
              <option value="Herbs" <%= typeof formData !== 'undefined' && formData.category === 'Herbs' ? 'selected' : '' %>>Herbs</option>
              <option value="Other" <%= typeof formData !== 'undefined' && formData.category === 'Other' ? 'selected' : '' %>>Other</option>
            </select>
          </div>

          <div class="mb-3 image-upload-container">
            <label for="imageUrl" class="form-label">Crop Image</label>
            <div class="input-group mb-2">
              <input type="url" class="form-control image-url-input" id="imageUrl" name="imageUrl" placeholder="Enter image URL or use buttons below" value="<%= typeof formData !== 'undefined' && formData.imageUrl ? formData.imageUrl : '' %>">
              <button class="btn btn-outline-secondary clear-image-btn <%= typeof formData !== 'undefined' && formData.imageUrl ? '' : 'd-none' %>" type="button">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
            <div class="d-flex gap-2 mb-2">
              <button type="button" class="btn btn-outline-success take-photo-btn d-none flex-grow-1">
                <i class="bi bi-camera"></i> Take Photo
              </button>
              <button type="button" class="btn btn-outline-success select-image-btn flex-grow-1">
                <i class="bi bi-image"></i> Select Image
              </button>
            </div>
            <input type="file" class="d-none file-input" accept="image/*">
            <div class="image-preview-container <%= typeof formData !== 'undefined' && formData.imageUrl ? '' : 'd-none' %> mt-2">
              <img src="<%= typeof formData !== 'undefined' && formData.imageUrl ? formData.imageUrl : '' %>" class="image-preview img-fluid rounded" alt="Crop preview">
            </div>
            <div class="form-text">Add an image of your crop to attract more buyers.</div>
          </div>

          <div class="mb-3">
            <label for="location" class="form-label">Location</label>
            <input type="text" class="form-control" id="location" name="location" required value="<%= typeof formData !== 'undefined' && formData.location ? formData.location : '' %>">
          </div>

          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-success">Add Crop</button>
            <a href="/crops" class="btn btn-outline-secondary">Cancel</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Include image upload script -->
<script src="/js/image-upload.js"></script>
