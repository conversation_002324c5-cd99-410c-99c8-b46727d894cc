<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/">Home</a></li>
          <li class="breadcrumb-item"><a href="/courses">Courses</a></li>
          <li class="breadcrumb-item active" aria-current="page">My Courses</li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <h1 class="mb-4"><span class="time-greeting">Welcome</span> to Your Courses</h1>

      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Course Tabs -->
  <div class="row mb-4">
    <div class="col-md-12">
      <ul class="nav nav-tabs" id="coursesTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="in-progress-tab" data-bs-toggle="tab" data-bs-target="#in-progress" type="button" role="tab" aria-controls="in-progress" aria-selected="true">
            In Progress
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab" aria-controls="completed" aria-selected="false">
            Completed
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="all-courses-tab" data-bs-toggle="tab" data-bs-target="#all-courses" type="button" role="tab" aria-controls="all-courses" aria-selected="false">
            All Courses
          </button>
        </li>
      </ul>
    </div>
  </div>

  <div class="tab-content" id="coursesTabsContent">
    <!-- In Progress Courses -->
    <div class="tab-pane fade show active" id="in-progress" role="tabpanel" aria-labelledby="in-progress-tab">
      <div class="row">
        <%
          const inProgressCourses = courses.filter(course => !course.isCompleted);
          if (inProgressCourses.length > 0) {
        %>
          <% inProgressCourses.forEach(course => { %>
            <div class="col-md-6 mb-4">
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title mb-0"><%= course.title %></h5>
                    <span class="badge bg-<%= course.level === 'beginner' ? 'success' : (course.level === 'intermediate' ? 'warning' : 'danger') %>">
                      <%= course.level.charAt(0).toUpperCase() + course.level.slice(1) %>
                    </span>
                  </div>

                  <div class="progress mb-2" style="height: 8px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: <%= course.progress %>%;" aria-valuenow="<%= course.progress %>" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <p class="text-muted small mb-3"><%= course.progress %>% complete</p>

                  <p class="card-text small mb-3"><%= course.description.length > 100 ? course.description.substring(0, 100) + '...' : course.description %></p>

                  <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                      <% if (course.lastAccessedAt) { %>
                        Last accessed: <%= new Date(course.lastAccessedAt).toLocaleDateString() %>
                      <% } %>
                    </small>
                    <div>
                      <a href="/courses/<%= course.id %>" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="bi bi-info-circle"></i> Details
                      </a>
                      <a href="/courses/<%= course.id %>/module/<%= course.currentModuleId %>" class="btn btn-sm btn-success">
                        <i class="bi bi-play-fill"></i> Continue
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% }); %>
        <% } else { %>
          <div class="col-md-12">
            <div class="alert alert-info" role="alert">
              <i class="bi bi-info-circle"></i> You don't have any courses in progress.
              <a href="/courses" class="alert-link">Browse courses</a> to start learning!
            </div>
          </div>
        <% } %>
      </div>
    </div>

    <!-- Completed Courses -->
    <div class="tab-pane fade" id="completed" role="tabpanel" aria-labelledby="completed-tab">
      <div class="row">
        <%
          const completedCourses = courses.filter(course => course.isCompleted);
          if (completedCourses.length > 0) {
        %>
          <% completedCourses.forEach(course => { %>
            <div class="col-md-6 mb-4">
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title mb-0"><%= course.title %></h5>
                    <span class="badge bg-success">Completed</span>
                  </div>

                  <p class="card-text small mb-3"><%= course.description.length > 100 ? course.description.substring(0, 100) + '...' : course.description %></p>

                  <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                      <% if (course.lastAccessedAt) { %>
                        Completed: <%= new Date(course.lastAccessedAt).toLocaleDateString() %>
                      <% } %>
                    </small>
                    <div>
                      <a href="/courses/<%= course.id %>" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="bi bi-info-circle"></i> Details
                      </a>
                      <a href="/courses/<%= course.id %>/complete" class="btn btn-sm btn-success">
                        <i class="bi bi-award"></i> Certificate
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% }); %>
        <% } else { %>
          <div class="col-md-12">
            <div class="alert alert-info" role="alert">
              <i class="bi bi-info-circle"></i> You haven't completed any courses yet.
              Keep learning to earn your first completion certificate!
            </div>
          </div>
        <% } %>
      </div>
    </div>

    <!-- All Courses -->
    <div class="tab-pane fade" id="all-courses" role="tabpanel" aria-labelledby="all-courses-tab">
      <div class="row">
        <% if (courses.length > 0) { %>
          <div class="col-md-12 mb-4">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Course</th>
                    <th>Category</th>
                    <th>Progress</th>
                    <th>Last Accessed</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% courses.forEach(course => { %>
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <% if (course.isCompleted) { %>
                            <span class="badge bg-success me-2">
                              <i class="bi bi-check-circle"></i>
                            </span>
                          <% } %>
                          <div>
                            <strong><%= course.title %></strong>
                            <div class="small text-muted"><%= course.level.charAt(0).toUpperCase() + course.level.slice(1) %></div>
                          </div>
                        </div>
                      </td>
                      <td><%= course.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></td>
                      <td>
                        <div class="progress" style="height: 8px; width: 100px;">
                          <div class="progress-bar bg-success" role="progressbar" style="width: <%= course.progress %>%;" aria-valuenow="<%= course.progress %>" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small><%= course.progress %>%</small>
                      </td>
                      <td><small><%= course.lastAccessedAt ? new Date(course.lastAccessedAt).toLocaleDateString() : 'N/A' %></small></td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <a href="/courses/<%= course.id %>" class="btn btn-outline-secondary">
                            <i class="bi bi-info-circle"></i>
                          </a>
                          <% if (course.isCompleted) { %>
                            <a href="/courses/<%= course.id %>/complete" class="btn btn-success">
                              <i class="bi bi-award"></i>
                            </a>
                          <% } else { %>
                            <a href="/courses/<%= course.id %>/module/<%= course.currentModuleId %>" class="btn btn-success">
                              <i class="bi bi-play-fill"></i>
                            </a>
                          <% } %>
                        </div>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          </div>
        <% } else { %>
          <div class="col-md-12">
            <div class="alert alert-info" role="alert">
              <i class="bi bi-info-circle"></i> You haven't enrolled in any courses yet.
              <a href="/courses" class="alert-link">Browse courses</a> to start learning!
            </div>
          </div>
        <% } %>
      </div>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-md-12">
      <div class="d-flex justify-content-between align-items-center">
        <a href="/courses" class="btn btn-outline-success">
          <i class="bi bi-arrow-left"></i> Browse All Courses
        </a>
        <% if (courses.length > 0) { %>
          <div class="text-muted">
            <i class="bi bi-book"></i> <%= courses.length %> enrolled course<%= courses.length !== 1 ? 's' : '' %>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>
