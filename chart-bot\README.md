# 🌱 Agricultural Chatbot with RAG

An AI-powered agricultural advisor that uses Retrieval-Augmented Generation (RAG) to provide expert farming advice. Built with Google Gemini for natural language generation and Pinecone for vector-based knowledge retrieval.

## 🚀 Features

- **Expert Agricultural Knowledge**: Comprehensive database covering crop diseases, pest management, soil health, irrigation, and more
- **RAG Technology**: Combines retrieval of relevant information with AI generation for accurate, contextual responses
- **Real-time Chat Interface**: User-friendly web interface for asking agricultural questions
- **Source Attribution**: Shows which knowledge sources were used to generate each response
- **Categorized Knowledge**: Organized by agricultural topics for better retrieval
- **Extensible**: Easy to add new agricultural documents and knowledge

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │   FastAPI       │    │   Pinecone      │
│   Frontend      │◄──►│   Backend       │◄──►│   Vector DB     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Google Gemini  │
                       │   AI Model      │
                       └─────────────────┘
```

## 📋 Prerequisites

- Python 3.8+
- Google Gemini API key
- Pinecone API key
- Internet connection for API access

## 🛠️ Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure API keys**:
   The API keys are already configured in `config.py`:
   - Gemini API Key: `AIzaSyAkmatFMGGakhDn8wrpcscGMFdXzqsQEy8`
   - Pinecone API Key: `pcsk_35nGRN_J1YMt94G5k6nbtQyo6Cuvip2xdTboPAQZVtkCeVmR11Q6DAuUAkrAUocVaATWec`

## 🚀 Quick Start

### Method 1: Using Streamlit (Recommended for testing)

1. **Start the FastAPI backend**:
   ```bash
   python app.py
   ```
   The API will be available at `http://localhost:8000`

2. **In a new terminal, start the Streamlit frontend**:
   ```bash
   streamlit run streamlit_app.py
   ```
   The web interface will open at `http://localhost:8501`

### Method 2: Using API directly

1. **Start the FastAPI server**:
   ```bash
   python app.py
   ```

2. **Test with curl**:
   ```bash
   curl -X POST "http://localhost:8000/chat" \
        -H "Content-Type: application/json" \
        -d '{"query": "How do I treat tomato blight disease?"}'
   ```

## 📚 Knowledge Base

The system comes pre-loaded with agricultural knowledge covering:

- **Crop Diseases**: Disease identification, prevention, and treatment
- **Pest Management**: Integrated pest management strategies
- **Soil Management**: pH testing, nutrient management, soil health
- **Fertilizers**: Organic and conventional fertilizer guidance
- **Irrigation**: Water-efficient irrigation techniques
- **Crop Rotation**: Disease prevention through rotation planning

## 🔧 API Endpoints

### Chat Endpoint
```
POST /chat
{
  "query": "Your agricultural question",
  "max_sources": 5
}
```

### Health Check
```
GET /health
```

### Sample Questions
```
GET /sample-questions
```

### Add Knowledge
```
POST /add-knowledge
[
  {
    "content": "Agricultural content...",
    "metadata": {
      "category": "crop_diseases",
      "title": "Document Title",
      "source": "Source Name"
    }
  }
]
```

## 💡 Example Questions

Try asking these questions:

- "How do I treat tomato blight disease?"
- "What are the best methods for controlling aphids?"
- "How do I test and adjust soil pH?"
- "What organic fertilizers should I use for vegetables?"
- "How can I set up efficient irrigation for my crops?"
- "What is crop rotation and why is it important?"

## 🔄 Adding New Knowledge

You can extend the knowledge base by:

1. **Adding text files** to an `agricultural_data/` directory
2. **Using the API** to add documents programmatically
3. **Modifying** `knowledge_loader.py` to load from other sources

## 🏃‍♂️ Development

### Project Structure
```
chart-bot/
├── app.py                 # FastAPI main application
├── rag_system.py         # RAG implementation
├── knowledge_loader.py   # Document processing
├── config.py            # Configuration and API keys
├── streamlit_app.py     # Web interface
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

### Running Tests
```bash
# Test the API health
curl http://localhost:8000/health

# Test a simple query
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "What is crop rotation?"}'
```

## 🔍 Troubleshooting

### Common Issues

1. **API not responding**:
   - Check if `python app.py` is running
   - Verify port 8000 is not in use
   - Check API key configuration

2. **Pinecone connection errors**:
   - Verify Pinecone API key is correct
   - Check internet connection
   - Ensure Pinecone index is created

3. **Gemini API errors**:
   - Verify Gemini API key is valid
   - Check API quota limits
   - Ensure internet connection

### Logs
Check the console output for detailed error messages and system status.

## 🤝 Contributing

To add new agricultural knowledge:

1. Create documents with proper metadata
2. Use the `/add-knowledge` endpoint
3. Test with relevant queries

## 📄 License

This project is for educational and agricultural support purposes.

## 🙏 Acknowledgments

- Google Gemini for natural language generation
- Pinecone for vector database services
- Agricultural extension services for knowledge content
- Open source community for tools and libraries
