import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import {
  getAllCourses,
  getCoursesByCategory,
  getCourseById,
  getCourseModules,
  getModuleById,
  enrollInCourse,
  getCourseProgress,
  completeModule,
  getUserCourses,
  saveUserNotes
} from '../services/courseService.js';
import {
  hasAccessToPremiumCourse,
  getEnrollmentByCourse
} from '../services/paymentService.js';

const router = express.Router();

// Get all courses
router.get('/', async (req, res) => {
  try {
    console.log('Fetching courses for display...');
    const courses = await getAllCourses();
    console.log(`Found ${courses.length} courses:`, courses.map(c => c.title));
    res.render('courses/index', { courses });
  } catch (error) {
    console.error('Error getting courses:', error);
    res.render('courses/index', {
      courses: [],
      error: 'Error loading courses: ' + error.message
    });
  }
});

// Debug route to show all courses as JSON
router.get('/debug', async (req, res) => {
  try {
    const courses = await getAllCourses();
    res.json({
      count: courses.length,
      courses: courses
    });
  } catch (error) {
    console.error('Error in debug route:', error);
    res.status(500).json({
      error: error.message,
      stack: error.stack
    });
  }
});

// Get courses by category
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const courses = await getCoursesByCategory(category);

    res.render('courses/category', {
      courses,
      category
    });
  } catch (error) {
    console.error(`Error getting courses for category ${req.params.category}:`, error);
    res.render('courses/category', {
      courses: [],
      category: req.params.category,
      error: 'Error loading courses: ' + error.message
    });
  }
});

// Get user's enrolled courses
router.get('/my-courses', isAuthenticated, async (req, res) => {
  try {
    const courses = await getUserCourses();

    res.render('courses/my-courses', {
      courses,
      user: req.user
    });
  } catch (error) {
    console.error('Error getting user courses:', error);
    res.render('courses/my-courses', {
      courses: [],
      user: req.user,
      error: 'Error loading your courses: ' + error.message
    });
  }
});

// Get course details
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const course = await getCourseById(id);

    if (!course) {
      return res.status(404).render('error', {
        error: 'Course not found',
        message: 'The course you are looking for does not exist.'
      });
    }

    const modules = await getCourseModules(id);

    // Get user progress and premium access if authenticated
    let progress = null;
    let enrollment = null;
    let hasAccess = true; // Default to true for free courses

    if (req.user) {
      progress = await getCourseProgress(id);

      // Check premium course access
      if (course.isPremium) {
        hasAccess = await hasAccessToPremiumCourse(id);
        enrollment = await getEnrollmentByCourse(id);
      }
    } else if (course.isPremium) {
      // Not logged in and course is premium
      hasAccess = false;
    }

    res.render('courses/show', {
      course,
      modules: hasAccess ? modules : [], // Hide modules if no access
      progress,
      enrollment,
      hasAccess,
      user: req.user
    });
  } catch (error) {
    console.error(`Error getting course ${req.params.id}:`, error);
    res.render('error', {
      error: 'Error loading course',
      message: error.message
    });
  }
});

// Enroll in a course
router.post('/:id/enroll', isAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;

    // Get course details
    const course = await getCourseById(id);
    if (!course) {
      return res.status(404).render('error', {
        error: 'Course not found',
        message: 'The course you are trying to enroll in does not exist.'
      });
    }

    // Check if course is premium
    if (course.isPremium) {
      // Premium courses require payment and admin approval
      return res.redirect(`/courses/${id}/purchase`);
    }

    // Free course - enroll directly
    const progress = await enrollInCourse(id);
    res.redirect(`/courses/${id}/module/${progress.currentModuleId}`);
  } catch (error) {
    console.error(`Error enrolling in course ${req.params.id}:`, error);

    const course = await getCourseById(req.params.id);
    const modules = await getCourseModules(req.params.id);
    let hasAccess = true;
    let enrollment = null;

    if (course && course.isPremium) {
      hasAccess = await hasAccessToPremiumCourse(req.params.id);
      enrollment = await getEnrollmentByCourse(req.params.id);
    }

    res.render('courses/show', {
      course,
      modules: hasAccess ? modules : [],
      progress: null,
      enrollment,
      hasAccess,
      user: req.user,
      error: 'Error enrolling in course: ' + error.message
    });
  }
});

// Premium course purchase page
router.get('/:id/purchase', isAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;

    const course = await getCourseById(id);
    if (!course) {
      return res.status(404).render('error', {
        error: 'Course not found',
        message: 'The course you are looking for does not exist.'
      });
    }

    if (!course.isPremium) {
      return res.redirect(`/courses/${id}`);
    }

    // Check if user already has enrollment
    const enrollment = await getEnrollmentByCourse(id);

    res.render('courses/purchase', {
      course,
      enrollment,
      user: req.user
    });
  } catch (error) {
    console.error(`Error loading purchase page for course ${req.params.id}:`, error);
    res.render('error', {
      error: 'Error loading purchase page',
      message: error.message
    });
  }
});

// Get module content
router.get('/:courseId/module/:moduleId', isAuthenticated, async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;

    // Get course, module, and progress
    const course = await getCourseById(courseId);
    const module = await getModuleById(courseId, moduleId);
    const modules = await getCourseModules(courseId);
    const progress = await getCourseProgress(courseId);

    if (!course || !module) {
      return res.status(404).render('error', {
        error: 'Course or module not found',
        message: 'The course or module you are looking for does not exist.'
      });
    }

    // Check premium course access
    if (course.isPremium) {
      const hasAccess = await hasAccessToPremiumCourse(courseId);
      if (!hasAccess) {
        return res.redirect(`/courses/${courseId}/purchase`);
      }
    }

    // If user is not enrolled, redirect to course page
    if (!progress) {
      return res.redirect(`/courses/${courseId}`);
    }

    // Find previous and next modules
    const currentIndex = modules.findIndex(m => m.id === moduleId);
    const prevModule = currentIndex > 0 ? modules[currentIndex - 1] : null;
    const nextModule = currentIndex < modules.length - 1 ? modules[currentIndex + 1] : null;

    res.render('courses/module', {
      course,
      module,
      modules,
      progress,
      prevModule,
      nextModule,
      user: req.user
    });
  } catch (error) {
    console.error(`Error getting module ${req.params.moduleId}:`, error);
    res.render('error', {
      error: 'Error loading module',
      message: error.message
    });
  }
});

// Mark module as completed
router.post('/:courseId/module/:moduleId/complete', isAuthenticated, async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;
    const progress = await completeModule(courseId, moduleId);

    // Redirect to next module or course completion page
    if (progress.isCompleted) {
      res.redirect(`/courses/${courseId}/complete`);
    } else if (progress.currentModuleId && progress.currentModuleId !== moduleId) {
      res.redirect(`/courses/${courseId}/module/${progress.currentModuleId}`);
    } else {
      res.redirect(`/courses/${courseId}`);
    }
  } catch (error) {
    console.error(`Error completing module ${req.params.moduleId}:`, error);
    res.redirect(`/courses/${req.params.courseId}/module/${req.params.moduleId}`);
  }
});

// Course completion page
router.get('/:id/complete', isAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;
    const course = await getCourseById(id);
    const progress = await getCourseProgress(id);

    if (!course) {
      return res.status(404).render('error', {
        error: 'Course not found',
        message: 'The course you are looking for does not exist.'
      });
    }

    // If user hasn't completed the course, redirect to course page
    if (!progress || !progress.isCompleted) {
      return res.redirect(`/courses/${id}`);
    }

    res.render('courses/complete', {
      course,
      progress,
      user: req.user
    });
  } catch (error) {
    console.error(`Error loading completion page for course ${req.params.id}:`, error);
    res.render('error', {
      error: 'Error loading completion page',
      message: error.message
    });
  }
});

// Save user notes
router.post('/:id/notes', isAuthenticated, async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    await saveUserNotes(id, notes);

    res.redirect(`/courses/${id}`);
  } catch (error) {
    console.error(`Error saving notes for course ${req.params.id}:`, error);
    res.redirect(`/courses/${req.params.id}`);
  }
});

export default router;
