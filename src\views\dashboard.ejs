<!-- Facebook-style Dashboard with Agricultural Theme -->
<%
// Set the current path for active navigation highlighting
const currentPath = originalUrl || '';
%>

<!-- Main Content Area with Facebook-style Layout -->
<div class="fb-container">
  <div class="fb-main-content">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
      <a href="/dashboard" class="fb-menu-item <%= currentPath === '/dashboard' ? 'active' : '' %>">
        <i class="bi bi-house-door-fill"></i>
        <span>Home</span>
      </a>

      <a href="/profile" class="fb-menu-item <%= currentPath.includes('/profile') ? 'active' : '' %>">
        <i class="bi bi-person-fill"></i>
        <span>Profile</span>
      </a>

      <a href="/network" class="fb-menu-item <%= currentPath.includes('/network') ? 'active' : '' %>">
        <i class="bi bi-people-fill"></i>
        <span>Farmer Network</span>
      </a>

      <a href="/messaging" class="fb-menu-item <%= currentPath.includes('/messaging') ? 'active' : '' %>">
        <i class="bi bi-chat-dots-fill"></i>
        <span>Messages</span>
      </a>

      <div class="fb-menu-divider"></div>

      <a href="/courses" class="fb-menu-item <%= currentPath === '/courses' ? 'active' : '' %>">
        <i class="bi bi-book-fill"></i>
        <span>Courses</span>
      </a>

      <a href="/courses/my-courses" class="fb-menu-item <%= currentPath.includes('/courses/my-courses') ? 'active' : '' %>">
        <i class="bi bi-journal-check"></i>
        <span>My Courses</span>
      </a>

      <a href="/resources" class="fb-menu-item <%= currentPath.includes('/resources') ? 'active' : '' %>">
        <i class="bi bi-file-earmark-text"></i>
        <span>Resources</span>
      </a>

      <div class="fb-menu-divider"></div>

      <a href="/uploads/new" class="fb-menu-item <%= currentPath.includes('/uploads/new') ? 'active' : '' %>">
        <i class="bi bi-cloud-upload"></i>
        <span>Upload Content</span>
      </a>

      <a href="/uploads/my-uploads" class="fb-menu-item <%= currentPath.includes('/uploads/my-uploads') ? 'active' : '' %>">
        <i class="bi bi-collection"></i>
        <span>My Uploads</span>
      </a>

      <a href="/weather" class="fb-menu-item <%= currentPath.includes('/weather') ? 'active' : '' %>">
        <i class="bi bi-cloud-sun"></i>
        <span>Weather</span>
      </a>

      <a href="/transport" class="fb-menu-item <%= currentPath.includes('/transport') ? 'active' : '' %>">
        <i class="bi bi-truck"></i>
        <span>Transport</span>
      </a>

      <a href="/chart-bot" class="fb-menu-item <%= currentPath.includes('/chart-bot') ? 'active' : '' %>">
        <i class="bi bi-robot"></i>
        <span>AI Chart Bot</span>
      </a>

      <div class="fb-menu-divider"></div>

      <a href="/auth/logout" class="fb-menu-item">
        <i class="bi bi-box-arrow-right"></i>
        <span>Logout</span>
      </a>
    </div>

    <!-- Main Feed -->
    <div class="fb-feed">
      <!-- Stories/Welcome Banner -->
      <div class="fb-stories-container">
        <div class="fb-story fb-welcome-story animate-on-scroll" style="background: linear-gradient(135deg, var(--primary-500, #4CAF50), var(--primary-600, #45a049)); position: relative; overflow: hidden;">
          <!-- Animated Background Pattern -->
          <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>'); animation: float 6s ease-in-out infinite;"></div>

          <div class="welcome-text position-relative">
            <div class="mb-3">
              <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);">
                <i class="bi bi-sun" style="font-size: 1.5rem; color: white;"></i>
              </div>
            </div>
            <h2 id="time-greeting" style="font-size: 1.5rem; margin-bottom: 0.5rem;">Good day</h2>
            <h3 style="font-size: 1.25rem; margin-bottom: 0.5rem;"><%= user && user.displayName ? user.displayName : 'Farmer' %>!</h3>
            <p style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 0;">Welcome to your sustainable farming community</p>
          </div>
        </div>

        <!-- Story cards with Facebook-like design -->
        <div class="fb-story">
          <div class="fb-story-content">
            <div class="fb-story-icon">
              <i class="bi bi-newspaper"></i>
            </div>
            <span>Farming News</span>
          </div>
        </div>

        <div class="fb-story">
          <div class="fb-story-content">
            <div class="fb-story-icon">
              <i class="bi bi-calendar-event"></i>
            </div>
            <span>Events</span>
          </div>
        </div>

        <div class="fb-story">
          <div class="fb-story-content">
            <div class="fb-story-icon">
              <i class="bi bi-graph-up"></i>
            </div>
            <span>Market Trends</span>
          </div>
        </div>

        <div class="fb-story">
          <div class="fb-story-content">
            <div class="fb-story-icon">
              <i class="bi bi-people"></i>
            </div>
            <span>Community</span>
          </div>
        </div>
      </div>

      <!-- Create Post Box -->
      <div class="fb-create-post">
        <div class="fb-create-post-header">
          <% if (user && user.photoURL) { %>
            <img src="<%= user.photoURL %>" alt="<%= user.displayName %>" class="fb-user-avatar">
          <% } else { %>
            <div class="fb-header-icon">
              <i class="bi bi-person-fill"></i>
            </div>
          <% } %>
          <div class="fb-create-post-input">
            What's on your mind, <%= user && user.displayName ? user.displayName.split(' ')[0] : 'Farmer' %>?
          </div>
        </div>
        <div class="fb-create-post-actions">
          <div class="fb-post-action photo">
            <i class="bi bi-image"></i>
            <span>Photo</span>
          </div>
          <div class="fb-post-action video">
            <i class="bi bi-camera-video"></i>
            <span>Video</span>
          </div>
          <div class="fb-post-action event">
            <i class="bi bi-calendar-event"></i>
            <span>Event</span>
          </div>
        </div>
      </div>

      <!-- Content Filter -->
      <div class="fb-content-filter">
        <div class="filter-tabs">
          <div class="filter-tab active" data-filter="all">
            <i class="bi bi-grid-3x3-gap-fill"></i>
            <span>All</span>
          </div>
          <div class="filter-tab" data-filter="crops">
            <i class="bi bi-flower1"></i>
            <span>Crops</span>
          </div>
          <div class="filter-tab" data-filter="farming-techniques">
            <i class="bi bi-tools"></i>
            <span>Techniques</span>
          </div>
          <div class="filter-tab" data-filter="others">
            <i class="bi bi-gear"></i>
            <span>Others</span>
          </div>
          <div class="filter-tab" data-filter="connections">
            <i class="bi bi-people-fill"></i>
            <span>Connect</span>
          </div>
        </div>
      </div>

      <!-- Community Content Feed -->
      <div class="community-feed">
        <!-- Content Container for Dynamic Loading -->
        <div class="news-feed" id="content-container">
          <!-- Server-side fallback content -->
          <% if (typeof uploads !== 'undefined' && uploads && uploads.length > 0) { %>
            <div id="server-side-content">
              <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle me-2"></i>
                Showing server-side content. Dynamic loading will take over once JavaScript loads.
              </div>

              <% uploads.forEach(upload => { %>
                <%- include('./partials/facebook-post', { post: upload, user: user }) %>
              <% }); %>
            </div>
          <% } else { %>
            <div id="server-side-content">
              <div class="text-center py-5">
                <div class="mb-4">
                  <i class="bi bi-cloud-upload text-muted" style="font-size: 5rem;"></i>
                </div>
                <h3>No Content Available</h3>
                <p class="text-muted mb-4">Be the first to share content with the community!</p>
                <a href="/uploads/new" class="btn btn-lg btn-success">
                  <i class="bi bi-cloud-upload me-2"></i> Upload Content
                </a>
              </div>
            </div>
          <% } %>
        </div>

        <!-- Loading Indicator -->
        <div id="loading-indicator" class="loading-indicator text-center py-4" style="display: none;">
          <div class="spinner-border text-success mb-3" role="status">
            <span class="visually-hidden">Loading more content...</span>
          </div>
          <p class="text-muted">Loading more posts...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include the Facebook-style footer -->
<%- include('./partials/facebook-footer') %>

<!-- Load notification system first -->
<script src="/js/toast-notifications.js"></script>
<!-- Load Facebook interactions -->
<script src="/js/facebook-interactions.js"></script>
<!-- Load content manager -->
<script src="/js/content-manager.js"></script>

<!-- Immediate function test -->
<script>
  // Test functions immediately after loading
  setTimeout(() => {
    console.log('=== IMMEDIATE FUNCTION TEST ===');
    console.log('handleLike:', typeof window.handleLike);
    console.log('handleCommentKeydown:', typeof window.handleCommentKeydown);
    console.log('autoResizeTextarea:', typeof window.autoResizeTextarea);

    // If functions are not available, try to reload the script
    if (typeof window.handleLike !== 'function') {
      console.warn('Functions not loaded, attempting to reload...');
      const script = document.createElement('script');
      script.src = '/js/facebook-interactions.js';
      document.head.appendChild(script);
    }
  }, 100);
</script>

<script>
  // Global variables for user info
  const currentUserName = '<%= typeof user !== "undefined" && user ? user.displayName || "Anonymous" : "Anonymous" %>';
  const currentUserPhoto = '<%= typeof user !== "undefined" && user && user.photoURL ? user.photoURL : "" %>';
  const currentUserId = '<%= typeof user !== "undefined" && user ? user.uid : "" %>';

  // Make user ID available globally for Content Manager
  window.currentUserId = currentUserId;

  // Define temporary placeholder functions to prevent errors until real functions load
  if (!window.handleLike) {
    window.handleLike = function(postId) {
      console.log('Temporary handleLike placeholder called for:', postId);
      // Wait for real function to load
      setTimeout(() => {
        if (window.handleLike && window.handleLike !== arguments.callee) {
          window.handleLike(postId);
        }
      }, 100);
    };
  }

  if (!window.handleCommentKeydown) {
    window.handleCommentKeydown = function(event, postId) {
      console.log('Temporary handleCommentKeydown placeholder called for:', postId);
      setTimeout(() => {
        if (window.handleCommentKeydown && window.handleCommentKeydown !== arguments.callee) {
          window.handleCommentKeydown(event, postId);
        }
      }, 100);
    };
  }

  if (!window.autoResizeTextarea) {
    window.autoResizeTextarea = function(textarea) {
      console.log('Temporary autoResizeTextarea placeholder called');
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
    };
  }

  if (!window.submitComment) {
    window.submitComment = function(postId) {
      console.log('Temporary submitComment placeholder called for:', postId);
      setTimeout(() => {
        if (window.submitComment && window.submitComment !== arguments.callee) {
          window.submitComment(postId);
        }
      }, 100);
    };
  }

  if (!window.focusCommentInput) {
    window.focusCommentInput = function(postId) {
      console.log('Temporary focusCommentInput placeholder called for:', postId);
      const commentInput = document.getElementById(`comment-text-${postId}`);
      if (commentInput) {
        commentInput.focus();
      }
    };
  }

  document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard DOMContentLoaded fired');

    // Check if elements exist
    const container = document.getElementById('content-container');
    const loadingIndicator = document.getElementById('loading-indicator');

    console.log('Container found:', !!container);
    console.log('Loading indicator found:', !!loadingIndicator);
    console.log('ContentManager class available:', typeof ContentManager);

    if (!container) {
      console.error('Content container not found!');
      return;
    }

    if (typeof ContentManager === 'undefined') {
      console.error('ContentManager class not loaded!');
      return;
    }

    // Initialize Content Manager
    try {
      window.contentManager = new ContentManager({
        container: container,
        loadingIndicator: loadingIndicator,
        itemsPerPage: 10,
        apiEndpoint: '/api/content'
      });
      console.log('ContentManager initialized successfully');

      // Hide server-side content once dynamic loading takes over
      setTimeout(() => {
        const serverSideContent = document.getElementById('server-side-content');
        if (serverSideContent) {
          serverSideContent.style.display = 'none';
          console.log('Server-side content hidden, dynamic content active');
        }
      }, 2000); // Give Content Manager time to load

    } catch (error) {
      console.error('Error initializing ContentManager:', error);
      // Keep server-side content visible if Content Manager fails
      const serverSideContent = document.getElementById('server-side-content');
      if (serverSideContent) {
        const alertDiv = serverSideContent.querySelector('.alert');
        if (alertDiv) {
          alertDiv.className = 'alert alert-warning mb-4';
          alertDiv.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>Dynamic loading failed. Showing server-side content.';
        }
      }
    }

    // Initialize Facebook-style dashboard
    initFacebookDashboard();

    // Add agricultural theme elements
    addAgriculturalElements();

    // Initialize scrolling effects
    initScrollEffects();

    // Setup filter functionality
    setupFilterTabs();

    // Test function availability
    console.log('=== FUNCTION AVAILABILITY TEST ===');
    console.log('handleLike function available:', typeof window.handleLike);
    console.log('submitComment function available:', typeof window.submitComment);
    console.log('focusCommentInput function available:', typeof window.focusCommentInput);
    console.log('handleCommentKeydown function available:', typeof window.handleCommentKeydown);
    console.log('autoResizeTextarea function available:', typeof window.autoResizeTextarea);

    // Test if functions are actually callable
    if (typeof window.handleLike === 'function') {
      console.log('✅ handleLike is callable');
    } else {
      console.error('❌ handleLike is not callable');
    }

    if (typeof window.handleCommentKeydown === 'function') {
      console.log('✅ handleCommentKeydown is callable');
    } else {
      console.error('❌ handleCommentKeydown is not callable');
    }

    if (typeof window.autoResizeTextarea === 'function') {
      console.log('✅ autoResizeTextarea is callable');
    } else {
      console.error('❌ autoResizeTextarea is not callable');
    }

    // Test if we can find like buttons
    const likeButtons = document.querySelectorAll('.like');
    console.log('Like buttons found:', likeButtons.length);
    likeButtons.forEach((btn, index) => {
      console.log(`Like button ${index}:`, btn.getAttribute('data-item-id'), btn.getAttribute('onclick'));
    });

    // Test if we can find comment textareas
    const commentTextareas = document.querySelectorAll('[id^="comment-text-"]');
    console.log('Comment textareas found:', commentTextareas.length);
    commentTextareas.forEach((textarea, index) => {
      console.log(`Comment textarea ${index}:`, textarea.id);
    });

    // Add event listeners to existing comment textareas
    attachCommentEventListeners();
  });

  // Function to attach event listeners to comment textareas
  function attachCommentEventListeners() {
    const commentTextareas = document.querySelectorAll('.fb-comment-input');
    const sendButtons = document.querySelectorAll('.fb-comment-send-btn');

    commentTextareas.forEach(textarea => {
      // Remove existing listeners to avoid duplicates
      textarea.removeEventListener('keydown', handleTextareaKeydown);
      textarea.removeEventListener('input', handleTextareaInput);

      // Add new listeners
      textarea.addEventListener('keydown', handleTextareaKeydown);
      textarea.addEventListener('input', handleTextareaInput);
    });

    // Add event listeners to send buttons
    sendButtons.forEach(button => {
      // Remove existing listener to avoid duplicates
      button.removeEventListener('click', handleSendButtonClick);

      // Add new listener
      button.addEventListener('click', handleSendButtonClick);
    });
  }

  // Event handler functions
  function handleTextareaKeydown(event) {
    const postId = this.getAttribute('data-post-id') || this.id.replace('comment-text-', '');

    if (typeof window.handleCommentKeydown === 'function') {
      window.handleCommentKeydown(event, postId);
    } else {
      // Fallback: Enter key submits comment
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (typeof window.submitComment === 'function') {
          window.submitComment(postId);
        }
      }
    }
  }

  function handleTextareaInput(event) {
    if (typeof window.autoResizeTextarea === 'function') {
      window.autoResizeTextarea(this);
    } else {
      // Fallback auto-resize
      this.style.height = 'auto';
      this.style.height = Math.min(this.scrollHeight, 100) + 'px';
    }
  }

  function handleSendButtonClick(event) {
    const postId = this.getAttribute('data-post-id');

    if (postId && typeof window.submitComment === 'function') {
      window.submitComment(postId);
    } else {
      console.warn('submitComment function not available or postId missing');
    }
  }

  /**
   * Setup filter tabs functionality
   */
  function setupFilterTabs() {
    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
      tab.addEventListener('click', function() {
        // Remove active class from all tabs
        filterTabs.forEach(t => t.classList.remove('active'));

        // Add active class to clicked tab
        this.classList.add('active');

        // Get filter value
        const filter = this.dataset.filter;

        // Apply filter to content manager or load connections
        if (filter === 'connections') {
          loadConnectionsContent();
        } else if (window.contentManager) {
          window.contentManager.changeFilter(filter);
        }
      });
    });
  }

  /**
   * Initialize the Facebook-style dashboard
   */
  function initFacebookDashboard() {
    // Add active class to current menu item
    const menuItems = document.querySelectorAll('.fb-menu-item');

    menuItems.forEach(item => {
      // Active state is now handled by server-side rendering

      // Add click effect
      item.addEventListener('click', function(e) {
        if (this.getAttribute('data-link')) {
          // Create ripple effect
          const ripple = document.createElement('div');
          ripple.classList.add('fb-ripple');

          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);

          ripple.style.width = ripple.style.height = `${size}px`;
          ripple.style.left = `${e.clientX - rect.left - size/2}px`;
          ripple.style.top = `${e.clientY - rect.top - size/2}px`;

          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        }
      });
    });
  }

  /**
   * Add agricultural theme elements to the page
   */
  function addAgriculturalElements() {
    // Add decorative leaf icons
    const body = document.body;

    const leaf1 = document.createElement('i');
    leaf1.className = 'bi bi-flower1 agri-leaf-icon agri-leaf-1';
    body.appendChild(leaf1);

    const leaf2 = document.createElement('i');
    leaf2.className = 'bi bi-flower2 agri-leaf-icon agri-leaf-2';
    body.appendChild(leaf2);
  }

  /**
   * Initialize scrolling effects
   */
  function initScrollEffects() {
    // Add scroll event listener
    window.addEventListener('scroll', function() {
      // Get scroll position
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Add shadow to header on scroll
      const header = document.querySelector('.fb-header');
      if (header) {
        if (scrollTop > 10) {
          header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
        } else {
          header.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
        }
      }
    });
  }

  // Functions are now loaded from facebook-interactions.js
  // This section is kept for any dashboard-specific overrides if needed



  // Share functionality is now handled by facebook-interactions.js

  function showShareModal(postId) {
    const postUrl = encodeURIComponent(`${window.location.origin}/uploads/${postId}`);
    const shareText = encodeURIComponent('Check out this farming content!');

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
      <div class="modal-dialog modal-sm">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Share Content</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="d-grid gap-2">
              <a href="https://www.facebook.com/sharer/sharer.php?u=${postUrl}"
                 target="_blank" class="btn btn-primary">
                <i class="bi bi-facebook"></i> Facebook
              </a>
              <a href="https://twitter.com/intent/tweet?text=${shareText}&url=${postUrl}"
                 target="_blank" class="btn btn-info">
                <i class="bi bi-twitter"></i> Twitter
              </a>
              <a href="https://wa.me/?text=${shareText}%20${postUrl}"
                 target="_blank" class="btn btn-success">
                <i class="bi bi-whatsapp"></i> WhatsApp
              </a>
              <button type="button" class="btn btn-secondary" onclick="copyToClipboard('${decodeURIComponent(postUrl)}')">
                <i class="bi bi-clipboard"></i> Copy Link
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
      modal.remove();
    });
  }

  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      showNotification('Link copied to clipboard!', 'success');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      showNotification('Link copied to clipboard!', 'success');
    });
  }

  // Comment functionality is now handled by facebook-interactions.js

  // Functions are now loaded from facebook-interactions.js

  function addCommentToUI(postId, comment) {
    console.log('Adding comment to UI:', postId, comment);

    const commentsSection = document.getElementById(`comments-${postId}`);
    if (!commentsSection) {
      console.error('Comments section not found for post:', postId);
      return;
    }

    let existingComments = commentsSection.querySelector('.fb-existing-comments');

    if (!existingComments) {
      existingComments = document.createElement('div');
      existingComments.className = 'fb-existing-comments';
      commentsSection.insertBefore(existingComments, commentsSection.querySelector('.fb-comment-input-section'));
    }

    const commentHTML = `
      <div class="fb-comment" data-comment-id="${comment.id}">
        <div class="fb-comment-avatar">
          ${comment.userPhotoURL ?
            `<img src="${comment.userPhotoURL}" alt="${comment.userName}" class="fb-comment-avatar-img">` :
            `<div class="fb-comment-avatar-placeholder">
              <i class="bi bi-person-fill"></i>
            </div>`
          }
        </div>
        <div class="fb-comment-content">
          <div class="fb-comment-bubble">
            <div class="fb-comment-author">
              <a href="/profile/user/${comment.userId}" class="fb-comment-author-link">
                ${comment.userName}
              </a>
            </div>
            <div class="fb-comment-text">${comment.text}</div>
          </div>
          <div class="fb-comment-actions">
            <span class="fb-comment-time">Just now</span>
            <span class="fb-comment-action" onclick="likeComment('${comment.id}')">Like</span>
            <span class="fb-comment-action" onclick="replyToComment('${comment.id}')">Reply</span>
          </div>
        </div>
      </div>
    `;

    existingComments.insertAdjacentHTML('beforeend', commentHTML);
    console.log('Comment added to UI successfully');
  }

  // updateCommentCount function is now handled by facebook-interactions.js

  function showCommentFeedback(postId, message, type) {
    const commentInput = document.getElementById(`comment-input-${postId}`);
    if (!commentInput) {
      console.error('Comment input not found for feedback');
      return;
    }

    const feedback = document.createElement('div');
    feedback.className = `alert alert-${type === 'success' ? 'success' : 'danger'} mt-2`;
    feedback.style.fontSize = '12px';
    feedback.style.padding = '4px 8px';
    feedback.textContent = message;

    commentInput.appendChild(feedback);

    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.remove();
      }
    }, 3000);
  }

  // Missing functions for comment actions
  function likeComment(commentId) {
    console.log('Like comment:', commentId);
    // TODO: Implement comment liking
    showNotification('Comment like feature coming soon!', 'info');
  }

  function replyToComment(commentId) {
    console.log('Reply to comment:', commentId);
    // TODO: Implement comment replies
    showNotification('Comment reply feature coming soon!', 'info');
  }

  // Auto-resize and keydown functions are now handled by facebook-interactions.js



  // Load connections content
  async function loadConnectionsContent() {
    const contentContainer = document.getElementById('content-container');

    if (!contentContainer) {
      console.error('Content container not found');
      return;
    }

    // Show loading state
    contentContainer.innerHTML = `
      <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Loading connections...</p>
      </div>
    `;

    try {
      // Create connections interface
      const connectionsHTML = `
        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card">
              <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-people-fill me-2"></i>Find Farmers</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <div class="input-group">
                    <input type="text" class="form-control" id="farmer-search" placeholder="Search for farmers...">
                    <button class="btn btn-outline-primary" type="button" onclick="searchFarmers()">
                      <i class="bi bi-search"></i>
                    </button>
                  </div>
                </div>
                <div id="farmers-list" class="mt-3">
                  <div class="text-center text-muted py-3">
                    <i class="bi bi-search fs-1"></i>
                    <p>Search for farmers to connect with</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="card">
              <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-chat-dots-fill me-2"></i>My Connections</h5>
              </div>
              <div class="card-body">
                <div id="my-connections">
                  <div class="text-center text-muted py-3">
                    <i class="bi bi-people fs-1"></i>
                    <p>Your connections will appear here</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="bi bi-chat-square-dots-fill me-2"></i>Quick Message</h5>
              </div>
              <div class="card-body">
                <div id="messaging-interface">
                  <div class="text-center text-muted py-3">
                    <i class="bi bi-chat-square-dots fs-1"></i>
                    <p>Connect with farmers to start messaging</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;

      contentContainer.innerHTML = connectionsHTML;

      // Load initial data
      await loadMyConnections();

    } catch (error) {
      console.error('Error loading connections content:', error);
      contentContainer.innerHTML = `
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-triangle me-2"></i>
          Error loading connections. Please try again.
        </div>
      `;
    }
  }

  // Functions are already globally available
  window.loadConnectionsContent = loadConnectionsContent;

  // Search for farmers
  async function searchFarmers() {
    const searchInput = document.getElementById('farmer-search');
    const farmersList = document.getElementById('farmers-list');

    if (!searchInput || !farmersList) return;

    const searchTerm = searchInput.value.trim();

    if (!searchTerm) {
      farmersList.innerHTML = `
        <div class="text-center text-muted py-3">
          <i class="bi bi-search fs-1"></i>
          <p>Search for farmers to connect with</p>
        </div>
      `;
      return;
    }

    // Show loading
    farmersList.innerHTML = `
      <div class="text-center py-3">
        <div class="spinner-border spinner-border-sm" role="status"></div>
        <p class="mt-2">Searching...</p>
      </div>
    `;

    try {
      // For now, create some sample farmers
      const sampleFarmers = [
        {
          uid: 'farmer1',
          displayName: 'Alice Johnson',
          farmName: 'Green Valley Farm',
          location: 'California, USA',
          photoURL: null,
          connectionStatus: 'none'
        },
        {
          uid: 'farmer2',
          displayName: 'Bob Smith',
          farmName: 'Organic Acres',
          location: 'Texas, USA',
          photoURL: null,
          connectionStatus: 'none'
        },
        {
          uid: 'farmer3',
          displayName: 'Carol Davis',
          farmName: 'Sustainable Crops Co.',
          location: 'Florida, USA',
          photoURL: null,
          connectionStatus: 'pending'
        }
      ];

      // Filter farmers based on search term
      const filteredFarmers = sampleFarmers.filter(farmer =>
        farmer.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        farmer.farmName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        farmer.location.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (filteredFarmers.length === 0) {
        farmersList.innerHTML = `
          <div class="text-center text-muted py-3">
            <i class="bi bi-person-x fs-1"></i>
            <p>No farmers found matching "${searchTerm}"</p>
          </div>
        `;
        return;
      }

      // Display farmers
      const farmersHTML = filteredFarmers.map(farmer => `
        <div class="farmer-card mb-3 p-3 border rounded">
          <div class="d-flex align-items-center">
            <div class="farmer-avatar me-3">
              ${farmer.photoURL ?
                `<img src="${farmer.photoURL}" alt="${farmer.displayName}" class="rounded-circle" width="50" height="50">` :
                `<div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="bi bi-person-fill text-white"></i>
                </div>`
              }
            </div>
            <div class="farmer-info flex-grow-1">
              <h6 class="mb-1">${farmer.displayName}</h6>
              <p class="mb-1 text-muted small">${farmer.farmName}</p>
              <p class="mb-0 text-muted small"><i class="bi bi-geo-alt"></i> ${farmer.location}</p>
            </div>
            <div class="farmer-actions">
              ${farmer.connectionStatus === 'none' ?
                `<button class="btn btn-primary btn-sm" onclick="sendConnectionRequest('${farmer.uid}')">
                  <i class="bi bi-person-plus"></i> Connect
                </button>` :
                farmer.connectionStatus === 'pending' ?
                `<button class="btn btn-warning btn-sm" disabled>
                  <i class="bi bi-clock"></i> Pending
                </button>` :
                `<button class="btn btn-success btn-sm" onclick="startConversation('${farmer.uid}')">
                  <i class="bi bi-chat-dots"></i> Message
                </button>`
              }
            </div>
          </div>
        </div>
      `).join('');

      farmersList.innerHTML = farmersHTML;

    } catch (error) {
      console.error('Error searching farmers:', error);
      farmersList.innerHTML = `
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-triangle"></i> Error searching farmers
        </div>
      `;
    }
  }

  window.searchFarmers = searchFarmers;



  // Notification system wrapper for toast notifications
  window.showNotification = function(message, type = 'info') {
    if (typeof toast !== 'undefined') {
      // Use the toast notification system
      switch(type) {
        case 'success':
          toast.success(message);
          break;
        case 'error':
          toast.error(message);
          break;
        case 'warning':
          toast.warning(message);
          break;
        case 'info':
        default:
          toast.info(message);
          break;
      }
    } else {
      // Fallback to console if toast is not available
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  };

  // Add CSS for animations and styling
  const style = document.createElement('style');
  style.textContent = `
    .spin {
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes likeFloat {
      0% {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
      100% {
        transform: translateY(-50px) scale(1.5);
        opacity: 0;
      }
    }

    @keyframes facebookLikeFloat {
      0% {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
      50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 0.8;
      }
      100% {
        transform: translateY(-40px) scale(0.8);
        opacity: 0;
      }
    }

    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes slideOutRight {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }

    .fb-post-action-button.liked {
      color: #1877f2 !important;
    }

    .fb-post-action-button.liked i {
      color: #1877f2 !important;
    }

    .notification-toast {
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      border: none;
    }

    /* Facebook-style Action Buttons */
    .fb-action-button {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      padding: 8px 12px;
      background: none;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.2s;
      color: #65676b;
      font-size: 15px;
      font-weight: 600;
      gap: 8px;
    }

    .fb-action-button:hover {
      background-color: #f2f3f5;
    }

    .fb-action-button.liked {
      color: #1877f2;
    }

    .fb-action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .fb-action-text {
      font-size: 15px;
      font-weight: 600;
    }

    /* Facebook-style Reactions */
    .fb-post-reactions {
      display: flex;
      align-items: center;
    }

    .fb-reaction-summary {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .fb-reaction-summary:hover {
      background-color: #f2f3f5;
    }

    .fb-reaction-icons {
      display: flex;
      margin-right: 6px;
    }

    .fb-reaction-icon {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-right: -2px;
      border: 2px solid white;
      background: white;
    }

    .fb-like-icon {
      background: #1877f2;
      color: white;
    }

    .fb-reaction-count {
      font-size: 13px;
      color: #65676b;
      font-weight: 400;
    }

    .fb-post-engagement {
      display: flex;
      align-items: center;
    }

    .fb-engagement-stats {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .fb-comment-count,
    .fb-share-count {
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .fb-comment-count:hover,
    .fb-share-count:hover {
      background-color: #f2f3f5;
    }

    .fb-stat-separator {
      color: #bcc0c4;
      font-weight: bold;
    }

    /* Toast Notification Styles */
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      max-width: 400px;
    }

    .toast {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      margin-bottom: 10px;
      padding: 16px;
      display: flex;
      align-items: flex-start;
      transform: translateX(100%);
      opacity: 0;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .toast.show {
      transform: translateX(0);
      opacity: 1;
    }

    .toast.hide {
      transform: translateX(100%);
      opacity: 0;
    }

    .toast-icon {
      margin-right: 12px;
      font-size: 20px;
    }

    .toast-success .toast-icon { color: #28a745; }
    .toast-error .toast-icon { color: #dc3545; }
    .toast-warning .toast-icon { color: #ffc107; }
    .toast-info .toast-icon { color: #17a2b8; }

    .toast-content {
      flex: 1;
    }

    .toast-title {
      font-weight: 600;
      margin-bottom: 4px;
      color: #333;
    }

    .toast-message {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    .toast-close {
      background: none;
      border: none;
      font-size: 18px;
      color: #999;
      cursor: pointer;
      padding: 0;
      margin-left: 12px;
    }

    .toast-close:hover {
      color: #666;
    }

    .toast-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: rgba(0, 0, 0, 0.1);
    }

    .toast-progress-bar {
      height: 100%;
      background: currentColor;
      width: 0;
    }

    .toast-success .toast-progress-bar { background: #28a745; }
    .toast-error .toast-progress-bar { background: #dc3545; }
    .toast-warning .toast-progress-bar { background: #ffc107; }
    .toast-info .toast-progress-bar { background: #17a2b8; }

    @keyframes progress {
      from { width: 100%; }
      to { width: 0%; }
    }
  `;
  document.head.appendChild(style);
</script>
