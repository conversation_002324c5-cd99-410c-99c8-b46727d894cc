/* Facebook Messenger Style CSS */

:root {
  /* Facebook Messenger Colors */
  --fb-blue: #0084ff;
  --fb-light-blue: #e9f3ff;
  --fb-dark-blue: #0078e7;
  --fb-outgoing-background: #0084ff;
  --fb-incoming-background: #f0f0f0;
  --fb-chat-background: #ffffff;
  --fb-panel-background: #f0f2f5;
  --fb-header-background: #ffffff;
  --fb-border-color: #e4e6eb;
  --fb-icon-color: #65676b;
  --fb-text-primary: #050505;
  --fb-text-secondary: #65676b;
  --fb-hover-background: #f2f2f2;
  --fb-active-background: #e6f2ff;
  --fb-notification-background: #fa3e3e;
  --fb-message-time: #65676b;
  --fb-online-indicator: #31a24c;
}

/* Main Layout */
.messenger-container {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background-color: var(--fb-panel-background);
  position: relative;
  display: flex;
}

/* Conversation List Panel */
.conversation-list-panel {
  width: 360px;
  height: 100vh;
  background-color: white;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--fb-border-color);
  z-index: 10;
  position: relative;
}

.conversation-list-header {
  padding: 12px 16px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid var(--fb-border-color);
}

.conversation-list-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--fb-text-primary);
}

.conversation-list-actions {
  display: flex;
  gap: 16px;
}

.conversation-list-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--fb-panel-background);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--fb-text-primary);
  transition: background-color 0.2s;
}

.conversation-list-icon:hover {
  background-color: var(--fb-hover-background);
}

.search-container {
  padding: 8px 16px;
  background-color: white;
}

.search-box {
  background-color: var(--fb-panel-background);
  border-radius: 50px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
}

.search-box input {
  border: none;
  outline: none;
  width: 100%;
  margin-left: 8px;
  font-size: 15px;
  background-color: transparent;
}

.search-box .search-icon {
  color: var(--fb-icon-color);
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  background-color: white;
}

.conversation-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 8px;
  margin: 2px 8px;
}

.conversation-item:hover {
  background-color: var(--fb-hover-background);
}

.conversation-item.active {
  background-color: var(--fb-active-background);
}

.conversation-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  position: relative;
}

.online-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--fb-online-indicator);
  border: 2px solid white;
  position: absolute;
  bottom: 0;
  right: 0;
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.conversation-name {
  font-weight: 500;
  color: var(--fb-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 12px;
  color: var(--fb-text-secondary);
  white-space: nowrap;
}

.conversation-message {
  display: flex;
  align-items: center;
  color: var(--fb-text-secondary);
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-message-status {
  margin-right: 4px;
  color: var(--fb-icon-color);
}

.conversation-message-preview {
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-unread {
  background-color: var(--fb-notification-background);
  color: white;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

/* Chat Panel */
.chat-panel {
  flex: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
  position: relative;
}

.chat-header {
  padding: 8px 16px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid var(--fb-border-color);
  z-index: 10;
}

.chat-header-info {
  display: flex;
  align-items: center;
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
}

.chat-name {
  font-weight: 600;
  color: var(--fb-text-primary);
}

.chat-status {
  font-size: 13px;
  color: var(--fb-text-secondary);
}

.chat-header-actions {
  display: flex;
  gap: 16px;
}

.chat-header-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--fb-panel-background);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--fb-text-primary);
  transition: background-color 0.2s;
}

.chat-header-icon:hover {
  background-color: var(--fb-hover-background);
}

/* Message styles */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.message {
  max-width: 65%;
  margin-bottom: 8px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.message.outgoing {
  align-self: flex-end;
}

.message.incoming {
  align-self: flex-start;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 18px;
  position: relative;
}

.message.outgoing .message-bubble {
  background-color: var(--fb-outgoing-background);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.incoming .message-bubble {
  background-color: var(--fb-incoming-background);
  color: var(--fb-text-primary);
  border-bottom-left-radius: 4px;
}

.message-text {
  font-size: 15px;
  line-height: 20px;
  word-wrap: break-word;
}

.message-time {
  font-size: 11px;
  color: var(--fb-message-time);
  margin-top: 2px;
  align-self: flex-end;
}

.message.outgoing .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-status {
  margin-left: 4px;
  font-size: 14px;
}

.message-status.sent {
  color: #a5a5a5;
}

.message-status.delivered {
  color: #a5a5a5;
}

.message-status.read {
  color: var(--fb-blue);
}

/* Chat input area */
.chat-footer {
  padding: 12px 16px;
  background-color: white;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
  border-top: 1px solid var(--fb-border-color);
}

.chat-footer-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--fb-panel-background);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--fb-text-primary);
  transition: background-color 0.2s;
}

.chat-footer-icon:hover {
  background-color: var(--fb-hover-background);
}

.message-input-container {
  flex: 1;
  background-color: var(--fb-panel-background);
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 15px;
  background-color: transparent;
}

.message-input::placeholder {
  color: var(--fb-text-secondary);
}

.send-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--fb-panel-background);
  border: none;
  color: var(--fb-blue);
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: var(--fb-hover-background);
}

.send-button.active {
  color: var(--fb-blue);
}

/* Info Panel */
.info-panel {
  width: 340px;
  height: 100vh;
  background-color: white;
  border-left: 1px solid var(--fb-border-color);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.info-header {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid var(--fb-border-color);
}

.info-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 12px;
}

.info-name {
  font-size: 17px;
  font-weight: 600;
  color: var(--fb-text-primary);
}

.info-status {
  font-size: 13px;
  color: var(--fb-text-secondary);
  margin-top: 4px;
}

.info-section {
  padding: 16px;
  border-bottom: 1px solid var(--fb-border-color);
}

.info-section-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--fb-text-primary);
  margin-bottom: 12px;
}

.shared-media {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
}

.shared-media-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
}

.shared-media-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Mobile responsiveness */
@media (max-width: 992px) {
  .info-panel {
    display: none;
  }
}

@media (max-width: 768px) {
  .conversation-list-panel {
    position: absolute;
    width: 100%;
    z-index: 100;
    transform: translateX(0);
    transition: transform 0.3s ease;
  }

  .conversation-list-panel.hidden {
    transform: translateX(-100%);
  }

  .chat-panel {
    width: 100%;
  }

  .chat-back-button {
    display: block !important;
  }
}
