<%- include('../partials/header') %>

<!-- Agricultural-themed Network CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="linkedin-container">
  <div class="container">
    <div class="row">
      <!-- Left Sidebar -->
      <div class="col-lg-3">
        <!-- User Profile Card -->
        <div class="linkedin-card profile-card">
          <div class="profile-background"></div>
          <% if (userData && userData.photoURL) { %>
            <img src="<%= userData.photoURL %>" class="profile-photo">
          <% } else { %>
            <div class="profile-photo d-flex align-items-center justify-content-center">
              <i class="bi bi-person-fill text-secondary" style="font-size: 2rem;"></i>
            </div>
          <% } %>
          <h5 class="profile-name"><%= user.displayName || 'User' %></h5>
          <p class="profile-headline">Sustainable Farmer</p>
          <a href="/network/profile/<%= user.uid %>" class="linkedin-btn linkedin-btn-outline w-100">View Profile</a>
        </div>

        <!-- Manage My Network -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">Manage my network</h5>
          </div>
          <div class="p-0">
            <a href="/network/connections" class="connection-card text-decoration-none">
              <i class="bi bi-people-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Connections</span>
                <span class="linkedin-badge" id="connections-count">
                  <% if (typeof connectionsCount !== 'undefined') { %>
                    <%= connectionsCount %>
                  <% } else { %>
                    0
                  <% } %>
                </span>
              </div>
            </a>

            <a href="/network/search" class="connection-card text-decoration-none">
              <i class="bi bi-person-plus-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Find Farmers</span>
              </div>
            </a>

            <a href="/messaging" class="connection-card text-decoration-none">
              <i class="bi bi-chat-dots-fill me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Messages</span>
              </div>
            </a>
          </div>
        </div>

        <!-- Farming Communities -->
        <div class="linkedin-card">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">Farming Communities</h5>
          </div>
          <div class="p-0">
            <a href="/network/search?q=organic" class="connection-card text-decoration-none">
              <i class="bi bi-hash me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Organic Farming</span>
              </div>
            </a>

            <a href="/network/search?q=sustainable" class="connection-card text-decoration-none">
              <i class="bi bi-hash me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Sustainable Practices</span>
              </div>
            </a>

            <a href="/network/search?q=irrigation" class="connection-card text-decoration-none">
              <i class="bi bi-hash me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Water Conservation</span>
              </div>
            </a>

            <a href="/network/search?q=soil" class="connection-card text-decoration-none">
              <i class="bi bi-hash me-3" style="font-size: 20px; color: var(--network-primary);"></i>
              <div class="connection-info">
                <span class="connection-name" style="color: var(--network-gray);">Soil Health</span>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="col-lg-9">
        <!-- Search Bar -->
        <div class="linkedin-card mb-4">
          <div class="linkedin-card-body">
            <form action="/network/search" method="GET">
              <div class="input-group">
                <input type="text" class="form-control" name="q" placeholder="Search for farmers by name, location, or farming type">
                <button class="linkedin-btn linkedin-btn-primary" type="submit">
                  <i class="bi bi-search me-1"></i> Find Farmers
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Connection Requests -->
        <div class="linkedin-card mb-4" id="connection-requests-section">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">
              <i class="bi bi-envelope me-2" style="color: var(--network-primary);"></i>
              Network Invitations
            </h5>
            <a href="/network/connections" class="linkedin-btn linkedin-btn-text">See all</a>
          </div>
          <div id="pending-requests-container">
            <!-- Pending requests will be loaded here -->
            <div class="text-center py-4">
              <div class="spinner-border" style="color: var(--network-primary);" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>

        <!-- People You May Know -->
        <div class="linkedin-card mb-4">
          <div class="linkedin-card-header">
            <h5 class="linkedin-card-title">
              <i class="bi bi-people me-2" style="color: var(--network-primary);"></i>
              People you may know
            </h5>
            <a href="/network/search" class="linkedin-btn linkedin-btn-text">See all</a>
          </div>
          <div class="linkedin-card-body p-0">
            <div class="row m-0" id="suggested-connections-container">
              <!-- Suggested connections will be loaded here -->
              <div class="text-center py-4 col-12">
                <div class="spinner-border" style="color: var(--network-primary);" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Post Card -->
        <div class="linkedin-card">
          <div class="linkedin-card-body">
            <div class="d-flex mb-3">
              <% if (userData && userData.photoURL) { %>
                <img src="<%= userData.photoURL %>" class="profile-photo" style="width: 48px; height: 48px;">
              <% } else { %>
                <div class="profile-photo d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                  <i class="bi bi-person-fill text-secondary"></i>
                </div>
              <% } %>
              <div class="flex-grow-1 ms-3">
                <textarea class="form-control" id="post-text" rows="2" placeholder="Share your farming knowledge or ask a question..."></textarea>
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                <button class="linkedin-btn linkedin-btn-outline" id="upload-image-btn">
                  <i class="bi bi-image me-1"></i> Photo
                </button>
                <input type="file" id="post-image" class="d-none" accept="image/*">
                <div id="image-preview-container" class="mt-2 d-none">
                  <img id="image-preview" class="img-fluid rounded" style="max-height: 200px;">
                  <button class="btn btn-sm btn-danger position-absolute" id="remove-image-btn" style="top: 5px; right: 5px;">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </div>
              <button class="linkedin-btn linkedin-btn-primary" id="create-post-btn">Post</button>
            </div>
          </div>
        </div>

        <!-- Index Error Alert -->
        <% if (typeof indexError !== 'undefined' && indexError) { %>
          <div class="alert alert-warning mb-4">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <strong>Database Index Building:</strong> <%= errorMessage %>
          </div>
        <% } %>

        <!-- Posts Feed -->
        <div id="posts-container">
          <% if (posts && posts.length > 0) { %>
            <% posts.forEach(post => { %>
              <!-- Post Card Template -->
              <div class="linkedin-card post-card" data-post-id="<%= post.id %>">
                <!-- Post content will go here -->
              </div>
            <% }); %>
          <% } else { %>
            <div class="linkedin-card">
              <div class="linkedin-card-body text-center py-5">
                <i class="bi bi-newspaper text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3">No Posts Yet</h5>
                <p class="text-muted">
                  <% if (typeof indexError !== 'undefined' && indexError) { %>
                    Please wait while the database index is being built.
                  <% } else { %>
                    Be the first to share your farming knowledge!
                  <% } %>
                </p>
              </div>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Connection Request Template (Hidden) -->
<template id="connection-request-template">
  <div class="connection-card" data-connection-id="">
    <img class="connection-photo" src="" alt="Profile photo">
    <div class="connection-info">
      <h6 class="connection-name"></h6>
      <p class="connection-headline"></p>
      <div class="connection-location mb-2"></div>
      <div class="connection-actions">
        <button class="linkedin-btn linkedin-btn-primary accept-request-btn" data-id="">
          <i class="bi bi-check-lg me-1"></i> Accept
        </button>
        <button class="linkedin-btn linkedin-btn-outline reject-request-btn" data-id="">
          <i class="bi bi-x-lg me-1"></i> Ignore
        </button>
      </div>
    </div>
  </div>
</template>

<!-- Suggested Connection Template (Hidden) -->
<template id="suggested-connection-template">
  <div class="col-md-4 mb-3">
    <div class="linkedin-card h-100 hover-shadow">
      <div class="p-3">
        <div class="text-center mb-3">
          <div class="position-relative d-inline-block">
            <img class="connection-photo" src="" alt="Profile photo" style="width: 80px; height: 80px; border: 3px solid var(--network-primary-light); padding: 2px;">
            <span class="online-indicator position-absolute" style="bottom: 5px; right: 5px;"></span>
          </div>
          <h6 class="connection-name mt-2 mb-1 fw-bold"></h6>
          <p class="connection-headline small text-muted"></p>
          <div class="connection-location small text-muted mb-2"></div>
        </div>
        <div class="d-grid">
          <button class="linkedin-btn linkedin-btn-outline connect-btn" data-id="">
            <i class="bi bi-person-plus-fill me-1"></i> Connect
          </button>
          <a href="#" class="linkedin-btn linkedin-btn-text mt-2 view-profile-btn">
            <i class="bi bi-eye me-1"></i> View Profile
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="/js/toast-notifications.js"></script>
<script src="/js/network-linkedin.js"></script>

<%- include('../partials/footer') %>
