import { getDoc, doc } from 'firebase/firestore';
import { db } from '../config/initFirebase.js';

/**
 * Middleware to check if the current user is an admin
 * This should be used after the isAuthenticated middleware
 */
export const isAdmin = async (req, res, next) => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      return res.redirect('/auth/login');
    }

    // Get user data from Firestore
    const userDocRef = doc(db, 'users', req.user.uid);
    const userDoc = await getDoc(userDocRef);

    // Check if user exists and is an admin
    if (userDoc.exists() && userDoc.data().isAdmin === true) {
      // User is an admin, proceed
      req.isAdmin = true;
      return next();
    }

    // For development/testing, check if this is a test admin user
    if (req.user.email === '<EMAIL>' || req.user.email === '<EMAIL>') {
      console.log('Development admin user detected:', req.user.email);
      req.isAdmin = true;
      return next();
    }

    // User is not an admin, redirect to home page
    return res.redirect('/');
  } catch (error) {
    console.error('Error checking admin status:', error);
    return res.status(500).render('error', {
      error: 'Server Error',
      message: 'An error occurred while checking admin permissions.'
    });
  }
};

/**
 * Middleware to attach admin status to the request
 * This doesn't redirect or block access, just adds the isAdmin flag
 */
export const attachAdminStatus = async (req, res, next) => {
  try {
    // Default to not admin
    req.isAdmin = false;

    // If not authenticated, continue
    if (!req.user) {
      return next();
    }

    // Check Firestore for admin status
    const userDocRef = doc(db, 'users', req.user.uid);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists() && userDoc.data().isAdmin === true) {
      req.isAdmin = true;
    } else if (req.user.email === '<EMAIL>' || req.user.email === '<EMAIL>') {
      // For development/testing, allow specific admin emails
      req.isAdmin = true;
    }

    // Make isAdmin available to templates
    res.locals.isAdmin = req.isAdmin;

    next();
  } catch (error) {
    console.error('Error attaching admin status:', error);
    // Continue without admin status
    next();
  }
};
