<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/articles">Articles</a></li>
        <li class="breadcrumb-item active" aria-current="page">My Articles</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4">My Articles</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
    
    <div class="mb-4">
      <a href="/articles/new" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Write New Article
      </a>
    </div>
  </div>
</div>

<div class="row">
  <% if (articles && articles.length > 0) { %>
    <div class="col-md-12">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>Title</th>
              <th>Category</th>
              <th>Published Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <% articles.forEach(article => { %>
              <tr>
                <td>
                  <a href="/articles/<%= article.id %>" class="text-decoration-none">
                    <%= article.title %>
                  </a>
                </td>
                <td><span class="badge bg-secondary"><%= article.category %></span></td>
                <td><%= new Date(article.createdAt).toLocaleDateString() %></td>
                <td><span class="badge bg-success">Published</span></td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <a href="/articles/<%= article.id %>" class="btn btn-outline-secondary">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="/articles/<%= article.id %>/edit" class="btn btn-outline-primary">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteArticleModal<%= article.id %>">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                  
                  <!-- Delete Modal for each article -->
                  <div class="modal fade" id="deleteArticleModal<%= article.id %>" tabindex="-1" aria-labelledby="deleteArticleModalLabel<%= article.id %>" aria-hidden="true">
                    <div class="modal-dialog">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h5 class="modal-title" id="deleteArticleModalLabel<%= article.id %>">Confirm Delete</h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                          <p>Are you sure you want to delete "<%= article.title %>"? This action cannot be undone.</p>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                          <form action="/articles/<%= article.id %>/delete" method="POST">
                            <button type="submit" class="btn btn-danger">Delete</button>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
  <% } else { %>
    <div class="col-md-12">
      <div class="alert alert-info" role="alert">
        You haven't published any articles yet. Click the "Write New Article" button to get started.
      </div>
    </div>
  <% } %>
</div>
