// Enhanced Facebook-style Interactions
console.log('=== FACEBOOK INTERACTIONS SCRIPT LOADING ===');

/**
 * Handle like button click
 */
async function handleLike(postId) {
  console.log('=== LIKE FUNCTION CALLED ===');
  console.log('Post ID:', postId);
  console.log('Current user ID:', window.currentUserId);

  const likeButton = document.querySelector(`[data-item-id="${postId}"].like`);
  console.log('Like button found:', !!likeButton);

  if (!likeButton) {
    console.error('Like button not found for post:', postId);
    console.log('Available like buttons:', document.querySelectorAll('.like').length);
    if (typeof showNotification === 'function') {
      showNotification('Like button not found', 'error');
    }
    return;
  }

  const likeIcon = likeButton.querySelector('i');
  const likeText = likeButton.querySelector('span');
  const isLiked = likeButton.classList.contains('liked');

  try {
    // Optimistic UI update
    if (isLiked) {
      // Unlike
      likeButton.classList.remove('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up';
      likeText.textContent = 'Like';
      likeButton.style.color = '';
    } else {
      // Like
      likeButton.classList.add('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up-fill';
      likeText.textContent = 'Liked';
      likeButton.style.color = '#1877f2';

      // Create like animation
      createLikeAnimation(likeButton);
    }

    // Make API call
    console.log('Making API call to:', `/uploads/${postId}/like`);
    const response = await fetch(`/uploads/${postId}/like`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'include'
    });
    console.log('API response status:', response.status, 'OK:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error response:', errorText);
      throw new Error(`Failed to like post: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Like API response:', result);

    if (!result.success) {
      throw new Error(result.error || 'Failed to like post');
    }

    // Show success notification
    if (typeof showNotification === 'function') {
      showNotification(result.liked ? 'Post liked!' : 'Post unliked!', 'success');
    }

    // Update button state based on server response
    if (result.liked) {
      likeButton.classList.add('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up-fill';
      likeText.textContent = 'Liked';
      likeButton.style.color = '#1877f2';
    } else {
      likeButton.classList.remove('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up';
      likeText.textContent = 'Like';
      likeButton.style.color = '';
    }

    // Update like count display with the actual count from server
    updateLikeCountDisplay(postId, result.likeCount || 0);

  } catch (error) {
    console.error('Error liking post:', error);

    // Revert optimistic update on error
    if (isLiked) {
      likeButton.classList.add('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up-fill';
      likeText.textContent = 'Liked';
      likeButton.style.color = '#1877f2';
    } else {
      likeButton.classList.remove('liked');
      likeIcon.className = 'bi bi-hand-thumbs-up';
      likeText.textContent = 'Like';
      likeButton.style.color = '';
    }

    // Show error message
    if (typeof showNotification === 'function') {
      showNotification('Failed to like post. Please try again.', 'error');
    }
  }
};

console.log('handleLike function defined:', typeof handleLike);

/**
 * Focus comment input
 */
function focusCommentInput(postId) {
  const commentInput = document.getElementById(`comment-text-${postId}`);
  if (commentInput) {
    commentInput.focus();
    commentInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

console.log('focusCommentInput function defined:', typeof focusCommentInput);

/**
 * Handle comment keydown (Enter to submit)
 */
function handleCommentKeydown(event, postId) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    submitComment(postId);
  }
}

console.log('handleCommentKeydown function defined:', typeof handleCommentKeydown);

/**
 * Auto-resize textarea
 */
function autoResizeTextarea(textarea) {
  textarea.style.height = 'auto';
  textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
}

console.log('autoResizeTextarea function defined:', typeof autoResizeTextarea);

/**
 * Handle comment submission
 */
async function submitComment(postId) {
  console.log('Submitting comment for post:', postId);

  const textarea = document.getElementById(`comment-text-${postId}`);

  if (!textarea) {
    console.error('Comment textarea not found for post:', postId);
    if (typeof showNotification === 'function') {
      showNotification('Comment input not found', 'error');
    }
    return;
  }

  const commentText = textarea.value.trim();

  if (!commentText) {
    if (typeof showNotification === 'function') {
      showNotification('Please enter a comment', 'error');
    }
    return;
  }

  try {
    // Show loading state
    const sendBtn = textarea.parentElement.querySelector('.fb-comment-send-btn');
    if (sendBtn) {
      const originalIcon = sendBtn.innerHTML;
      sendBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
      sendBtn.disabled = true;
    }

    // Submit comment to API
    const response = await fetch(`/uploads/${postId}/comments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        text: commentText
      })
    });

    if (response.ok) {
      const result = await response.json();

      // Clear the textarea
      textarea.value = '';
      autoResizeTextarea(textarea);

      // Add the new comment to the UI
      addCommentToUI(postId, result.comment);

      // Update comment count with the actual count from server
      updateCommentCount(postId, result.commentCount || 1);

      // Show success feedback
      if (typeof showNotification === 'function') {
        showNotification('Comment posted!', 'success');
      }

    } else {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      throw new Error('Failed to post comment: ' + response.status);
    }
  } catch (error) {
    console.error('Error posting comment:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to post comment. Please try again.', 'error');
    }
  } finally {
    // Reset button state
    const sendBtn = textarea.parentElement.querySelector('.fb-comment-send-btn');
    if (sendBtn) {
      sendBtn.innerHTML = '<i class="bi bi-send"></i>';
      sendBtn.disabled = false;
    }
  }
}

console.log('submitComment function defined:', typeof submitComment);

/**
 * Handle share button click
 */
function handleShare(postId) {
  // Create share modal or use Web Share API
  if (navigator.share) {
    const post = document.querySelector(`[data-post-id="${postId}"]`);
    const title = post.querySelector('.fb-post-text h5')?.textContent || 'Sustainable Farming Post';
    const text = post.querySelector('.fb-post-text p')?.textContent || '';

    navigator.share({
      title: title,
      text: text,
      url: window.location.href + '#post-' + postId
    }).catch(console.error);
  } else {
    // Fallback: Copy link to clipboard
    const url = window.location.href + '#post-' + postId;
    navigator.clipboard.writeText(url).then(() => {
      if (typeof showNotification === 'function') {
        showNotification('Link copied to clipboard!', 'success');
      }
    }).catch(() => {
      if (typeof showNotification === 'function') {
        showNotification('Failed to copy link', 'error');
      }
    });
  }
}

console.log('handleShare function defined:', typeof handleShare);

// Make functions available globally immediately - override any placeholders
if (typeof window !== 'undefined') {
  // Force assignment to override any existing placeholders
  window.handleLike = handleLike;
  window.focusCommentInput = focusCommentInput;
  window.handleCommentKeydown = handleCommentKeydown;
  window.autoResizeTextarea = autoResizeTextarea;
  window.submitComment = submitComment;
  window.handleShare = handleShare;

  // Log function availability
  console.log('=== FUNCTIONS ASSIGNED TO WINDOW OBJECT ===');
  console.log('- handleLike:', typeof window.handleLike, window.handleLike);
  console.log('- focusCommentInput:', typeof window.focusCommentInput, window.focusCommentInput);
  console.log('- handleCommentKeydown:', typeof window.handleCommentKeydown, window.handleCommentKeydown);
  console.log('- autoResizeTextarea:', typeof window.autoResizeTextarea, window.autoResizeTextarea);
  console.log('- submitComment:', typeof window.submitComment, window.submitComment);
  console.log('- handleShare:', typeof window.handleShare, window.handleShare);
}

// Initialize Facebook-style interactions
document.addEventListener('DOMContentLoaded', function() {
  // Force assignment to ensure functions are available globally and override any placeholders
  window.handleLike = handleLike;
  window.submitComment = submitComment;
  window.focusCommentInput = focusCommentInput;
  window.handleShare = handleShare;
  window.handleCommentKeydown = handleCommentKeydown;
  window.autoResizeTextarea = autoResizeTextarea;

  console.log('=== DOMContentLoaded: Facebook interactions initializing ===');
  console.log('handleLike available:', typeof window.handleLike, 'function ref:', !!window.handleLike);
  console.log('submitComment available:', typeof window.submitComment, 'function ref:', !!window.submitComment);
  console.log('focusCommentInput available:', typeof window.focusCommentInput, 'function ref:', !!window.focusCommentInput);
  console.log('handleShare available:', typeof window.handleShare, 'function ref:', !!window.handleShare);
  console.log('handleCommentKeydown available:', typeof window.handleCommentKeydown, 'function ref:', !!window.handleCommentKeydown);
  console.log('autoResizeTextarea available:', typeof window.autoResizeTextarea, 'function ref:', !!window.autoResizeTextarea);

  initializeFacebookInteractions();
  initializeTimeAgo();
  initializeCommentInputs();

  console.log('Facebook interactions initialized successfully');

  // Test function calls
  setTimeout(() => {
    console.log('=== TESTING FUNCTION CALLS ===');
    try {
      if (typeof window.handleLike === 'function') {
        console.log('✓ handleLike function is callable');
      } else {
        console.error('✗ handleLike function is not callable');
      }

      if (typeof window.focusCommentInput === 'function') {
        console.log('✓ focusCommentInput function is callable');
      } else {
        console.error('✗ focusCommentInput function is not callable');
      }
    } catch (error) {
      console.error('Error testing functions:', error);
    }
  }, 500);
});

/**
 * Initialize Facebook-style interactions
 */
function initializeFacebookInteractions() {
  // Add ripple effect to action buttons
  document.querySelectorAll('.fb-post-action-button').forEach(button => {
    button.addEventListener('click', createRippleEffect);
  });

  // Initialize like button states
  updateLikeButtonStates();

  // Initialize auto-resize for comment inputs
  document.querySelectorAll('.fb-comment-input').forEach(textarea => {
    autoResizeTextarea(textarea);
  });
}

/**
 * Create ripple effect on button click
 */
function createRippleEffect(e) {
  const button = e.currentTarget;
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = e.clientX - rect.left - size / 2;
  const y = e.clientY - rect.top - size / 2;

  const ripple = document.createElement('div');
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
  `;

  button.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}



/**
 * Update like button states based on current user's likes
 */
function updateLikeButtonStates() {
  // This function would normally check the current user's likes from the server
  // For now, we'll just ensure all like buttons are properly initialized
  document.querySelectorAll('.fb-post-action-button.like').forEach(button => {
    const isLiked = button.classList.contains('liked');
    const icon = button.querySelector('i');
    const text = button.querySelector('span');

    if (isLiked) {
      icon.className = 'bi bi-hand-thumbs-up-fill';
      text.textContent = 'Liked';
      button.style.color = '#1877f2';
    } else {
      icon.className = 'bi bi-hand-thumbs-up';
      text.textContent = 'Like';
      button.style.color = '';
    }
  });
}

/**
 * Toggle like button state
 */
function toggleLikeButton(button, isLiked) {
  if (isLiked) {
    button.classList.add('liked');
    button.querySelector('i').className = 'bi bi-hand-thumbs-up-fill';
  } else {
    button.classList.remove('liked');
    button.querySelector('i').className = 'bi bi-hand-thumbs-up';
  }
}

/**
 * Update like count display with exact count
 */
function updateLikeCountDisplay(postId, exactCount) {
  const statsDiv = document.querySelector(`[data-post-id="${postId}"] .fb-post-stats > div:first-child`);
  if (!statsDiv) {
    console.error('Stats div not found for post:', postId);
    return;
  }

  const countSpan = statsDiv.querySelector('span');
  if (countSpan) {
    countSpan.textContent = exactCount;
  } else {
    // If no span exists, create the structure
    statsDiv.innerHTML = '<i class="bi bi-hand-thumbs-up-fill"></i> <span>' + exactCount + '</span>';
  }
}

/**
 * Update like count display (legacy function for compatibility)
 */
function updateLikeCount(postId, increment) {
  const statsDiv = document.querySelector(`[data-post-id="${postId}"] .fb-post-stats > div:first-child`);
  const countSpan = statsDiv.querySelector('span');

  if (countSpan) {
    const currentCount = parseInt(countSpan.textContent) || 0;
    const newCount = increment ? currentCount + 1 : Math.max(0, currentCount - 1);
    countSpan.textContent = newCount;
  } else if (statsDiv) {
    // If no span exists, create the structure
    const newCount = increment ? 1 : 0;
    statsDiv.innerHTML = '<i class="bi bi-hand-thumbs-up-fill"></i> <span>' + newCount + '</span>';
  }
}

/**
 * Create like animation
 */
function createLikeAnimation(button) {
  const animation = document.createElement('div');
  animation.className = 'like-animation';
  animation.innerHTML = '<i class="bi bi-heart-fill" style="color: #e74c3c; font-size: 24px;"></i>';
  
  button.style.position = 'relative';
  button.appendChild(animation);
  
  setTimeout(() => {
    animation.remove();
  }, 700);
}



/**
 * Initialize comment inputs
 */
function initializeCommentInputs() {
  document.querySelectorAll('.fb-comment-input').forEach(textarea => {
    textarea.addEventListener('input', function() {
      autoResizeTextarea(this);
    });
  });
}

/**
 * Add comment to UI
 */
function addCommentToUI(postId, comment) {
  const commentsSection = document.getElementById(`comments-${postId}`);
  if (!commentsSection) {
    console.error('Comments section not found for post:', postId);
    return;
  }

  let existingComments = commentsSection.querySelector('.fb-existing-comments');

  if (!existingComments) {
    existingComments = document.createElement('div');
    existingComments.className = 'fb-existing-comments';
    commentsSection.insertBefore(existingComments, commentsSection.querySelector('.fb-comment-input-section'));
  }

  const commentHTML = `
    <div class="fb-comment" data-comment-id="${comment.id}">
      <div class="fb-comment-avatar">
        ${comment.userPhotoURL ?
          `<img src="${comment.userPhotoURL}" alt="${comment.userName}" class="fb-comment-avatar-img">` :
          `<div class="fb-comment-avatar-placeholder">
            <i class="bi bi-person-fill"></i>
          </div>`
        }
      </div>
      <div class="fb-comment-content">
        <div class="fb-comment-bubble">
          <div class="fb-comment-author">
            <a href="/profile/user/${comment.userId}" class="fb-comment-author-link">
              ${comment.userName}
            </a>
          </div>
          <div class="fb-comment-text">${comment.text}</div>
        </div>
        <div class="fb-comment-actions">
          <span class="fb-comment-time">Just now</span>
          <span class="fb-comment-action" onclick="likeComment('${comment.id}')">Like</span>
          <span class="fb-comment-action" onclick="replyToComment('${comment.id}')">Reply</span>
        </div>
      </div>
    </div>
  `;

  existingComments.insertAdjacentHTML('beforeend', commentHTML);
}

/**
 * Update comment count
 */
function updateCommentCount(postId, newCount) {
  // Find the post by data-post-id attribute
  const postElement = document.querySelector(`[data-post-id="${postId}"]`);
  if (!postElement) {
    console.error('Post element not found for ID:', postId);
    return;
  }

  // Find the stats div (second div in fb-post-stats)
  const statsDiv = postElement.querySelector('.fb-post-stats div:last-child');
  if (!statsDiv) {
    console.error('Stats div not found for post:', postId);
    return;
  }

  // Find the comment count span (first span in the stats div)
  const commentCountSpan = statsDiv.querySelector('span:first-child');
  if (commentCountSpan) {
    commentCountSpan.textContent = newCount;
  } else {
    // If no span exists, update the entire content with proper structure
    const commentText = newCount === 1 ? 'comment' : 'comments';
    const shareCount = 0; // Default share count
    const shareText = shareCount === 1 ? 'share' : 'shares';
    statsDiv.innerHTML = `<span>${newCount}</span> ${commentText} · <span>${shareCount}</span> ${shareText}`;
  }
}



/**
 * Initialize time ago functionality
 */
function initializeTimeAgo() {
  updateTimeAgo();
  setInterval(updateTimeAgo, 60000); // Update every minute
}

/**
 * Update time ago displays
 */
function updateTimeAgo() {
  document.querySelectorAll('.time-ago').forEach(element => {
    const time = element.getAttribute('data-time');
    if (time) {
      element.textContent = getTimeAgo(new Date(time));
    }
  });
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
  
  return date.toLocaleDateString();
}

/**
 * Show notification - wrapper for toast system
 */
function showNotification(message, type = 'info') {
  if (typeof toast !== 'undefined') {
    // Use the toast notification system
    switch(type) {
      case 'success':
        toast.success(message);
        break;
      case 'error':
        toast.error(message);
        break;
      case 'warning':
        toast.warning(message);
        break;
      case 'info':
      default:
        toast.info(message);
        break;
    }
  } else {
    // Fallback to console if toast is not available
    console.log(`${type.toUpperCase()}: ${message}`);
  }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// Export functions to global scope
window.handleLike = handleLike;
window.submitComment = submitComment;
window.focusCommentInput = focusCommentInput;
window.handleCommentKeydown = handleCommentKeydown;
window.autoResizeTextarea = autoResizeTextarea;
window.handleShare = handleShare;
window.showNotification = showNotification;
