/* Footer Styles */
footer {
  background-color: var(--theme-primary, #4CAF50) !important; /* Use theme variable with fallback */
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--theme-text-light, #ffffff);
  font-size: 0.9rem;
  padding: 1rem 0;
  margin-top: 3rem;
  width: 100%;
}

footer p {
  margin-bottom: 0;
}

/* Make footer stay at the bottom */
html, body {
  height: 100%;
}

body {
  display: flex;
  flex-direction: column;
}

.container.mt-4 {
  flex: 1 0 auto;
  padding-bottom: 2rem;
}

footer {
  flex-shrink: 0;
  margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  footer {
    padding: 0.75rem 0;
  }

  footer p {
    font-size: 0.85rem;
  }
}
