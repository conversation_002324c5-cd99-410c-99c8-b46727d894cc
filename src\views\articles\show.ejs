<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/articles">Articles</a></li>
        <% if (article && article.category) { %>
          <li class="breadcrumb-item"><a href="/articles/category/<%= article.category.toLowerCase().replace(/\s+/g, '-') %>"><%= article.category %></a></li>
        <% } %>
        <li class="breadcrumb-item active" aria-current="page"><%= article ? article.title : 'Article Details' %></li>
      </ol>
    </nav>
  </div>
</div>

<% if (!article) { %>
  <div class="row">
    <div class="col-md-12">
      <div class="alert alert-danger" role="alert">
        Article not found or an error occurred.
      </div>
      <a href="/articles" class="btn btn-primary">Back to Articles</a>
    </div>
  </div>
<% } else { %>
  <div class="row">
    <div class="col-md-8">
      <article class="blog-post">
        <h1 class="mb-3"><%= article.title %></h1>
        
        <div class="mb-4">
          <span class="badge bg-secondary me-2"><%= article.category %></span>
          <small class="text-muted">
            <i class="bi bi-calendar"></i> <%= new Date(article.createdAt).toLocaleDateString() %>
            <span class="mx-2">|</span>
            <i class="bi bi-person"></i> <%= article.userName %>
          </small>
        </div>
        
        <% if (article.imageUrl) { %>
          <img src="<%= article.imageUrl %>" class="img-fluid rounded mb-4" alt="<%= article.title %>">
        <% } %>
        
        <div class="article-content mb-5">
          <%- article.content %>
        </div>
        
        <% if (article.tags && article.tags.length > 0) { %>
          <div class="mb-4">
            <h5>Tags:</h5>
            <div>
              <% article.tags.forEach(tag => { %>
                <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#<%= tag %></a>
              <% }); %>
            </div>
          </div>
        <% } %>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
          <a href="/articles" class="btn btn-outline-success">
            <i class="bi bi-arrow-left"></i> Back to Articles
          </a>
          
          <div>
            <button class="btn btn-outline-primary me-2" id="shareArticleBtn">
              <i class="bi bi-share"></i> Share
            </button>
            
            <% if (typeof user !== 'undefined' && user && user.uid === article.userId) { %>
              <div class="btn-group">
                <a href="/articles/<%= article.id %>/edit" class="btn btn-outline-secondary">
                  <i class="bi bi-pencil"></i> Edit
                </a>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteArticleModal">
                  <i class="bi bi-trash"></i> Delete
                </button>
              </div>
            <% } %>
          </div>
        </div>
      </article>
      
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h4 class="mb-0">Comments</h4>
        </div>
        <div class="card-body">
          <!-- This would be populated with actual comments in a real application -->
          <div class="mb-4">
            <form id="commentForm">
              <div class="mb-3">
                <label for="commentText" class="form-label">Leave a comment</label>
                <textarea class="form-control" id="commentText" rows="3" placeholder="Share your thoughts..."></textarea>
              </div>
              <button type="submit" class="btn btn-success">Submit</button>
            </form>
          </div>
          
          <div class="comment">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="bi bi-person text-secondary"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">Sample User</h6>
                  <small class="text-muted">2 days ago</small>
                </div>
                <p class="mb-0">This is a sample comment. In a real application, users would be able to leave comments on articles.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="position-sticky" style="top: 2rem;">
        <div class="card mb-4">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0">About the Author</h5>
          </div>
          <div class="card-body">
            <div class="text-center mb-3">
              <div class="avatar-placeholder bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                <i class="bi bi-person-circle text-success" style="font-size: 2.5rem;"></i>
              </div>
              <h5><%= article.userName %></h5>
            </div>
            <p class="card-text">This author has shared their farming knowledge and experience with the community.</p>
            <div class="d-grid">
              <a href="#" class="btn btn-outline-success">View Profile</a>
            </div>
          </div>
        </div>
        
        <div class="card mb-4">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0">Related Articles</h5>
          </div>
          <div class="card-body">
            <div class="list-group list-group-flush">
              <!-- This would be populated with actual related articles in a real application -->
              <a href="#" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h6 class="mb-1">Related Article 1</h6>
                </div>
                <small class="text-muted">3 days ago</small>
              </a>
              <a href="#" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h6 class="mb-1">Related Article 2</h6>
                </div>
                <small class="text-muted">1 week ago</small>
              </a>
              <a href="#" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                  <h6 class="mb-1">Related Article 3</h6>
                </div>
                <small class="text-muted">2 weeks ago</small>
              </a>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0">Popular Tags</h5>
          </div>
          <div class="card-body">
            <div class="d-flex flex-wrap">
              <!-- This would be populated with actual popular tags in a real application -->
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#sustainable</a>
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#organic</a>
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#farming</a>
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#crops</a>
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#soil</a>
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#water</a>
              <a href="#" class="badge bg-light text-dark text-decoration-none me-1 mb-1">#pestcontrol</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Article Modal -->
  <% if (typeof user !== 'undefined' && user && user.uid === article.userId) { %>
    <div class="modal fade" id="deleteArticleModal" tabindex="-1" aria-labelledby="deleteArticleModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="deleteArticleModalLabel">Confirm Delete</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete "<%= article.title %>"? This action cannot be undone.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <form action="/articles/<%= article.id %>/delete" method="POST">
              <button type="submit" class="btn btn-danger">Delete</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  <% } %>
<% } %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle share button
    const shareArticleBtn = document.getElementById('shareArticleBtn');
    if (shareArticleBtn) {
      shareArticleBtn.addEventListener('click', function() {
        alert('Share functionality would be implemented here in a real application.');
      });
    }
    
    // Handle comment form submission
    const commentForm = document.getElementById('commentForm');
    if (commentForm) {
      commentForm.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Comment functionality would be implemented in a real application.');
        document.getElementById('commentText').value = '';
      });
    }
  });
</script>
