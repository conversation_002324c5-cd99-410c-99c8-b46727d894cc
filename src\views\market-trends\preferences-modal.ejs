<!-- User Preferences Modal -->
<div class="modal fade" id="preferencesModal" tabindex="-1" aria-labelledby="preferencesModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="preferencesModalLabel">Market Trends Preferences</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="preferencesForm">
          <div class="mb-3">
            <label for="defaultCrop" class="form-label">Default Crop</label>
            <select class="form-select" id="defaultCrop">
              <option value="all">All Crops</option>
              <!-- Options will be populated dynamically -->
            </select>
            <div class="form-text">Select the default crop to display when you open the page.</div>
          </div>
          
          <div class="mb-3">
            <label for="defaultLocation" class="form-label">Default Market Location</label>
            <select class="form-select" id="defaultLocation">
              <option value="all">All Locations</option>
              <!-- Options will be populated dynamically -->
            </select>
            <div class="form-text">Select the default market location to display.</div>
          </div>
          
          <div class="mb-3">
            <label for="defaultTimeRange" class="form-label">Default Time Range</label>
            <select class="form-select" id="defaultTimeRange">
              <option value="1day">Last 24 Hours</option>
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="1year">Last Year</option>
              <option value="5years">Last 5 Years</option>
            </select>
            <div class="form-text">Select the default time range for price trends.</div>
          </div>
          
          <div class="mb-3">
            <label for="defaultChartType" class="form-label">Default Chart Type</label>
            <select class="form-select" id="defaultChartType">
              <option value="line">Line Chart</option>
              <option value="bar">Bar Chart</option>
              <option value="heatmap">Heatmap</option>
            </select>
            <div class="form-text">Select the default chart type for price trends.</div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Theme</label>
            <div class="d-flex gap-3">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="theme" id="themeLight" value="light" checked>
                <label class="form-check-label" for="themeLight">
                  Light
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="theme" id="themeDark" value="dark">
                <label class="form-check-label" for="themeDark">
                  Dark
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="theme" id="themeSystem" value="system">
                <label class="form-check-label" for="themeSystem">
                  System Default
                </label>
              </div>
            </div>
            <div class="form-text">Select your preferred theme for the market trends page.</div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Data Refresh</label>
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="autoRefresh">
              <label class="form-check-label" for="autoRefresh">Auto-refresh data</label>
            </div>
            <div class="form-text">When enabled, data will automatically refresh every 5 minutes.</div>
          </div>
          
          <div class="mb-3">
            <label for="refreshInterval" class="form-label">Refresh Interval (minutes)</label>
            <input type="number" class="form-control" id="refreshInterval" min="1" max="60" value="5">
            <div class="form-text">How often to refresh the data (1-60 minutes).</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="savePreferencesBtn">Save Preferences</button>
      </div>
    </div>
  </div>
</div>

<!-- Export Options Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exportModalLabel">Export Market Data</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="exportForm">
          <div class="mb-3">
            <label class="form-label">Export Format</label>
            <div class="d-flex gap-3">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="exportFormat" id="formatCSV" value="csv" checked>
                <label class="form-check-label" for="formatCSV">
                  CSV
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="exportFormat" id="formatJSON" value="json">
                <label class="form-check-label" for="formatJSON">
                  JSON
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="exportFormat" id="formatExcel" value="excel">
                <label class="form-check-label" for="formatExcel">
                  Excel
                </label>
              </div>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Data to Export</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="exportCurrentView" checked>
              <label class="form-check-label" for="exportCurrentView">
                Current View (filtered data)
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="exportAllData">
              <label class="form-check-label" for="exportAllData">
                All Available Data
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="exportChartData">
              <label class="form-check-label" for="exportChartData">
                Chart Data Only
              </label>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Include</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="includeMetadata" checked>
              <label class="form-check-label" for="includeMetadata">
                Include Metadata
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="includeStatistics" checked>
              <label class="form-check-label" for="includeStatistics">
                Include Statistics
              </label>
            </div>
          </div>
          
          <div class="mb-3">
            <label for="fileName" class="form-label">File Name</label>
            <input type="text" class="form-control" id="fileName" value="market-trends-export">
            <div class="form-text">Name for the exported file (without extension).</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="exportDataBtn">Export Data</button>
      </div>
    </div>
  </div>
</div>
