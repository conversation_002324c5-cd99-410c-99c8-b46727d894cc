import express from 'express';
import { 
  getCrops, 
  getCropById, 
  getCropsByUser,
  addCrop,
  updateCrop,
  deleteCrop
} from '../services/localAuthService.js';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// Get all crops
router.get('/', (req, res) => {
  try {
    const crops = getCrops();
    res.render('crops/index', { crops });
  } catch (error) {
    console.error('Error getting crops:', error);
    res.render('crops/index', { 
      crops: [],
      error: 'Error loading crops: ' + error.message
    });
  }
});

// Get crop details
router.get('/:id', (req, res) => {
  try {
    const crop = getCropById(req.params.id);
    
    if (!crop) {
      return res.status(404).render('error', { 
        error: 'Crop not found',
        message: 'The crop you are looking for does not exist.'
      });
    }
    
    res.render('crops/show', { crop });
  } catch (error) {
    console.error('Error getting crop:', error);
    res.render('error', { 
      error: 'Error loading crop',
      message: error.message
    });
  }
});

// Get crops by category
router.get('/category/:category', (req, res) => {
  try {
    const { category } = req.params;
    const allCrops = getCrops();
    const crops = allCrops.filter(crop => crop.category.toLowerCase() === category.toLowerCase());
    
    res.render('crops/category', { 
      crops,
      category
    });
  } catch (error) {
    console.error('Error getting crops by category:', error);
    res.render('crops/category', { 
      crops: [],
      category: req.params.category,
      error: 'Error loading crops: ' + error.message
    });
  }
});

// Get new crop form
router.get('/new', isAuthenticated, (req, res) => {
  res.render('crops/new');
});

// Create new crop
router.post('/', isAuthenticated, (req, res) => {
  try {
    const { 
      name, 
      description, 
      price, 
      unit, 
      quantity, 
      category,
      imageUrl,
      location
    } = req.body;
    
    const newCrop = addCrop({
      name,
      description,
      price: parseFloat(price),
      unit,
      quantity: parseInt(quantity),
      category,
      imageUrl: imageUrl || '',
      location
    });
    
    res.redirect(`/crops/${newCrop.id}`);
  } catch (error) {
    console.error('Error creating crop:', error);
    res.render('crops/new', { 
      error: 'Error creating crop: ' + error.message,
      formData: req.body
    });
  }
});

// Get edit crop form
router.get('/:id/edit', isAuthenticated, (req, res) => {
  try {
    const crop = getCropById(req.params.id);
    
    if (!crop) {
      return res.status(404).render('error', { 
        error: 'Crop not found',
        message: 'The crop you are trying to edit does not exist.'
      });
    }
    
    res.render('crops/edit', { crop });
  } catch (error) {
    console.error('Error getting crop for edit:', error);
    res.render('error', { 
      error: 'Error loading crop',
      message: error.message
    });
  }
});

// Update crop
router.post('/:id', isAuthenticated, (req, res) => {
  try {
    const { 
      name, 
      description, 
      price, 
      unit, 
      quantity, 
      category,
      imageUrl,
      location
    } = req.body;
    
    const updatedCrop = updateCrop(req.params.id, {
      name,
      description,
      price: parseFloat(price),
      unit,
      quantity: parseInt(quantity),
      category,
      imageUrl: imageUrl || '',
      location
    });
    
    res.redirect(`/crops/${updatedCrop.id}`);
  } catch (error) {
    console.error('Error updating crop:', error);
    res.render('crops/edit', { 
      crop: { ...req.body, id: req.params.id },
      error: 'Error updating crop: ' + error.message
    });
  }
});

// Delete crop
router.post('/:id/delete', isAuthenticated, (req, res) => {
  try {
    deleteCrop(req.params.id);
    res.redirect('/crops');
  } catch (error) {
    console.error('Error deleting crop:', error);
    res.render('error', { 
      error: 'Error deleting crop',
      message: error.message
    });
  }
});

// Get my crops
router.get('/user/my-crops', isAuthenticated, (req, res) => {
  try {
    const crops = getCropsByUser(req.user.uid);
    res.render('crops/my-crops', { crops });
  } catch (error) {
    console.error('Error getting my crops:', error);
    res.render('crops/my-crops', { 
      crops: [],
      error: 'Error loading your crops: ' + error.message
    });
  }
});

export default router;
