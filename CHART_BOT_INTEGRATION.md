# Chart Bot Integration

This document describes the integration of the AI Chart Bot into the Sustainable Farming application.

## Overview

The Chart Bot is an AI-powered agricultural assistant that provides:
- Agricultural advice and recommendations
- Data visualization and chart generation
- Interactive chat interface
- Persistent chat history and saved charts

## Architecture

### Components

1. **Frontend Interface** (`src/views/chart-bot/index.ejs`)
   - <PERSON><PERSON> interface with message history
   - Chart visualization modal
   - Saved charts management
   - Quick actions and suggestions

2. **Backend Routes** (`src/routes/chartBot.js`)
   - `/chart-bot/` - Main chart bot page
   - `/chart-bot/api/chat` - Chat with AI bot
   - `/chart-bot/api/save-chart` - Save chart to database
   - `/chart-bot/api/charts` - Get user's saved charts
   - `/chart-bot/api/chat-history` - Get chat history
   - `/chart-bot/api/health` - Check bot service health

3. **Database Service** (`src/services/chartBotService.js`)
   - Chat history management
   - Chart storage and retrieval
   - User data persistence

4. **Frontend JavaScript** (`public/js/chart-bot.js`)
   - Chat interface interactions
   - Chart generation and display
   - API communication
   - Chart.js integration

5. **Styling** (`public/css/chart-bot.css`)
   - Chat interface styling
   - Responsive design
   - Dark mode support

### Database Schema

#### Collections

1. **chartBotChats**
   ```javascript
   {
     id: "auto-generated",
     userId: "user-uid",
     userMessage: "User's question",
     botResponse: "AI response",
     sources: [{ title: "Source title", content: "..." }],
     status: "success|fallback|error",
     createdAt: timestamp,
     timestamp: date
   }
   ```

2. **userCharts**
   ```javascript
   {
     id: "auto-generated",
     userId: "user-uid",
     title: "Chart title",
     description: "Chart description",
     chartData: { /* Chart.js data object */ },
     chartConfig: { /* Chart.js config object */ },
     chartType: "line|bar|pie|doughnut",
     createdAt: timestamp,
     updatedAt: timestamp
   }
   ```

## Integration with FastAPI Chart Bot

The Node.js application communicates with the FastAPI chart bot service running on `http://localhost:8000`.

### API Endpoints

- `POST /chat` - Send message to AI bot
- `GET /health` - Check service health
- `POST /add-knowledge` - Add knowledge to bot (admin)

### Fallback Handling

If the FastAPI service is unavailable, the application provides:
- Fallback responses to user queries
- Graceful error handling
- Status indicators showing service availability

## Features

### Chat Interface
- Real-time messaging with AI bot
- Message history persistence
- Source attribution for bot responses
- Typing indicators and loading states

### Chart Generation
- Automatic chart generation based on conversation context
- Multiple chart types (line, bar, pie, doughnut)
- Interactive Chart.js visualizations
- Chart saving and management

### Data Persistence
- Chat history stored in Firestore
- User charts saved with metadata
- Cross-session data persistence

### User Experience
- Responsive design for mobile and desktop
- Quick action buttons and suggestions
- Export functionality for chat history
- Integration with existing navigation

## Setup Instructions

### 1. Start the FastAPI Chart Bot Service

```bash
cd chart-bot
pip install -r requirements.txt
python app.py
```

The service will run on `http://localhost:8000`

### 2. Update Firestore Indexes

Deploy the updated Firestore indexes:

```bash
firebase deploy --only firestore:indexes
```

### 3. Start the Main Application

```bash
npm start
```

### 4. Access the Chart Bot

Navigate to `/chart-bot` in the application to access the AI assistant.

## Usage Examples

### Basic Chat
1. Navigate to the Chart Bot page
2. Type a question like "How do I treat tomato blight?"
3. Receive AI-powered agricultural advice

### Chart Generation
1. Ask for data visualization: "Show me corn price trends"
2. The system generates an appropriate chart
3. Save the chart for future reference

### Chart Management
1. View saved charts in the sidebar
2. Load, edit, or delete existing charts
3. Export chart data or chat history

## Configuration

### Environment Variables
- Chart bot service URL can be configured in the route handler
- API timeouts and retry logic can be adjusted

### Chart Types
The system supports various chart types:
- Line charts for trends and time series
- Bar charts for comparisons
- Pie/Doughnut charts for distributions
- Custom chart configurations

## Error Handling

### Service Unavailable
- Fallback responses when FastAPI service is down
- Clear status indicators for users
- Graceful degradation of functionality

### Database Errors
- Retry logic for Firestore operations
- Error messages for failed operations
- Data validation and sanitization

## Security Considerations

### Authentication
- All routes require user authentication
- User data isolation in database queries
- Secure API communication

### Data Validation
- Input sanitization for chat messages
- Chart data validation before saving
- XSS prevention in message display

## Future Enhancements

### Planned Features
1. **Advanced Chart Types**
   - Heatmaps for geographic data
   - Scatter plots for correlations
   - Multi-axis charts for complex data

2. **Enhanced AI Capabilities**
   - Image analysis for crop diseases
   - Weather integration for recommendations
   - Market trend predictions

3. **Collaboration Features**
   - Share charts with other users
   - Collaborative chat sessions
   - Community knowledge base

4. **Mobile App Integration**
   - Native mobile chart viewing
   - Offline chat capability
   - Push notifications for insights

### Technical Improvements
1. **Performance Optimization**
   - Chart data caching
   - Lazy loading for large datasets
   - WebSocket for real-time updates

2. **Analytics and Monitoring**
   - Usage analytics for bot interactions
   - Performance monitoring
   - Error tracking and alerting

## Troubleshooting

### Common Issues

1. **Chart Bot Service Not Available**
   - Check if FastAPI service is running on port 8000
   - Verify network connectivity
   - Check service logs for errors

2. **Charts Not Saving**
   - Verify Firestore permissions
   - Check browser console for errors
   - Ensure user is authenticated

3. **Chat History Not Loading**
   - Check Firestore indexes are deployed
   - Verify collection permissions
   - Check for JavaScript errors

### Debug Mode
Enable debug logging by setting `console.log` statements in:
- `chart-bot.js` for frontend debugging
- `chartBotService.js` for backend debugging
- Route handlers for API debugging

## Support

For issues or questions regarding the Chart Bot integration:
1. Check the browser console for JavaScript errors
2. Review server logs for backend issues
3. Verify FastAPI service is running and accessible
4. Check Firestore rules and indexes are properly configured
