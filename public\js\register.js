document.addEventListener('DOMContentLoaded', function() {
  const passwordInput = document.getElementById('password');
  const confirmPasswordInput = document.getElementById('confirmPassword');
  const togglePasswordBtn = document.getElementById('togglePassword');
  const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPassword');
  const passwordMatchFeedback = document.getElementById('passwordMatchFeedback');
  const registrationForm = document.getElementById('registrationForm');
  const termsCheckbox = document.getElementById('termsAgreement');
  const agreeTermsBtn = document.getElementById('agreeTerms');

  // Toggle password visibility
  if (togglePasswordBtn) {
    togglePasswordBtn.addEventListener('click', function() {
      const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
      passwordInput.setAttribute('type', type);
      this.querySelector('i').classList.toggle('bi-eye');
      this.querySelector('i').classList.toggle('bi-eye-slash');
    });
  }

  // Toggle confirm password visibility
  if (toggleConfirmPasswordBtn) {
    toggleConfirmPasswordBtn.addEventListener('click', function() {
      const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
      confirmPasswordInput.setAttribute('type', type);
      this.querySelector('i').classList.toggle('bi-eye');
      this.querySelector('i').classList.toggle('bi-eye-slash');
    });
  }

  // Check if passwords match
  function checkPasswordsMatch() {
    if (passwordInput.value !== confirmPasswordInput.value) {
      confirmPasswordInput.classList.add('is-invalid');
      passwordMatchFeedback.style.display = 'block';
      return false;
    } else {
      confirmPasswordInput.classList.remove('is-invalid');
      passwordMatchFeedback.style.display = 'none';
      return true;
    }
  }

  // Add event listeners for password validation
  if (confirmPasswordInput) {
    confirmPasswordInput.addEventListener('input', checkPasswordsMatch);
    passwordInput.addEventListener('input', function() {
      if (confirmPasswordInput.value) {
        checkPasswordsMatch();
      }
    });
  }

  // Handle form submission
  if (registrationForm) {
    registrationForm.addEventListener('submit', async function(event) {
      event.preventDefault();

      // Check if passwords match
      if (!checkPasswordsMatch()) {
        return;
      }

      // Check if terms are agreed to
      if (!termsCheckbox.checked) {
        showNotification('You must agree to the Terms and Conditions to register.', 'error');
        return;
      }

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const firstName = document.getElementById('firstName').value;
      const lastName = document.getElementById('lastName').value;
      const location = document.getElementById('location').value;
      const farmName = document.getElementById('farmName')?.value || '';

      const submitButton = this.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.innerHTML;

      // Show loading state
      submitButton.disabled = true;
      submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating account...';

      try {
        // Check if Firebase client is available
        if (typeof window.firebaseRegister === 'function') {
          // Use Firebase client-side authentication
          const user = await window.firebaseRegister(email, password);
          console.log('Firebase registration successful:', user.email);

          // Update user profile with additional information
          if (window.firebaseAuth && window.firebaseAuth.currentUser) {
            const { updateProfile } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            await updateProfile(window.firebaseAuth.currentUser, {
              displayName: `${firstName} ${lastName}`
            });
          }

          // Show success message
          showNotification('Registration successful! Redirecting to login...', 'success');

          // Redirect to login page after a short delay
          setTimeout(() => {
            window.location.href = '/auth/login?registered=true';
          }, 2000);
        } else {
          // Fallback to server-side authentication
          console.log('Firebase client not available, using server-side authentication');
          this.submit();
        }
      } catch (error) {
        console.error('Registration error:', error);

        // Handle specific Firebase errors
        let errorMessage = 'Registration failed. Please try again.';

        if (error.code === 'auth/email-already-in-use') {
          errorMessage = 'This email is already in use. Please use a different email or try logging in.';
        } else if (error.code === 'auth/weak-password') {
          errorMessage = 'Password is too weak. Please use a stronger password (at least 6 characters).';
        } else if (error.code === 'auth/invalid-email') {
          errorMessage = 'Please enter a valid email address.';
        } else if (error.code === 'auth/network-request-failed') {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message) {
          errorMessage = error.message;
        }

        showNotification(errorMessage, 'error');

        // Reset button
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
      }
    });
  }

  // Handle "I Agree" button in terms modal
  if (agreeTermsBtn) {
    agreeTermsBtn.addEventListener('click', function() {
      termsCheckbox.checked = true;
    });
  }
});

// Notification function
function showNotification(message, type = 'info') {
  // Remove existing notifications
  const existingNotifications = document.querySelectorAll('.notification-toast');
  existingNotifications.forEach(notification => notification.remove());

  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification-toast alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
  notification.style.cssText = `
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
  `;

  const icon = type === 'success' ? 'check-circle-fill' :
               type === 'error' ? 'exclamation-triangle-fill' :
               'info-circle-fill';

  notification.innerHTML = `
    <div class="d-flex align-items-center">
      <i class="bi bi-${icon} me-2"></i>
      <span>${message}</span>
      <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
    </div>
  `;

  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}

// Add CSS animations if not already added
if (!document.querySelector('#register-animations')) {
  const style = document.createElement('style');
  style.id = 'register-animations';
  style.textContent = `
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes slideOutRight {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);
}
