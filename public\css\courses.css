/* Modern Course Page Styles */

:root {
  --course-primary: #4CAF50;
  --course-primary-dark: #388E3C;
  --course-primary-light: #8BC34A;
  --course-secondary: #2196F3;
  --course-warning: #FF9800;
  --course-danger: #F44336;
  --course-success: #4CAF50;
  --course-light: #F8F9FA;
  --course-dark: #212529;
  --course-border: #E0E0E0;
  --course-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --course-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
  --course-radius: 12px;
  --course-transition: all 0.3s ease;
}

/* Course Page Layout */
.course-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Course Header */
.course-header {
  background: linear-gradient(135deg, var(--course-primary) 0%, var(--course-primary-dark) 100%);
  color: white;
  border-radius: var(--course-radius);
  padding: 40px;
  margin-bottom: 30px;
  box-shadow: var(--course-shadow);
}

.course-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.course-header .lead {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 25px;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.course-meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.course-meta-item i {
  font-size: 1.1rem;
}

/* Course Cards */
.course-card {
  background: white;
  border-radius: var(--course-radius);
  box-shadow: var(--course-shadow);
  transition: var(--course-transition);
  overflow: hidden;
  height: 100%;
  border: 1px solid var(--course-border);
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--course-shadow-hover);
}

.course-card-image {
  height: 200px;
  background: linear-gradient(135deg, var(--course-primary-light) 0%, var(--course-primary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.course-card-image i {
  font-size: 4rem;
  color: white;
  opacity: 0.8;
}

.course-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-card-body {
  padding: 25px;
}

.course-card-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--course-dark);
}

.course-card-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.course-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 0.9rem;
  color: #777;
}

.course-card-footer {
  padding: 20px 25px;
  background: var(--course-light);
  border-top: 1px solid var(--course-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Course Badges */
.course-badge {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.course-badge.beginner {
  background: var(--course-success);
  color: white;
}

.course-badge.intermediate {
  background: var(--course-warning);
  color: white;
}

.course-badge.advanced {
  background: var(--course-danger);
  color: white;
}

/* Course Buttons */
.course-btn {
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: var(--course-transition);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.course-btn-primary {
  background: var(--course-primary);
  color: white;
}

.course-btn-primary:hover {
  background: var(--course-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
  color: white;
  text-decoration: none;
}

.course-btn-outline {
  background: transparent;
  color: var(--course-primary);
  border: 2px solid var(--course-primary);
}

.course-btn-outline:hover {
  background: var(--course-primary);
  color: white;
  text-decoration: none;
}

/* Progress Bar */
.course-progress {
  background: #E0E0E0;
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
  margin-bottom: 10px;
}

.course-progress-bar {
  background: linear-gradient(90deg, var(--course-primary) 0%, var(--course-primary-light) 100%);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

/* Module Accordion */
.course-modules {
  background: white;
  border-radius: var(--course-radius);
  box-shadow: var(--course-shadow);
  overflow: hidden;
}

.course-module-item {
  border-bottom: 1px solid var(--course-border);
}

.course-module-item:last-child {
  border-bottom: none;
}

.course-module-header {
  padding: 20px 25px;
  background: white;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: var(--course-transition);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-module-header:hover {
  background: var(--course-light);
}

.course-module-title {
  font-weight: 600;
  color: var(--course-dark);
  margin: 0;
}

.course-module-duration {
  background: var(--course-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.course-module-content {
  padding: 0 25px 20px;
  color: #666;
  line-height: 1.6;
}

/* Category Cards */
.category-card {
  background: white;
  border-radius: var(--course-radius);
  padding: 30px;
  text-align: center;
  transition: var(--course-transition);
  box-shadow: var(--course-shadow);
  border: 1px solid var(--course-border);
  height: 100%;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--course-shadow-hover);
  text-decoration: none;
}

.category-card i {
  font-size: 3rem;
  margin-bottom: 20px;
  color: var(--course-primary);
}

.category-card h5 {
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--course-dark);
}

.category-card p {
  color: #666;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .course-container {
    padding: 15px;
  }

  .course-header {
    padding: 25px;
    text-align: center;
  }

  .course-header h1 {
    font-size: 2rem;
  }

  .course-meta {
    justify-content: center;
  }

  .course-card-body {
    padding: 20px;
  }

  .course-card-footer {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }
}

/* Loading States */
.course-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px;
}

.course-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--course-border);
  border-top: 4px solid var(--course-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Alert Styles */
.course-alert {
  padding: 20px;
  border-radius: var(--course-radius);
  margin-bottom: 20px;
  border: 1px solid transparent;
}

.course-alert-info {
  background: #E3F2FD;
  border-color: #2196F3;
  color: #1976D2;
}

.course-alert-warning {
  background: #FFF3E0;
  border-color: #FF9800;
  color: #F57C00;
}

.course-alert-success {
  background: #E8F5E8;
  border-color: var(--course-success);
  color: var(--course-primary-dark);
}

/* JavaScript Toggle Animation */
.course-module-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.course-module-header i.bi-chevron-down {
  transition: transform 0.3s ease;
}

.course-module-header.active i.bi-chevron-down {
  transform: rotate(180deg);
}

/* Course Page Specific Styles */
.course-show-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

/* Enhanced Module Hover Effects */
.course-module-header:hover {
  background: #f0f2f5 !important;
  transform: translateY(-1px);
}

.course-module-header:active {
  transform: translateY(0);
}
