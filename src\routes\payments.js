import express from 'express';
import { 
  createPayment, 
  updatePaymentStatus, 
  getPaymentById, 
  getUserPayments,
  createEnrollmentRequest,
  updateEnrollmentStatus,
  getEnrollmentByCourse,
  processStripePayment,
  hasAccessToPremiumCourse,
  PAYMENT_STATUS,
  ENROLLMENT_STATUS
} from '../services/paymentService.js';
import { getCourseById } from '../services/courseService.js';
import { isAuthenticated, isAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create payment for premium course
router.post('/create', isAuthenticated, async (req, res) => {
  try {
    const { courseId } = req.body;
    
    if (!courseId) {
      return res.status(400).json({
        success: false,
        message: 'Course ID is required'
      });
    }

    // Get course details
    const course = await getCourseById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    if (!course.isPremium) {
      return res.status(400).json({
        success: false,
        message: 'This course is not a premium course'
      });
    }

    // Check if user already has an enrollment
    const existingEnrollment = await getEnrollmentByCourse(courseId);
    if (existingEnrollment) {
      return res.status(400).json({
        success: false,
        message: 'You have already enrolled in this course',
        enrollment: existingEnrollment
      });
    }

    // Create payment record
    const payment = await createPayment(courseId, course.price, course.currency);
    
    res.json({
      success: true,
      payment,
      course: {
        id: course.id,
        title: course.title,
        price: course.price,
        currency: course.currency
      }
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating payment: ' + error.message
    });
  }
});

// Process payment with Stripe
router.post('/process', isAuthenticated, async (req, res) => {
  try {
    const { paymentId, paymentMethodId } = req.body;
    
    if (!paymentId || !paymentMethodId) {
      return res.status(400).json({
        success: false,
        message: 'Payment ID and payment method ID are required'
      });
    }

    // Get payment details
    const payment = await getPaymentById(paymentId);
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    if (payment.userId !== req.user.uid) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to payment'
      });
    }

    if (payment.status !== PAYMENT_STATUS.PENDING) {
      return res.status(400).json({
        success: false,
        message: 'Payment has already been processed'
      });
    }

    // Process payment with Stripe
    const result = await processStripePayment(paymentId, paymentMethodId);
    
    if (result.success) {
      // Create enrollment request
      const enrollment = await createEnrollmentRequest(payment.courseId, paymentId);
      
      res.json({
        success: true,
        message: 'Payment successful! Your enrollment is pending admin approval.',
        payment: await getPaymentById(paymentId),
        enrollment
      });
    }
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({
      success: false,
      message: 'Payment failed: ' + error.message
    });
  }
});

// Get user's payments
router.get('/my-payments', isAuthenticated, async (req, res) => {
  try {
    const payments = await getUserPayments();
    
    // Get course details for each payment
    const paymentsWithCourses = await Promise.all(
      payments.map(async (payment) => {
        const course = await getCourseById(payment.courseId);
        return {
          ...payment,
          course: course ? {
            id: course.id,
            title: course.title,
            imageUrl: course.imageUrl
          } : null
        };
      })
    );
    
    res.json({
      success: true,
      payments: paymentsWithCourses
    });
  } catch (error) {
    console.error('Error getting user payments:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting payments: ' + error.message
    });
  }
});

// Get payment details
router.get('/:paymentId', isAuthenticated, async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    const payment = await getPaymentById(paymentId);
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    if (payment.userId !== req.user.uid) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to payment'
      });
    }

    // Get course details
    const course = await getCourseById(payment.courseId);
    
    res.json({
      success: true,
      payment: {
        ...payment,
        course: course ? {
          id: course.id,
          title: course.title,
          imageUrl: course.imageUrl
        } : null
      }
    });
  } catch (error) {
    console.error('Error getting payment:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting payment: ' + error.message
    });
  }
});

// Check course access
router.get('/access/:courseId', isAuthenticated, async (req, res) => {
  try {
    const { courseId } = req.params;
    
    const hasAccess = await hasAccessToPremiumCourse(courseId);
    const enrollment = await getEnrollmentByCourse(courseId);
    
    res.json({
      success: true,
      hasAccess,
      enrollment
    });
  } catch (error) {
    console.error('Error checking course access:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking access: ' + error.message
    });
  }
});

// Admin routes
router.get('/admin/pending-enrollments', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { getPendingEnrollments } = await import('../services/paymentService.js');
    const enrollments = await getPendingEnrollments();
    
    // Get course and payment details for each enrollment
    const enrollmentsWithDetails = await Promise.all(
      enrollments.map(async (enrollment) => {
        const course = await getCourseById(enrollment.courseId);
        const payment = await getPaymentById(enrollment.paymentId);
        
        return {
          ...enrollment,
          course: course ? {
            id: course.id,
            title: course.title,
            imageUrl: course.imageUrl,
            price: course.price,
            currency: course.currency
          } : null,
          payment: payment ? {
            id: payment.id,
            amount: payment.amount,
            currency: payment.currency,
            status: payment.status,
            transactionId: payment.transactionId
          } : null
        };
      })
    );
    
    res.json({
      success: true,
      enrollments: enrollmentsWithDetails
    });
  } catch (error) {
    console.error('Error getting pending enrollments:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting pending enrollments: ' + error.message
    });
  }
});

// Admin approve/reject enrollment
router.post('/admin/enrollment/:enrollmentId/:action', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { enrollmentId, action } = req.params;
    const { reason } = req.body;
    
    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be approve or reject'
      });
    }

    const adminData = {
      [`${action}dBy`]: req.user.uid,
      [`${action}dByName`]: req.user.displayName || req.user.email
    };

    if (action === 'reject' && reason) {
      adminData.rejectionReason = reason;
    }

    const newStatus = action === 'approve' ? ENROLLMENT_STATUS.APPROVED : ENROLLMENT_STATUS.REJECTED;
    
    await updateEnrollmentStatus(enrollmentId, newStatus, adminData);
    
    res.json({
      success: true,
      message: `Enrollment ${action}d successfully`
    });
  } catch (error) {
    console.error(`Error ${req.params.action}ing enrollment:`, error);
    res.status(500).json({
      success: false,
      message: `Error ${req.params.action}ing enrollment: ` + error.message
    });
  }
});

export default router;
