/* Network Styles */

/* Post Card */
.post-card {
  transition: all 0.2s ease;
}

.post-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Like, Comment, Share Buttons */
.post-card .btn-link {
  color: var(--theme-text-muted, #666666);
  text-decoration: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.post-card .btn-link:hover {
  background-color: var(--theme-hover, rgba(76, 175, 80, 0.1));
  color: var(--theme-primary, #4CAF50);
}

.post-card .btn-link i {
  margin-right: 0.25rem;
}

/* Comments Section */
.comments-section {
  max-height: 300px;
  overflow-y: auto;
}

/* Profile Cover and Photo */
.profile-cover {
  position: relative;
  background-color: #f8f9fa;
  background-size: cover;
  background-position: center;
  height: 200px;
}

/* Connection Item */
.connection-item {
  transition: all 0.2s ease;
}

.connection-item:hover {
  background-color: var(--theme-hover, rgba(76, 175, 80, 0.05));
}

/* Conversation Item */
.conversation-item {
  transition: all 0.2s ease;
}

.conversation-item:hover {
  background-color: var(--theme-hover, rgba(76, 175, 80, 0.05));
}

.conversation-item.active {
  background-color: var(--theme-hover, rgba(76, 175, 80, 0.1));
  border-left: 3px solid var(--theme-primary, #4CAF50);
}

/* Message Bubbles */
.message-bubble .bg-success {
  background-color: var(--theme-primary, #4CAF50) !important;
}

/* Search Results */
.search-result-card {
  transition: all 0.2s ease;
}

.search-result-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Badges */
.badge.bg-light {
  background-color: #f8f9fa !important;
  color: #212529 !important;
  border: 1px solid #dee2e6;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.post-card, .comment {
  animation: fadeIn 0.3s ease-out;
}

/* Message Content Area */
.message-content {
  height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
  padding: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .profile-cover {
    height: 150px;
  }

  .message-content {
    height: 300px;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
