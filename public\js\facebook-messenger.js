/**
 * Facebook Messenger Style Enhancements
 * Adds interactive styling and animations to the messaging interface
 */

document.addEventListener('DOMContentLoaded', function() {
  // Elements
  const conversationItems = document.querySelectorAll('.conversation-item');
  const messagesContainer = document.getElementById('messages-container');
  const messageInput = document.getElementById('message-input');
  const sendButton = document.getElementById('send-message-btn');
  const chatFooterIcons = document.querySelectorAll('.chat-footer-icon');
  const headerIcons = document.querySelectorAll('.chat-header-icon, .conversation-list-icon');
  const infoToggleBtn = document.getElementById('info-toggle-btn');
  const infoPanel = document.querySelector('.info-panel');
  const backButton = document.querySelector('.chat-back-button');
  
  // Toggle info panel
  if (infoToggleBtn && infoPanel) {
    infoToggleBtn.addEventListener('click', function() {
      infoPanel.classList.toggle('hidden');
    });
  }
  
  // Mobile back button
  if (backButton) {
    backButton.addEventListener('click', function() {
      const conversationPanel = document.querySelector('.conversation-list-panel');
      if (conversationPanel) {
        conversationPanel.classList.remove('hidden');
      }
    });
  }
  
  // Apply hover effects to conversation items
  conversationItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      if (!this.classList.contains('active')) {
        this.style.backgroundColor = 'var(--fb-hover-background)';
      }
    });
    
    item.addEventListener('mouseleave', function() {
      if (!this.classList.contains('active')) {
        this.style.backgroundColor = '';
      }
    });
  });
  
  // Message input enhancements
  if (messageInput) {
    // Focus effect
    messageInput.addEventListener('focus', function() {
      this.parentElement.style.boxShadow = '0 0 0 2px var(--fb-blue)';
    });
    
    messageInput.addEventListener('blur', function() {
      this.parentElement.style.boxShadow = 'none';
    });
    
    // Show/hide send button based on input
    messageInput.addEventListener('input', function() {
      if (sendButton) {
        if (this.value.trim() !== '') {
          sendButton.classList.add('active');
        } else {
          sendButton.classList.remove('active');
        }
      }
      
      // Show typing indicator (simulated)
      showTypingIndicator();
    });
  }
  
  // Simulate typing indicator
  function showTypingIndicator() {
    if (!messagesContainer) return;
    
    // Remove existing typing indicator
    const existingIndicator = document.querySelector('.typing-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }
    
    // Only show typing indicator sometimes (simulated)
    if (Math.random() > 0.7) {
      const typingIndicator = document.createElement('div');
      typingIndicator.className = 'typing-indicator message incoming';
      typingIndicator.innerHTML = `
        <div class="message-bubble">
          <div class="typing-dots">
            <span></span><span></span><span></span>
          </div>
        </div>
      `;
      
      messagesContainer.appendChild(typingIndicator);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
      
      // Remove after a few seconds
      setTimeout(() => {
        if (typingIndicator.parentNode) {
          typingIndicator.classList.add('fade-out');
          setTimeout(() => {
            if (typingIndicator.parentNode) {
              typingIndicator.parentNode.removeChild(typingIndicator);
            }
          }, 300);
        }
      }, 2000);
    }
  }
  
  // Add hover effects to footer icons
  chatFooterIcons.forEach(icon => {
    icon.addEventListener('mouseenter', function() {
      this.style.backgroundColor = 'var(--fb-hover-background)';
    });
    
    icon.addEventListener('mouseleave', function() {
      this.style.backgroundColor = 'var(--fb-panel-background)';
    });
  });
  
  // Add hover effects to header icons
  headerIcons.forEach(icon => {
    icon.addEventListener('mouseenter', function() {
      this.style.backgroundColor = 'var(--fb-hover-background)';
    });
    
    icon.addEventListener('mouseleave', function() {
      this.style.backgroundColor = 'var(--fb-panel-background)';
    });
  });
  
  // Scroll to bottom of messages container
  if (messagesContainer) {
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // Add message reaction functionality
    setupMessageReactions();
  }
  
  // Setup message reactions
  function setupMessageReactions() {
    const messageBubbles = document.querySelectorAll('.message-bubble');
    
    messageBubbles.forEach(bubble => {
      // Double click to show reaction picker
      bubble.addEventListener('dblclick', function(e) {
        e.preventDefault();
        addReaction(this, '❤️');
      });
      
      // Long press to show reaction picker (mobile)
      let pressTimer;
      
      bubble.addEventListener('touchstart', function(e) {
        pressTimer = setTimeout(() => {
          addReaction(this, '❤️');
        }, 500);
      });
      
      bubble.addEventListener('touchend', function() {
        clearTimeout(pressTimer);
      });
    });
  }
  
  // Add reaction to message
  function addReaction(messageBubble, reaction) {
    // Check if reaction already exists
    let reactionContainer = messageBubble.querySelector('.message-reactions');
    
    if (!reactionContainer) {
      // Create reaction container
      reactionContainer = document.createElement('div');
      reactionContainer.className = 'message-reactions';
      messageBubble.appendChild(reactionContainer);
    }
    
    // Check if this specific reaction already exists
    const existingReaction = reactionContainer.querySelector(`[data-reaction="${reaction}"]`);
    
    if (existingReaction) {
      // Increment count
      const countEl = existingReaction.querySelector('.reaction-count');
      let count = parseInt(countEl.textContent);
      countEl.textContent = count + 1;
    } else {
      // Add new reaction
      const reactionEl = document.createElement('div');
      reactionEl.className = 'message-reaction';
      reactionEl.setAttribute('data-reaction', reaction);
      reactionEl.innerHTML = `
        <span class="reaction-emoji">${reaction}</span>
        <span class="reaction-count">1</span>
      `;
      reactionContainer.appendChild(reactionEl);
    }
    
    // Show animation
    const newReaction = document.createElement('div');
    newReaction.className = 'reaction-animation';
    newReaction.textContent = reaction;
    newReaction.style.left = `${Math.random() * 80 + 10}%`;
    messageBubble.appendChild(newReaction);
    
    setTimeout(() => {
      newReaction.remove();
    }, 1000);
  }
  
  // Handle mobile view
  function handleMobileView() {
    const conversationPanel = document.querySelector('.conversation-list-panel');
    const chatPanel = document.querySelector('.chat-panel');
    const backButton = document.querySelector('.chat-back-button');
    
    if (window.innerWidth <= 768 && conversationPanel && chatPanel) {
      if (window.location.pathname.includes('/messaging/') &&
          !window.location.pathname.endsWith('/messaging/')) {
        // Show chat panel and hide conversation panel on mobile when in a conversation
        conversationPanel.classList.add('hidden');
        if (backButton) backButton.style.display = 'block';
      } else {
        // Show conversation panel and hide chat panel on mobile when not in a conversation
        conversationPanel.classList.remove('hidden');
        if (backButton) backButton.style.display = 'none';
      }
    } else {
      // Reset for desktop view
      if (conversationPanel) conversationPanel.classList.remove('hidden');
      if (backButton) backButton.style.display = 'none';
    }
  }
  
  // Call on load and on resize
  handleMobileView();
  window.addEventListener('resize', handleMobileView);
  
  // Add emoji picker functionality
  const emojiButton = document.querySelector('.emoji-btn');
  if (emojiButton && messageInput) {
    emojiButton.addEventListener('click', function() {
      // Common emojis
      const emojis = ['😊', '👍', '❤️', '🙏', '👋', '🔥', '😂', '🎉', '👏', '🌱', '🌿', '🍀', '🌾', '🌻', '🌽'];
      
      // Create emoji picker
      let emojiPicker = document.querySelector('.emoji-picker');
      
      if (!emojiPicker) {
        emojiPicker = document.createElement('div');
        emojiPicker.className = 'emoji-picker';
        
        // Add emojis
        emojis.forEach(emoji => {
          const emojiSpan = document.createElement('span');
          emojiSpan.textContent = emoji;
          emojiSpan.addEventListener('click', function() {
            messageInput.value += emoji;
            messageInput.focus();
            
            // Trigger input event to show send button
            const inputEvent = new Event('input');
            messageInput.dispatchEvent(inputEvent);
          });
          
          emojiPicker.appendChild(emojiSpan);
        });
        
        // Add to DOM
        const chatFooter = document.querySelector('.chat-footer');
        if (chatFooter) {
          chatFooter.appendChild(emojiPicker);
        }
      }
      
      // Toggle visibility
      emojiPicker.classList.toggle('show');
    });
    
    // Close emoji picker when clicking outside
    document.addEventListener('click', function(e) {
      const emojiPicker = document.querySelector('.emoji-picker');
      if (emojiPicker && !emojiPicker.contains(e.target) && e.target !== emojiButton) {
        emojiPicker.classList.remove('show');
      }
    });
  }
});
