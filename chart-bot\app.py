from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging
from rag_system import AgriculturalRAGSystem
from knowledge_loader import AgriculturalKnowledgeLoader

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Agricultural Chatbot with RAG",
    description="An AI-powered agricultural advisor using Retrieval-Augmented Generation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
rag_system = None
knowledge_loader = None

# Pydantic models
class ChatRequest(BaseModel):
    query: str
    max_sources: Optional[int] = 5

class ChatResponse(BaseModel):
    query: str
    response: str
    sources: List[Dict[str, Any]]
    status: str
    error: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    message: str

@app.on_event("startup")
async def startup_event():
    """Initialize the RAG system and load knowledge base on startup"""
    global rag_system, knowledge_loader
    
    try:
        logger.info("Initializing Agricultural RAG System...")
        
        # Initialize components
        rag_system = AgriculturalRAGSystem()
        knowledge_loader = AgriculturalKnowledgeLoader()
        
        # Load sample agricultural data
        logger.info("Loading agricultural knowledge base...")
        documents = knowledge_loader.load_sample_agricultural_data()
        
        # Chunk documents if needed
        chunked_docs = knowledge_loader.chunk_documents(documents)
        
        # Add documents to vector database
        rag_system.add_documents(chunked_docs)
        
        logger.info("Agricultural RAG System initialized successfully!")
        
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        raise e

@app.get("/", response_model=HealthResponse)
async def root():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        message="Agricultural Chatbot API is running"
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Detailed health check"""
    try:
        if rag_system is None:
            return HealthResponse(
                status="unhealthy",
                message="RAG system not initialized"
            )
        
        # Test a simple query to ensure everything is working
        test_result = rag_system.chat("What is crop rotation?")
        
        if test_result['status'] == 'success':
            return HealthResponse(
                status="healthy",
                message="All systems operational"
            )
        else:
            return HealthResponse(
                status="degraded",
                message="RAG system responding but with errors"
            )
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            message=f"System error: {str(e)}"
        )

@app.post("/chat", response_model=ChatResponse)
async def chat_with_bot(request: ChatRequest):
    """Main chat endpoint for agricultural questions"""
    try:
        if rag_system is None:
            raise HTTPException(
                status_code=503,
                detail="RAG system not initialized"
            )
        
        # Process the query
        result = rag_system.chat(request.query)
        
        # Limit sources if requested
        if request.max_sources and len(result['sources']) > request.max_sources:
            result['sources'] = result['sources'][:request.max_sources]
        
        return ChatResponse(**result)
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing query: {str(e)}"
        )

@app.get("/categories")
async def get_categories():
    """Get available agricultural categories"""
    from config import AGRICULTURAL_CATEGORIES
    return {
        "categories": AGRICULTURAL_CATEGORIES,
        "description": "Available agricultural knowledge categories"
    }

@app.get("/sample-questions")
async def get_sample_questions():
    """Get sample questions for testing"""
    sample_questions = [
        "How do I treat tomato blight disease?",
        "What are the best methods for controlling aphids?",
        "How do I test and adjust soil pH?",
        "What organic fertilizers should I use for vegetables?",
        "How can I set up efficient irrigation for my crops?",
        "What is crop rotation and why is it important?",
        "How do I identify and treat plant diseases?",
        "What are the signs of nutrient deficiency in plants?",
        "How can I improve soil health naturally?",
        "What are the best practices for organic farming?"
    ]
    
    return {
        "sample_questions": sample_questions,
        "description": "Sample questions to test the agricultural chatbot"
    }

@app.post("/add-knowledge")
async def add_knowledge(documents: List[Dict[str, Any]]):
    """Add new documents to the knowledge base"""
    try:
        if rag_system is None:
            raise HTTPException(
                status_code=503,
                detail="RAG system not initialized"
            )
        
        # Validate document format
        for doc in documents:
            if 'content' not in doc:
                raise HTTPException(
                    status_code=400,
                    detail="Each document must have 'content' field"
                )
        
        # Add documents to the system
        rag_system.add_documents(documents)
        
        return {
            "status": "success",
            "message": f"Added {len(documents)} documents to knowledge base",
            "documents_added": len(documents)
        }
        
    except Exception as e:
        logger.error(f"Error adding knowledge: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error adding documents: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
