<div class="container">
  <div class="row">
    <div class="col-md-12">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/">Home</a></li>
          <li class="breadcrumb-item"><a href="/courses">Courses</a></li>
          <li class="breadcrumb-item active" aria-current="page"><%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></li>
        </ol>
      </nav>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <h1 class="mb-4"><%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %> Courses</h1>
      
      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Category Description -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card bg-light">
        <div class="card-body">
          <% if (category === 'organic-farming') { %>
            <h5 class="card-title"><i class="bi bi-flower1 text-success me-2"></i>About Organic Farming</h5>
            <p class="card-text">Organic farming is a method of crop and livestock production that involves much more than choosing not to use pesticides, fertilizers, genetically modified organisms, antibiotics, and growth hormones. Our courses will guide you through the principles and practices of organic farming.</p>
          <% } else if (category === 'water-conservation') { %>
            <h5 class="card-title"><i class="bi bi-droplet text-primary me-2"></i>About Water Conservation</h5>
            <p class="card-text">Water conservation in agriculture is crucial for sustainable farming. These courses will teach you effective methods to reduce water usage, implement efficient irrigation systems, and manage water resources responsibly.</p>
          <% } else if (category === 'renewable-energy') { %>
            <h5 class="card-title"><i class="bi bi-sun text-warning me-2"></i>About Renewable Energy</h5>
            <p class="card-text">Renewable energy offers farmers opportunities to reduce costs, increase self-sufficiency, and minimize environmental impact. Learn how to integrate solar, wind, and other renewable energy sources into your farming operations.</p>
          <% } else if (category === 'soil-health') { %>
            <h5 class="card-title"><i class="bi bi-layers text-success me-2"></i>About Soil Health</h5>
            <p class="card-text">Healthy soil is the foundation of productive farming. These courses cover soil composition, testing, improvement techniques, and sustainable management practices to enhance your farm's productivity and sustainability.</p>
          <% } else { %>
            <h5 class="card-title">About <%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></h5>
            <p class="card-text">Explore our courses on <%= category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %> to enhance your sustainable farming knowledge and skills.</p>
          <% } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Courses List -->
  <div class="row">
    <% if (courses && courses.length > 0) { %>
      <% courses.forEach(course => { %>
        <div class="col-md-6 mb-4">
          <div class="card h-100 shadow-sm">
            <div class="row g-0">
              <div class="col-md-4">
                <% if (course.imageUrl) { %>
                  <img src="<%= course.imageUrl %>" class="img-fluid rounded-start h-100" alt="<%= course.title %>" style="object-fit: cover;">
                <% } else { %>
                  <div class="bg-light text-center d-flex align-items-center justify-content-center h-100 rounded-start">
                    <% if (category === 'organic-farming') { %>
                      <i class="bi bi-flower1 fs-1 text-success"></i>
                    <% } else if (category === 'water-conservation') { %>
                      <i class="bi bi-droplet fs-1 text-primary"></i>
                    <% } else if (category === 'renewable-energy') { %>
                      <i class="bi bi-sun fs-1 text-warning"></i>
                    <% } else if (category === 'soil-health') { %>
                      <i class="bi bi-layers fs-1 text-success"></i>
                    <% } else { %>
                      <i class="bi bi-book fs-1 text-muted"></i>
                    <% } %>
                  </div>
                <% } %>
              </div>
              <div class="col-md-8">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title mb-0"><%= course.title %></h5>
                    <span class="badge bg-<%= course.level === 'beginner' ? 'success' : (course.level === 'intermediate' ? 'warning' : 'danger') %>">
                      <%= course.level.charAt(0).toUpperCase() + course.level.slice(1) %>
                    </span>
                  </div>
                  <p class="card-text text-muted small mb-2">
                    <i class="bi bi-person-circle"></i> <%= course.authorName %>
                    <span class="mx-1">•</span>
                    <i class="bi bi-clock"></i> <%= Math.round(course.duration / 60) %> hours
                    <span class="mx-1">•</span>
                    <i class="bi bi-collection"></i> <%= course.moduleCount %> modules
                  </p>
                  <p class="card-text small"><%= course.description.length > 100 ? course.description.substring(0, 100) + '...' : course.description %></p>
                  <div class="d-flex justify-content-between align-items-center mt-2">
                    <span class="text-muted small">
                      <i class="bi bi-people"></i> <%= course.enrollmentCount %> enrolled
                    </span>
                    <a href="/courses/<%= course.id %>" class="btn btn-sm btn-success">View Course</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% }); %>
    <% } else { %>
      <div class="col-md-12">
        <div class="alert alert-info" role="alert">
          No courses available in this category yet. Check back soon!
        </div>
      </div>
    <% } %>
  </div>

  <div class="row mt-4">
    <div class="col-md-12">
      <a href="/courses" class="btn btn-outline-success">
        <i class="bi bi-arrow-left"></i> Back to All Courses
      </a>
    </div>
  </div>
</div>
