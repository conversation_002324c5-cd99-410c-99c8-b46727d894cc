<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title><%= typeof title !== 'undefined' ? title : 'Sustainable Farming' %></title>

  <!-- Meta Tags -->
  <meta name="description" content="Sustainable Farming application for farmers to connect, share knowledge, and access resources">
  <meta name="keywords" content="sustainable farming, agriculture, farmers network, farming techniques, crop management">
  <meta name="author" content="Sustainable Farming Team">

  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#689F38">
  <meta name="msapplication-TileColor" content="#8BC34A">
  <meta name="msapplication-TileImage" content="/images/icons/icon-144x144.png">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="SustainFarm">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="application-name" content="SustainFarm">

  <!-- Web App Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- Favicon and App Icons -->
  <link rel="icon" href="/img/favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/icon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/images/icons/icon-16x16.png">
  <link rel="mask-icon" href="/images/icons/safari-pinned-tab.svg" color="#689F38">

  <!-- Stylesheets -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/css/footer.css">
  <link rel="stylesheet" href="/css/weather-widget.css">
  <link rel="stylesheet" href="/css/fixed-header.css">
  <link rel="stylesheet" href="/css/facebook-profile.css">
  <link rel="stylesheet" href="/css/facebook-dashboard.css">
  <link rel="stylesheet" href="/css/network.css">
  <link rel="stylesheet" href="/css/messaging.css">
  <link rel="stylesheet" href="/css/weather.css">
  <link rel="stylesheet" href="/css/chart-bot.css">
  <link rel="stylesheet" href="/css/toast-notifications.css">
  <link rel="stylesheet" href="/css/consistent-theme.css">
  <link rel="stylesheet" href="/css/modern-design.css">
  <link rel="stylesheet" href="/css/market-trends.css">
  <link rel="stylesheet" href="/css/courses.css">
  <link rel="stylesheet" href="/css/enhanced-design-system.css">
  <link rel="stylesheet" href="/css/pwa.css">

  <!-- Enhanced Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body class="fb-body<% if (typeof bodyClass !== 'undefined') { %> <%= bodyClass %><% } %>">
  <%
    // Set current path for header navigation
    const currentPath = typeof originalUrl !== 'undefined' ? originalUrl : '';

    // Determine if this is a page that should not have a header
    const isLoginPage = currentPath.includes('/auth/login');
    const isRegisterPage = currentPath.includes('/auth/register');
    const isHomePage = currentPath === '/';
    const isForgotPasswordPage = currentPath.includes('/auth/forgot-password');
    const shouldShowHeader = !(isLoginPage || isRegisterPage || isHomePage || isForgotPasswordPage);

    // Only show header for authenticated users and non-auth/login/register pages
    if (isAuthenticated && shouldShowHeader) {
      // Include the Facebook-style header for authenticated users
  %>
    <%- include('./partials/facebook-header', { currentPath: currentPath }) %>
  <% } else if (shouldShowHeader) { %>
    <!-- Simple header for non-authenticated users on pages that should have a header -->
    <header class="fb-header">
      <!-- Left side elements -->
      <div class="fb-header-left">
        <!-- Logo -->
        <div class="fb-header-logo">
          <i class="bi bi-flower3 me-2" style="font-size: 24px; color: #F5DEB3;"></i>
          <h5 class="text-white mb-0">Sustainable Farming</h5>
        </div>
      </div>

      <!-- Right side elements -->
      <div class="fb-header-right">
        <a href="/auth/login" class="fb-nav-link">
          <span>Login</span>
        </a>
        <a href="/auth/register" class="fb-nav-link">
          <span>Register</span>
        </a>

        <!-- Weather Widget -->
        <div class="fb-weather-widget" id="weather-widget">
          <%- include('./partials/weather-widget') %>
        </div>
      </div>
    </header>
  <% } %>

  <div class="container <%= shouldShowHeader ? 'mt-5 pt-4' : 'mt-3' %>">
    <%- body %>
  </div>

  <footer class="text-center py-3" style="background-color: var(--theme-primary, #4CAF50); color: var(--theme-text-light, white);">
    <div class="container">
      <p>Sustainable Farming &copy; 2025</p>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/facebook-dashboard.js"></script>
  <script src="/js/weather-widget.js"></script>

  <!-- PWA Scripts -->
  <script src="/js/pwa.js"></script>

  <!-- Enhanced UI JavaScript -->
  <script src="/js/enhanced-ui.js"></script>

  <!-- PWA Installation and Offline Support -->
  <script>
    // Check if app is running in standalone mode
    if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
      document.body.classList.add('pwa-standalone');
    }

    // Add PWA-specific styles
    const pwaStyles = document.createElement('style');
    pwaStyles.textContent = `
      .pwa-standalone {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
      }

      @media (display-mode: standalone) {
        body {
          user-select: none;
          -webkit-user-select: none;
          -webkit-touch-callout: none;
        }

        .container {
          max-width: 100%;
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
        }
      }

      /* PWA Loading Animation */
      .pwa-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #8BC34A 0%, #689F38 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }

      .pwa-loading.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .pwa-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(pwaStyles);

    // Show loading screen for PWA
    if (window.matchMedia('(display-mode: standalone)').matches) {
      const loadingScreen = document.createElement('div');
      loadingScreen.className = 'pwa-loading';
      loadingScreen.innerHTML = '<div class="pwa-spinner"></div>';
      document.body.appendChild(loadingScreen);

      window.addEventListener('load', () => {
        setTimeout(() => {
          loadingScreen.classList.add('hidden');
          setTimeout(() => loadingScreen.remove(), 500);
        }, 1000);
      });
    }
  </script>
</body>
</html>
