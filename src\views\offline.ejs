<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Sustainable Farming</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #8BC34A 0%, #689F38 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .offline-features {
            margin-top: 2rem;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        .feature-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .connection-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .status-online {
            background: rgba(76, 175, 80, 0.3);
        }

        .status-offline {
            background: rgba(244, 67, 54, 0.3);
        }

        @media (max-width: 768px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-message {
                font-size: 1rem;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">🌱</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            Don't worry! You can still access some features of Sustainable Farming while offline.
            We'll sync your data when you're back online.
        </p>

        <div class="connection-status" id="connectionStatus">
            <span id="statusText">Checking connection...</span>
        </div>

        <button class="retry-button" onclick="retryConnection()">
            🔄 Try Again
        </button>
        
        <button class="retry-button" onclick="goHome()">
            🏠 Go Home
        </button>

        <div class="offline-features">
            <h3 style="margin-bottom: 1rem;">Available Offline:</h3>
            <div class="feature-item">
                <span class="feature-icon">📖</span>
                <span>View cached farming guides</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">📝</span>
                <span>Draft posts and messages</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">📊</span>
                <span>View saved market data</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🔄</span>
                <span>Auto-sync when online</span>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusText.textContent = '✅ Connection restored! You can refresh the page.';
            } else {
                statusElement.className = 'connection-status status-offline';
                statusText.textContent = '❌ No internet connection detected.';
            }
        }

        // Retry connection
        function retryConnection() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                alert('Still no internet connection. Please check your network settings.');
            }
        }

        // Go to home page
        function goHome() {
            window.location.href = '/';
        }

        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Auto-retry when connection is restored
        window.addEventListener('online', () => {
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.reload();
                }
            }, 1000);
        });
    </script>
</body>
</html>
