import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import * as messagingService from '../services/messagingService.js';

const router = express.Router();

// Messages home page - Conversation list
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Get conversations for the user
    const conversations = await messagingService.getUserConversations();

    res.render('messaging/index', {
      user: req.user,
      userData: req.userData || {},
      title: 'Messages',
      conversations: conversations || [],
      activeConversation: null
    });
  } catch (error) {
    console.error('Error rendering messages home:', error);

    // Check if this is a permission error
    if (error.code === 'permission-denied' || error.message.includes('Missing or insufficient permissions')) {
      res.render('messaging/index', {
        user: req.user,
        userData: req.userData || {},
        title: 'Messages',
        conversations: [],
        activeConversation: null,
        indexError: true,
        errorMessage: 'Database permissions are being configured. Please try again in a few minutes or contact support.'
      });
    }
    // Check if this is an index error
    else if (error.message && error.message.includes('requires an index')) {
      res.render('messaging/index', {
        user: req.user,
        userData: req.userData || {},
        title: 'Messages',
        conversations: [],
        activeConversation: null,
        indexError: true,
        errorMessage: 'The database index is still being built. Please try again in a few minutes.'
      });
    } else {
      // For other errors, render the messaging page with empty data instead of error page
      res.render('messaging/index', {
        user: req.user,
        userData: req.userData || {},
        title: 'Messages',
        conversations: [],
        activeConversation: null,
        indexError: true,
        errorMessage: 'Unable to load messages at the moment. Please try again later.'
      });
    }
  }
});

// Specific conversation page
router.get('/:conversationId', isAuthenticated, (req, res) => {
  try {
    const { conversationId } = req.params;

    // For now, just render a basic conversation page
    // Later we'll fetch actual conversation data
    res.render('messaging/conversation', {
      user: req.user,
      userData: req.userData,
      title: 'Conversation',
      conversations: [], // Empty array for now
      activeConversation: {
        id: conversationId,
        name: 'Conversation',
        participants: [],
        lastMessage: null,
        unreadCount: 0
      },
      messages: [] // Empty array for now
    });
  } catch (error) {
    console.error('Error viewing conversation:', error);
    res.render('error', {
      error: 'Error loading conversation',
      message: error.message,
      user: req.user,
      userData: req.userData
    });
  }
});

// API endpoints for messaging functionality
// These will be implemented later

// Get conversations
router.get('/api/conversations', isAuthenticated, async (req, res) => {
  try {
    // Get conversations for the user
    const conversations = await messagingService.getUserConversations();

    res.json({ success: true, data: conversations });
  } catch (error) {
    console.error('Error getting conversations:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get messages for a conversation
router.get('/api/conversations/:conversationId/messages', isAuthenticated, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { limit, lastMessageId } = req.query;

    if (!conversationId) {
      return res.status(400).json({ success: false, message: 'Conversation ID is required' });
    }

    // Get messages for the conversation
    const result = await messagingService.getConversationMessages(
      conversationId,
      limit ? parseInt(limit) : 20,
      lastMessageId || null
    );

    res.json({ success: true, data: result });
  } catch (error) {
    console.error('Error getting messages:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Send message
router.post('/api/conversations/:conversationId/messages', isAuthenticated, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { text, attachmentFile } = req.body;

    if (!conversationId) {
      return res.status(400).json({ success: false, message: 'Conversation ID is required' });
    }

    if (!text && !attachmentFile) {
      return res.status(400).json({ success: false, message: 'Message text or attachment is required' });
    }

    // Send the message
    const result = await messagingService.sendMessage(conversationId, text, attachmentFile);

    res.json({ success: true, message: 'Message sent successfully', data: result });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Create new conversation
router.post('/api/conversations', isAuthenticated, async (req, res) => {
  try {
    const { recipientId } = req.body;

    if (!recipientId) {
      return res.status(400).json({ success: false, message: 'Recipient ID is required' });
    }

    // Create or get the conversation
    const conversation = await messagingService.getOrCreateConversation(recipientId);

    res.json({ success: true, data: conversation });
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Mark conversation as read
router.post('/api/conversations/:conversationId/read', isAuthenticated, async (req, res) => {
  try {
    const { conversationId } = req.params;

    if (!conversationId) {
      return res.status(400).json({ success: false, message: 'Conversation ID is required' });
    }

    // Mark messages as read
    const result = await messagingService.markMessagesAsRead(conversationId);

    res.json({ success: true, message: 'Messages marked as read', data: result });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

export default router;
