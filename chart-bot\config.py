import os
from dotenv import load_dotenv

load_dotenv()

# API Keys
GEMINI_API_KEY = "AIzaSyAkmatFMGGakhDn8wrpcscGMFdXzqsQEy8"
PINECONE_API_KEY = "pcsk_35nGRN_J1YMt94G5k6nbtQyo6Cuvip2xdTboPAQZVtkCeVmR11Q6DAuUAkrAUocVaATWec"

# Pinecone Configuration
PINECONE_ENVIRONMENT = "gcp-starter"  # Default for free tier
PINECONE_INDEX_NAME = "agricultural-knowledge"
PINECONE_DIMENSION = 384  # For sentence-transformers/all-MiniLM-L6-v2

# Gemini Configuration
GEMINI_MODEL = "gemini-1.5-flash"

# Application Configuration
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200
MAX_TOKENS = 1000
TEMPERATURE = 0.7

# Agricultural Categories
AGRICULTURAL_CATEGORIES = [
    "crop_diseases",
    "pest_management", 
    "soil_management",
    "fertilizers",
    "irrigation",
    "weather_patterns",
    "harvesting",
    "seed_selection",
    "organic_farming",
    "livestock"
]
