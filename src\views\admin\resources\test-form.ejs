<div class="container">
  <div class="row">
    <div class="col-md-12">
      <h1 class="mb-4">Test Resource Creation</h1>
      
      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>
      
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0">Simple Resource Form</h4>
        </div>
        <div class="card-body">
          <form action="/admin/resources" method="POST">
            <div class="mb-3">
              <label for="title" class="form-label">Title</label>
              <input type="text" class="form-control" id="title" name="title" value="Test Resource" required>
            </div>
            
            <div class="mb-3">
              <label for="category" class="form-label">Category</label>
              <select class="form-select" id="category" name="category" required>
                <option value="organic-farming">Organic Farming</option>
                <option value="water-conservation">Water Conservation</option>
                <option value="renewable-energy">Renewable Energy</option>
                <option value="soil-health">Soil Health</option>
                <option value="other" selected>Other</option>
              </select>
            </div>
            
            <div class="mb-3">
              <label for="summary" class="form-label">Summary</label>
              <textarea class="form-control" id="summary" name="summary" rows="2" required>This is a test resource summary.</textarea>
            </div>
            
            <div class="mb-3">
              <label for="content" class="form-label">Content</label>
              <textarea class="form-control" id="content" name="content" rows="5" required>This is the content of the test resource.</textarea>
            </div>
            
            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="isPublished" name="isPublished" checked>
                <label class="form-check-label" for="isPublished">
                  Publish immediately
                </label>
              </div>
            </div>
            
            <button type="submit" class="btn btn-primary">Create Test Resource</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
