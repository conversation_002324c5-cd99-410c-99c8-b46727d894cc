import express from 'express';
import * as weatherService from '../services/weatherService.js';
import { isAuthenticated } from '../middleware/auth.js';

const router = express.Router();

// Weather page
router.get('/', isAuthenticated, async (req, res) => {
  try {
    res.render('weather/index', {
      title: 'Weather Forecast',
      user: req.user,
      userData: req.userData,
      isAuthenticated: true,
      originalUrl: req.originalUrl,
      bodyClass: 'weather-page'
    });
  } catch (error) {
    console.error('Error rendering weather page:', error);
    res.render('weather/index', {
      title: 'Weather Forecast',
      user: req.user,
      userData: req.userData,
      isAuthenticated: true,
      originalUrl: req.originalUrl,
      bodyClass: 'weather-page',
      error: 'Failed to load weather page. Please try again later.'
    });
  }
});



// Public API endpoint for the weather widget by coordinates
router.get('/api/widget', async (req, res) => {
  try {
    const { lat, lon } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const weatherData = await weatherService.getCurrentWeather(lat, lon);
    const formattedData = weatherService.formatWeatherData(weatherData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching weather for widget:', error);
    res.status(500).json({ error: 'Failed to fetch weather data' });
  }
});

// Public API endpoint for the weather widget by city
router.get('/api/widget/city', async (req, res) => {
  try {
    const { city } = req.query;

    if (!city) {
      return res.status(400).json({ error: 'City name is required' });
    }

    const weatherData = await weatherService.getWeatherByCity(city);
    const formattedData = weatherService.formatWeatherData(weatherData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching weather for widget by city:', error);
    res.status(500).json({ error: 'Failed to fetch weather data for this city' });
  }
});

// API endpoint to get current weather by coordinates
router.get('/api/current', isAuthenticated, async (req, res) => {
  try {
    const { lat, lon } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const weatherData = await weatherService.getCurrentWeather(lat, lon);
    const formattedData = weatherService.formatWeatherData(weatherData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching current weather:', error);
    res.status(500).json({ error: 'Failed to fetch weather data' });
  }
});

// API endpoint to get weather forecast by coordinates
router.get('/api/forecast', isAuthenticated, async (req, res) => {
  try {
    const { lat, lon, days } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const forecastData = await weatherService.getWeatherForecast(lat, lon, days || 5);
    const formattedData = weatherService.formatForecastData(forecastData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching weather forecast:', error);
    res.status(500).json({ error: 'Failed to fetch forecast data' });
  }
});

// API endpoint to get current weather by city name
router.get('/api/city/current', isAuthenticated, async (req, res) => {
  try {
    const { city } = req.query;

    if (!city) {
      return res.status(400).json({ error: 'City name is required' });
    }

    const weatherData = await weatherService.getWeatherByCity(city);
    const formattedData = weatherService.formatWeatherData(weatherData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching weather by city:', error);
    res.status(500).json({ error: 'Failed to fetch weather data for this city' });
  }
});

// API endpoint to get weather forecast by city name
router.get('/api/city/forecast', isAuthenticated, async (req, res) => {
  try {
    const { city, days } = req.query;

    if (!city) {
      return res.status(400).json({ error: 'City name is required' });
    }

    const forecastData = await weatherService.getForecastByCity(city, days || 5);
    const formattedData = weatherService.formatForecastData(forecastData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching forecast by city:', error);
    res.status(500).json({ error: 'Failed to fetch forecast data for this city' });
  }
});

// API endpoint to get air quality data
router.get('/api/air-quality', isAuthenticated, async (req, res) => {
  try {
    const { lat, lon } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const airQualityData = await weatherService.getAirQuality(lat, lon);
    const formattedData = weatherService.formatAirQualityData(airQualityData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching air quality:', error);
    res.status(500).json({ error: 'Failed to fetch air quality data' });
  }
});

// API endpoint to get comprehensive weather data (current, forecast, alerts)
router.get('/api/onecall', isAuthenticated, async (req, res) => {
  try {
    const { lat, lon } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const oneCallData = await weatherService.getOneCallWeather(lat, lon);
    const formattedData = weatherService.formatOneCallData(oneCallData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching comprehensive weather data:', error);
    res.status(500).json({ error: 'Failed to fetch comprehensive weather data' });
  }
});

// Public API endpoint for the widget to get air quality
router.get('/api/widget/air-quality', async (req, res) => {
  try {
    const { lat, lon } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const airQualityData = await weatherService.getAirQuality(lat, lon);
    const formattedData = weatherService.formatAirQualityData(airQualityData);

    res.json(formattedData);
  } catch (error) {
    console.error('Error fetching air quality for widget:', error);
    res.status(500).json({ error: 'Failed to fetch air quality data' });
  }
});

export default router;
