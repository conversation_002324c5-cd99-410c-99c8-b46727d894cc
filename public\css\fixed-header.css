/* Fixed Header Styles */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: var(--theme-primary, #4CAF50) !important; /* Use theme variable with fallback */
}

/* Add padding to the body to prevent content from being hidden under the fixed header */
body {
  padding-top: 56px; /* Adjust this value based on your navbar height */
}

/* Adjust for larger screens */
@media (min-width: 992px) {
  body {
    padding-top: 60px; /* Slightly larger padding for bigger screens */
  }
}

/* Adjust for smaller screens */
@media (max-width: 576px) {
  body {
    padding-top: 54px; /* Slightly smaller padding for mobile */
  }
}
