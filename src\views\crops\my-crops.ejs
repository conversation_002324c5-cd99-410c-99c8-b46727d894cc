<div class="row">
  <div class="col-md-12">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/crops">Crops</a></li>
        <li class="breadcrumb-item active" aria-current="page">My Crops</li>
      </ol>
    </nav>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <h1 class="mb-4">My Crops for Sale</h1>
    
    <% if (typeof error !== 'undefined') { %>
      <div class="alert alert-danger" role="alert">
        <%= error %>
      </div>
    <% } %>
    
    <div class="mb-4">
      <a href="/crops/new" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Add New Crop
      </a>
    </div>
  </div>
</div>

<div class="row">
  <% if (crops && crops.length > 0) { %>
    <div class="col-md-12">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>Name</th>
              <th>Category</th>
              <th>Price</th>
              <th>Quantity</th>
              <th>Posted Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <% crops.forEach(crop => { %>
              <tr>
                <td>
                  <a href="/crops/<%= crop.id %>" class="text-decoration-none">
                    <%= crop.name %>
                  </a>
                </td>
                <td><span class="badge bg-secondary"><%= crop.category %></span></td>
                <td>$<%= crop.price.toFixed(2) %>/<%= crop.unit %></td>
                <td><%= crop.quantity %> <%= crop.unit %>(s)</td>
                <td><%= new Date(crop.createdAt).toLocaleDateString() %></td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <a href="/crops/<%= crop.id %>" class="btn btn-outline-secondary">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="/crops/<%= crop.id %>/edit" class="btn btn-outline-primary">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCropModal<%= crop.id %>">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                  
                  <!-- Delete Modal for each crop -->
                  <div class="modal fade" id="deleteCropModal<%= crop.id %>" tabindex="-1" aria-labelledby="deleteCropModalLabel<%= crop.id %>" aria-hidden="true">
                    <div class="modal-dialog">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h5 class="modal-title" id="deleteCropModalLabel<%= crop.id %>">Confirm Delete</h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                          <p>Are you sure you want to delete "<%= crop.name %>"? This action cannot be undone.</p>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                          <form action="/crops/<%= crop.id %>/delete" method="POST">
                            <button type="submit" class="btn btn-danger">Delete</button>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
  <% } else { %>
    <div class="col-md-12">
      <div class="alert alert-info" role="alert">
        You haven't added any crops for sale yet. Click the "Add New Crop" button to get started.
      </div>
    </div>
  <% } %>
</div>
