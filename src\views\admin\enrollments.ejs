<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <!-- Page Header -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 class="h3 mb-2">Premium Course Enrollments</h1>
          <p class="text-muted">Manage pending premium course enrollment requests</p>
        </div>
        <div>
          <span class="badge bg-warning text-dark fs-6" id="pending-count">
            <i class="bi bi-clock"></i>
            Loading...
          </span>
        </div>
      </div>

      <!-- Enrollment Requests -->
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
              <i class="bi bi-list-check me-2"></i>
              Pending Enrollment Requests
            </h5>
            <button class="btn btn-outline-primary btn-sm" onclick="refreshEnrollments()">
              <i class="bi bi-arrow-clockwise"></i>
              Refresh
            </button>
          </div>
        </div>
        <div class="card-body p-0">
          <div id="enrollments-container">
            <!-- Loading State -->
            <div class="text-center py-5" id="loading-state">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3 text-muted">Loading enrollment requests...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enrollment Action Modal -->
<div class="modal fade" id="enrollmentModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalTitle">Enrollment Action</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="modalContent">
          <!-- Content will be populated by JavaScript -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="confirmAction">Confirm</button>
      </div>
    </div>
  </div>
</div>

<style>
.enrollment-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.enrollment-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.enrollment-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
}

.enrollment-body {
  padding: 1rem;
}

.enrollment-actions {
  padding: 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 8px 8px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 1rem;
}

.course-thumbnail {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.payment-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.payment-status.completed {
  background: #d4edda;
  color: #155724;
}

.payment-status.pending {
  background: #fff3cd;
  color: #856404;
}

.payment-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.enrollment-meta {
  font-size: 0.875rem;
  color: #6c757d;
}

.action-buttons .btn {
  margin-right: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}
</style>

<script>
let enrollments = [];
let currentEnrollment = null;
let currentAction = null;

document.addEventListener('DOMContentLoaded', function() {
  loadEnrollments();
});

async function loadEnrollments() {
  try {
    const response = await fetch('/payments/admin/pending-enrollments');
    const result = await response.json();
    
    if (result.success) {
      enrollments = result.enrollments;
      renderEnrollments();
      updatePendingCount();
    } else {
      showError('Failed to load enrollments: ' + result.message);
    }
  } catch (error) {
    console.error('Error loading enrollments:', error);
    showError('Error loading enrollments: ' + error.message);
  }
}

function renderEnrollments() {
  const container = document.getElementById('enrollments-container');
  const loadingState = document.getElementById('loading-state');
  
  if (loadingState) {
    loadingState.style.display = 'none';
  }
  
  if (enrollments.length === 0) {
    container.innerHTML = `
      <div class="empty-state">
        <i class="bi bi-check-circle"></i>
        <h4>No Pending Enrollments</h4>
        <p>All enrollment requests have been processed.</p>
      </div>
    `;
    return;
  }
  
  const enrollmentsHtml = enrollments.map(enrollment => `
    <div class="enrollment-card">
      <div class="enrollment-header">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <div class="user-avatar">
              ${enrollment.userName ? enrollment.userName.charAt(0).toUpperCase() : 'U'}
            </div>
            <div>
              <h6 class="mb-1">${enrollment.userName || 'Unknown User'}</h6>
              <small class="text-muted">${enrollment.userEmail}</small>
            </div>
          </div>
          <div class="text-end">
            <div class="enrollment-meta">
              Requested: ${new Date(enrollment.requestedAt?.seconds * 1000 || Date.now()).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>
      
      <div class="enrollment-body">
        <div class="row align-items-center">
          <div class="col-md-2 text-center mb-3 mb-md-0">
            <div class="course-thumbnail">
              <i class="bi bi-book"></i>
            </div>
          </div>
          <div class="col-md-6">
            <h5 class="mb-2">
              ${enrollment.course?.title || 'Unknown Course'}
              <span class="badge bg-warning text-dark ms-2">
                <i class="bi bi-gem"></i> Premium
              </span>
            </h5>
            <div class="enrollment-meta">
              <div class="mb-1">
                <i class="bi bi-currency-dollar me-1"></i>
                Price: $${enrollment.course?.price || 0} ${enrollment.course?.currency || 'USD'}
              </div>
              <div class="mb-1">
                <i class="bi bi-credit-card me-1"></i>
                Payment: 
                <span class="payment-status ${enrollment.payment?.status || 'pending'}">
                  ${(enrollment.payment?.status || 'pending').toUpperCase()}
                </span>
              </div>
              ${enrollment.payment?.transactionId ? `
                <div class="mb-1">
                  <i class="bi bi-receipt me-1"></i>
                  Transaction: ${enrollment.payment.transactionId}
                </div>
              ` : ''}
            </div>
          </div>
          <div class="col-md-4">
            <div class="enrollment-actions">
              <div class="action-buttons">
                <button class="btn btn-success btn-sm" onclick="showApprovalModal('${enrollment.id}')">
                  <i class="bi bi-check-circle"></i>
                  Approve
                </button>
                <button class="btn btn-danger btn-sm" onclick="showRejectionModal('${enrollment.id}')">
                  <i class="bi bi-x-circle"></i>
                  Reject
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `).join('');
  
  container.innerHTML = enrollmentsHtml;
}

function updatePendingCount() {
  const countElement = document.getElementById('pending-count');
  const count = enrollments.length;
  
  countElement.innerHTML = `
    <i class="bi bi-clock"></i>
    ${count} Pending
  `;
  
  if (count === 0) {
    countElement.className = 'badge bg-success fs-6';
    countElement.innerHTML = '<i class="bi bi-check-circle"></i> All Processed';
  }
}

function showApprovalModal(enrollmentId) {
  currentEnrollment = enrollments.find(e => e.id === enrollmentId);
  currentAction = 'approve';
  
  if (!currentEnrollment) return;
  
  document.getElementById('modalTitle').textContent = 'Approve Enrollment';
  document.getElementById('modalContent').innerHTML = `
    <div class="alert alert-info">
      <i class="bi bi-info-circle me-2"></i>
      <strong>Approve enrollment for:</strong>
    </div>
    <div class="mb-3">
      <strong>Student:</strong> ${currentEnrollment.userName} (${currentEnrollment.userEmail})
    </div>
    <div class="mb-3">
      <strong>Course:</strong> ${currentEnrollment.course?.title}
    </div>
    <div class="mb-3">
      <strong>Payment Status:</strong> 
      <span class="payment-status ${currentEnrollment.payment?.status || 'pending'}">
        ${(currentEnrollment.payment?.status || 'pending').toUpperCase()}
      </span>
    </div>
    <p>The student will gain immediate access to the premium course content.</p>
  `;
  
  const confirmButton = document.getElementById('confirmAction');
  confirmButton.textContent = 'Approve Enrollment';
  confirmButton.className = 'btn btn-success';
  
  new bootstrap.Modal(document.getElementById('enrollmentModal')).show();
}

function showRejectionModal(enrollmentId) {
  currentEnrollment = enrollments.find(e => e.id === enrollmentId);
  currentAction = 'reject';
  
  if (!currentEnrollment) return;
  
  document.getElementById('modalTitle').textContent = 'Reject Enrollment';
  document.getElementById('modalContent').innerHTML = `
    <div class="alert alert-warning">
      <i class="bi bi-exclamation-triangle me-2"></i>
      <strong>Reject enrollment for:</strong>
    </div>
    <div class="mb-3">
      <strong>Student:</strong> ${currentEnrollment.userName} (${currentEnrollment.userEmail})
    </div>
    <div class="mb-3">
      <strong>Course:</strong> ${currentEnrollment.course?.title}
    </div>
    <div class="mb-3">
      <label for="rejectionReason" class="form-label">Reason for rejection:</label>
      <textarea class="form-control" id="rejectionReason" rows="3" placeholder="Please provide a reason for rejecting this enrollment..."></textarea>
    </div>
    <p class="text-muted">The student will be notified of the rejection and the reason provided.</p>
  `;
  
  const confirmButton = document.getElementById('confirmAction');
  confirmButton.textContent = 'Reject Enrollment';
  confirmButton.className = 'btn btn-danger';
  
  new bootstrap.Modal(document.getElementById('enrollmentModal')).show();
}

document.getElementById('confirmAction').addEventListener('click', async function() {
  if (!currentEnrollment || !currentAction) return;
  
  const button = this;
  const originalText = button.textContent;
  
  button.disabled = true;
  button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
  
  try {
    const requestBody = {};
    
    if (currentAction === 'reject') {
      const reason = document.getElementById('rejectionReason')?.value;
      if (reason) {
        requestBody.reason = reason;
      }
    }
    
    const response = await fetch(`/payments/admin/enrollment/${currentEnrollment.id}/${currentAction}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Show success notification
      if (window.showNotification) {
        window.showNotification(`Enrollment ${currentAction}d successfully!`, 'success');
      }
      
      // Close modal
      bootstrap.Modal.getInstance(document.getElementById('enrollmentModal')).hide();
      
      // Refresh enrollments
      await loadEnrollments();
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error(`Error ${currentAction}ing enrollment:`, error);
    
    if (window.showNotification) {
      window.showNotification(`Error ${currentAction}ing enrollment: ` + error.message, 'error');
    }
  } finally {
    button.disabled = false;
    button.textContent = originalText;
  }
});

async function refreshEnrollments() {
  const button = event.target.closest('button');
  const originalHtml = button.innerHTML;
  
  button.disabled = true;
  button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Refreshing...';
  
  try {
    await loadEnrollments();
    
    if (window.showNotification) {
      window.showNotification('Enrollments refreshed successfully!', 'success');
    }
  } catch (error) {
    if (window.showNotification) {
      window.showNotification('Error refreshing enrollments: ' + error.message, 'error');
    }
  } finally {
    button.disabled = false;
    button.innerHTML = originalHtml;
  }
}

function showError(message) {
  const container = document.getElementById('enrollments-container');
  container.innerHTML = `
    <div class="alert alert-danger m-3">
      <i class="bi bi-exclamation-triangle me-2"></i>
      ${message}
    </div>
  `;
}
</script>
