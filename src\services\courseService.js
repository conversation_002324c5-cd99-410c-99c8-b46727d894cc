import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '../config/initFirebase.js';
import { getCurrentUser } from './firebaseService.js';

// Collection names
const COURSES_COLLECTION = 'courses';
const MODULES_COLLECTION = 'modules';
const USERS_COLLECTION = 'users';
const PROGRESS_COLLECTION = 'courseProgress';

/**
 * Get all published courses
 */
export const getAllCourses = async () => {
  try {
    // Modified to get all courses regardless of published status
    const coursesQuery = query(
      collection(db, COURSES_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    const coursesSnapshot = await getDocs(coursesQuery);

    const courses = [];
    coursesSnapshot.forEach((doc) => {
      courses.push({
        id: doc.id,
        ...doc.data()
      });
    });

    console.log(`Retrieved ${courses.length} courses from Firestore:`,
      courses.map(c => `${c.title} (Published: ${c.isPublished})`));

    return courses;
  } catch (error) {
    console.error('Error getting courses:', error);

    // Return sample courses for development/demo
    return getSampleCourses();
  }
};

/**
 * Get courses by category
 */
export const getCoursesByCategory = async (category) => {
  try {
    const coursesQuery = query(
      collection(db, COURSES_COLLECTION),
      where('category', '==', category),
      where('isPublished', '==', true),
      orderBy('createdAt', 'desc')
    );

    const coursesSnapshot = await getDocs(coursesQuery);

    const courses = [];
    coursesSnapshot.forEach((doc) => {
      courses.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return courses;
  } catch (error) {
    console.error(`Error getting courses for category ${category}:`, error);

    // Return empty array if error occurs
    return [];
  }
};

/**
 * Get a single course by ID
 */
export const getCourseById = async (courseId) => {
  try {
    const courseDoc = await getDoc(doc(db, COURSES_COLLECTION, courseId));

    if (!courseDoc.exists()) {
      return null;
    }

    return {
      id: courseDoc.id,
      ...courseDoc.data()
    };
  } catch (error) {
    console.error(`Error getting course ${courseId}:`, error);

    // Return null if error occurs
    return null;
  }
};

/**
 * Get all modules for a course
 */
export const getCourseModules = async (courseId) => {
  try {
    const modulesQuery = query(
      collection(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION),
      where('isPublished', '==', true),
      orderBy('order', 'asc')
    );

    const modulesSnapshot = await getDocs(modulesQuery);

    const modules = [];
    modulesSnapshot.forEach((doc) => {
      modules.push({
        id: doc.id,
        courseId,
        ...doc.data()
      });
    });

    return modules;
  } catch (error) {
    console.error(`Error getting modules for course ${courseId}:`, error);

    // Return empty array if error occurs
    return [];
  }
};

/**
 * Get a single module by ID
 */
export const getModuleById = async (courseId, moduleId) => {
  try {
    const moduleDoc = await getDoc(
      doc(db, COURSES_COLLECTION, courseId, MODULES_COLLECTION, moduleId)
    );

    if (!moduleDoc.exists()) {
      return null;
    }

    return {
      id: moduleDoc.id,
      courseId,
      ...moduleDoc.data()
    };
  } catch (error) {
    console.error(`Error getting module ${moduleId}:`, error);

    // Return null if error occurs
    return null;
  }
};

/**
 * Enroll a user in a course
 */
export const enrollInCourse = async (courseId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to enroll in a course');
    }

    // Check if the course exists
    const course = await getCourseById(courseId);

    if (!course) {
      throw new Error('Course not found');
    }

    // Check if user is already enrolled
    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (progressDoc.exists()) {
      // User is already enrolled, just update the lastAccessedAt
      await updateDoc(progressRef, {
        lastAccessedAt: serverTimestamp()
      });

      return {
        courseId,
        userId: user.uid,
        ...progressDoc.data()
      };
    }

    // Get the first module of the course
    const modules = await getCourseModules(courseId);
    const firstModuleId = modules.length > 0 ? modules[0].id : null;

    // Create new progress document
    const progressData = {
      courseId,
      userId: user.uid,
      enrolledAt: serverTimestamp(),
      lastAccessedAt: serverTimestamp(),
      completedModules: [],
      currentModuleId: firstModuleId,
      progress: 0,
      isCompleted: false,
      notes: ''
    };

    await setDoc(progressRef, progressData);

    // Increment the enrollment count for the course
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    await updateDoc(courseRef, {
      enrollmentCount: increment(1)
    });

    return {
      courseId,
      userId: user.uid,
      ...progressData
    };
  } catch (error) {
    console.error(`Error enrolling in course ${courseId}:`, error);
    throw error;
  }
};

/**
 * Get user's progress for a course
 */
export const getCourseProgress = async (courseId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      return null;
    }

    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (!progressDoc.exists()) {
      return null;
    }

    return {
      courseId,
      userId: user.uid,
      ...progressDoc.data()
    };
  } catch (error) {
    console.error(`Error getting progress for course ${courseId}:`, error);
    return null;
  }
};

/**
 * Mark a module as completed
 */
export const completeModule = async (courseId, moduleId) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to track progress');
    }

    // Get the course and all modules
    const course = await getCourseById(courseId);
    const modules = await getCourseModules(courseId);

    if (!course || modules.length === 0) {
      throw new Error('Course or modules not found');
    }

    // Get current progress
    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (!progressDoc.exists()) {
      throw new Error('You are not enrolled in this course');
    }

    const progress = progressDoc.data();

    // Check if module is already completed
    if (progress.completedModules.includes(moduleId)) {
      return {
        courseId,
        userId: user.uid,
        ...progress
      };
    }

    // Add module to completed modules
    const completedModules = [...progress.completedModules, moduleId];

    // Calculate new progress percentage
    const progressPercentage = Math.round((completedModules.length / modules.length) * 100);

    // Determine if course is completed
    const isCompleted = progressPercentage === 100;

    // Find the next module
    let nextModuleId = null;
    if (!isCompleted) {
      const currentModuleIndex = modules.findIndex(m => m.id === moduleId);
      if (currentModuleIndex < modules.length - 1) {
        nextModuleId = modules[currentModuleIndex + 1].id;
      }
    }

    // Update progress
    const updateData = {
      completedModules,
      progress: progressPercentage,
      lastAccessedAt: serverTimestamp()
    };

    if (nextModuleId) {
      updateData.currentModuleId = nextModuleId;
    }

    if (isCompleted) {
      updateData.isCompleted = true;
      updateData.completedAt = serverTimestamp();
    }

    await updateDoc(progressRef, updateData);

    return {
      courseId,
      userId: user.uid,
      ...progress,
      ...updateData,
      completedModules
    };
  } catch (error) {
    console.error(`Error completing module ${moduleId}:`, error);
    throw error;
  }
};

/**
 * Get all courses a user is enrolled in
 */
export const getUserCourses = async () => {
  try {
    const user = getCurrentUser();

    if (!user) {
      return [];
    }

    const progressQuery = query(
      collection(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION),
      orderBy('lastAccessedAt', 'desc')
    );

    const progressSnapshot = await getDocs(progressQuery);

    const coursePromises = [];
    progressSnapshot.forEach((doc) => {
      const progress = doc.data();
      coursePromises.push(
        getCourseById(progress.courseId).then(course => ({
          ...course,
          progress: progress.progress,
          isCompleted: progress.isCompleted,
          currentModuleId: progress.currentModuleId,
          lastAccessedAt: progress.lastAccessedAt
        }))
      );
    });

    const courses = await Promise.all(coursePromises);
    return courses.filter(course => course !== null);
  } catch (error) {
    console.error('Error getting user courses:', error);
    return [];
  }
};

/**
 * Save user notes for a course
 */
export const saveUserNotes = async (courseId, notes) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to save notes');
    }

    const progressRef = doc(db, USERS_COLLECTION, user.uid, PROGRESS_COLLECTION, courseId);
    const progressDoc = await getDoc(progressRef);

    if (!progressDoc.exists()) {
      throw new Error('You are not enrolled in this course');
    }

    await updateDoc(progressRef, {
      notes,
      lastAccessedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error(`Error saving notes for course ${courseId}:`, error);
    throw error;
  }
};



