<div class="container">
  <div class="row">
    <div class="col-md-12">
      <h1 class="mb-4">Weather Forecast</h1>

      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert">
          <%= error %>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Weather Search -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-body p-4">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="citySearch" class="form-label fw-bold">Search by City</label>
                <div class="input-group">
                  <input type="text" class="form-control bg-dark text-white border-dark" id="citySearch" placeholder="Enter city name (e.g., London, Tokyo, New York)">
                  <button class="btn" style="background-color: var(--theme-primary, #4CAF50); color: white;" type="button" id="searchCityBtn">
                    <i class="bi bi-search"></i> Search
                  </button>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-bold">Use Your Location</label>
                <button class="btn w-100" style="background-color: var(--weather-dark-card); color: var(--weather-text-primary); border: 1px solid rgba(255, 255, 255, 0.2);" id="getCurrentLocationBtn">
                  <i class="bi bi-geo-alt"></i> Get Current Location
                </button>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="form-text text-muted">
                <i class="bi bi-info-circle"></i> Search for any city worldwide or use your current location to get weather information.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Weather Links -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="d-flex flex-wrap gap-2">
        <button class="weather-map-btn" data-city="London">London</button>
        <button class="weather-map-btn" data-city="New York">New York</button>
        <button class="weather-map-btn" data-city="Tokyo">Tokyo</button>
        <button class="weather-map-btn" data-city="Sydney">Sydney</button>
        <button class="weather-map-btn" data-city="Paris">Paris</button>
        <button class="weather-map-btn" data-city="Cairo">Cairo</button>
        <button class="weather-map-btn" data-city="Mumbai">Mumbai</button>
        <button class="weather-map-btn" data-city="Rio de Janeiro">Rio</button>
      </div>
    </div>
  </div>

  <!-- Current Weather -->
  <div class="row mb-4" id="currentWeatherContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="weather-header">
          <div class="weather-header-content">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="d-flex align-items-center mb-2">
                  <h2 id="locationName" class="weather-location mb-0 me-2">--</h2>
                  <span id="locationCountry" class="badge bg-light text-dark">--</span>
                </div>
                <p id="currentDate" class="weather-date">--</p>
                <div class="d-flex align-items-center">
                  <img id="weatherIcon" src="https://openweathermap.org/img/wn/<EMAIL>" alt="Weather icon" class="weather-icon me-3">
                  <div>
                    <h1 id="currentTemp" class="weather-temp">--°C</h1>
                    <p id="weatherDescription" class="weather-description mb-1">--</p>
                    <p class="weather-feels-like">Feels like <span id="feelsLike">--</span>°C</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="row mt-md-0 mt-4">
                  <div class="col-6 col-md-3 mb-3">
                    <div class="weather-detail-card">
                      <i class="bi bi-droplet weather-detail-icon"></i>
                      <div class="weather-detail-value" id="humidity">--%</div>
                      <div class="weather-detail-label">Humidity</div>
                    </div>
                  </div>
                  <div class="col-6 col-md-3 mb-3">
                    <div class="weather-detail-card">
                      <i class="bi bi-wind weather-detail-icon"></i>
                      <div class="weather-detail-value" id="windSpeed">-- m/s</div>
                      <div class="weather-detail-label">Wind</div>
                    </div>
                  </div>
                  <div class="col-6 col-md-3 mb-3">
                    <div class="weather-detail-card">
                      <i class="bi bi-sunrise weather-detail-icon"></i>
                      <div class="weather-detail-value" id="sunrise">--:--</div>
                      <div class="weather-detail-label">Sunrise</div>
                    </div>
                  </div>
                  <div class="col-6 col-md-3 mb-3">
                    <div class="weather-detail-card">
                      <i class="bi bi-sunset weather-detail-icon"></i>
                      <div class="weather-detail-value" id="sunset">--:--</div>
                      <div class="weather-detail-label">Sunset</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="row g-0">
            <div class="col-md-3 col-6 border-end border-bottom p-3 text-center">
              <div class="weather-detail-label">Pressure</div>
              <div class="weather-detail-value" id="pressure">-- hPa</div>
            </div>
            <div class="col-md-3 col-6 border-bottom p-3 text-center">
              <div class="weather-detail-label">Visibility</div>
              <div class="weather-detail-value" id="visibility">-- km</div>
            </div>
            <div class="col-md-3 col-6 border-end p-3 text-center">
              <div class="weather-detail-label">UV Index</div>
              <div class="weather-detail-value" id="uvIndex">--</div>
            </div>
            <div class="col-md-3 col-6 p-3 text-center">
              <div class="weather-detail-label">Cloudiness</div>
              <div class="weather-detail-value" id="cloudiness">--%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Weather Forecast -->
  <div class="row mb-4" id="forecastContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3 bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">5-Day Forecast</h4>
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar3 me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <span class="text-muted">Daily prediction</span>
            </div>
          </div>
        </div>
        <div class="card-body p-3">
          <div class="row" id="forecastRow">
            <!-- Forecast cards will be inserted here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Air Quality Section -->
  <div class="row mb-4" id="airQualityContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3 bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Air Quality</h4>
            <div class="d-flex align-items-center">
              <i class="bi bi-wind me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <span class="text-muted">Current conditions</span>
            </div>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="row">
            <div class="col-lg-4 col-md-5">
              <div class="text-center mb-4">
                <div id="aqiGauge" class="aqi-gauge">
                  <div class="aqi-value">--</div>
                  <div class="aqi-label">--</div>
                </div>
                <div class="mt-3">
                  <h5 class="mb-2">Air Quality Index</h5>
                  <p class="text-muted">The Air Quality Index (AQI) indicates how clean or polluted the air is, and what associated health effects might be a concern.</p>
                </div>
              </div>
            </div>
            <div class="col-lg-8 col-md-7">
              <h5 class="mb-3">Air Quality Components</h5>
              <div class="row" id="aqiComponentsRow">
                <div class="col-lg-3 col-md-6 col-6 mb-3">
                  <div class="aqi-component-card">
                    <h6 class="aqi-component-title">PM2.5</h6>
                    <p id="pm25" class="aqi-component-value mb-0">-- μg/m³</p>
                    <small class="aqi-component-desc">Fine particles</small>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6 col-6 mb-3">
                  <div class="aqi-component-card">
                    <h6 class="aqi-component-title">PM10</h6>
                    <p id="pm10" class="aqi-component-value mb-0">-- μg/m³</p>
                    <small class="aqi-component-desc">Coarse particles</small>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6 col-6 mb-3">
                  <div class="aqi-component-card">
                    <h6 class="aqi-component-title">O₃</h6>
                    <p id="o3" class="aqi-component-value mb-0">-- μg/m³</p>
                    <small class="aqi-component-desc">Ozone</small>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6 col-6 mb-3">
                  <div class="aqi-component-card">
                    <h6 class="aqi-component-title">NO₂</h6>
                    <p id="no2" class="aqi-component-value mb-0">-- μg/m³</p>
                    <small class="aqi-component-desc">Nitrogen dioxide</small>
                  </div>
                </div>
              </div>
              <div class="mt-3">
                <div class="card bg-light border-0 p-3">
                  <div class="d-flex">
                    <i class="bi bi-info-circle-fill me-2" style="color: var(--theme-primary, #4CAF50);"></i>
                    <p class="text-muted small mb-0">
                      Air quality data is provided by OpenWeatherMap. The Air Quality Index (AQI) ranges from 1 (Good) to 5 (Very Poor).
                      Poor air quality can affect sensitive groups like those with respiratory conditions, the elderly, and children.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Weather Alerts Section -->
  <div class="row mb-4" id="weatherAlertsContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3" style="background-color: #dc3545; color: white;">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>Weather Alerts</h4>
            <div class="badge bg-light text-danger">Important</div>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="alert alert-danger mb-3">
            <div class="d-flex align-items-center">
              <i class="bi bi-info-circle-fill me-2"></i>
              <p class="mb-0">Weather alerts indicate potentially dangerous weather conditions. Please take necessary precautions.</p>
            </div>
          </div>
          <div id="alertsList">
            <!-- Weather alerts will be inserted here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Hourly Forecast Section -->
  <div class="row mb-4" id="hourlyForecastContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3 bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Hourly Forecast</h4>
            <div class="d-flex align-items-center">
              <i class="bi bi-clock me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <span class="text-muted">Next 24 hours</span>
            </div>
          </div>
        </div>
        <div class="card-body p-3">
          <div class="hourly-forecast-scroll">
            <div class="d-flex" id="hourlyForecastRow">
              <!-- Hourly forecast items will be inserted here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Precipitation Forecast Section -->
  <div class="row mb-4" id="precipitationContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3 bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Precipitation Forecast</h4>
            <div class="d-flex align-items-center">
              <i class="bi bi-cloud-rain me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <span class="text-muted">Chance of rain</span>
            </div>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="row">
            <div class="col-md-12">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="precipitationChart"></canvas>
              </div>
              <div class="text-center mt-3">
                <p class="text-muted small">
                  <i class="bi bi-info-circle me-1"></i>
                  The chart shows the probability of precipitation (rain, snow, etc.) over the next 24 hours.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Weather for Farming -->
  <div class="row mb-4" id="farmingWeatherContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3 bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Weather Impact on Farming</h4>
            <div class="d-flex align-items-center">
              <i class="bi bi-flower1 me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <span class="text-muted">Agricultural insights</span>
            </div>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="row">
            <div class="col-lg-6 mb-4 mb-lg-0">
              <div class="d-flex align-items-center mb-3">
                <i class="bi bi-thermometer-half me-2" style="color: var(--theme-primary, #4CAF50); font-size: 1.5rem;"></i>
                <h5 class="mb-0">Current Conditions</h5>
              </div>
              <div id="farmingConditionsList">
                <!-- Farming conditions will be inserted here -->
              </div>
            </div>
            <div class="col-lg-6">
              <div class="d-flex align-items-center mb-3">
                <i class="bi bi-lightbulb me-2" style="color: var(--theme-primary, #4CAF50); font-size: 1.5rem;"></i>
                <h5 class="mb-0">Recommendations</h5>
              </div>
              <div id="farmingRecommendationsList">
                <!-- Farming recommendations will be inserted here -->
              </div>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col-12">
              <div class="card bg-light border-0 p-3">
                <div class="d-flex">
                  <i class="bi bi-info-circle-fill me-2" style="color: var(--theme-primary, #4CAF50);"></i>
                  <p class="text-muted small mb-0">
                    These recommendations are based on current and forecasted weather conditions. Always consider your specific crop varieties, soil conditions, and local agricultural practices.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Weather Map Section -->
  <div class="row mb-4" id="weatherMapContainer" style="display: none;">
    <div class="col-md-12">
      <div class="weather-container">
        <div class="card-header p-3 bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Weather Map</h4>
            <div class="d-flex align-items-center">
              <i class="bi bi-map me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <span class="text-muted">Visual forecast</span>
            </div>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="weather-map-controls mb-3">
            <div class="d-flex flex-wrap gap-2">
              <button class="weather-map-btn active" data-map="temp">Temperature</button>
              <button class="weather-map-btn" data-map="precipitation">Precipitation</button>
              <button class="weather-map-btn" data-map="wind">Wind</button>
              <button class="weather-map-btn" data-map="pressure">Pressure</button>
              <button class="weather-map-btn" data-map="clouds">Clouds</button>
            </div>
          </div>
          <div class="weather-map-container" id="weatherMap">
            <!-- Weather map will be displayed here -->
            <div class="text-center py-5">
              <p class="text-muted">Weather map loading...</p>
              <div class="spinner-border" style="color: var(--theme-primary, #4CAF50);" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Settings Section -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="weather-settings">
        <h5 class="weather-settings-title">Settings</h5>
        <div class="row">
          <div class="col-md-6">
            <div class="weather-settings-option">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="unitToggle">
                <label class="form-check-label" for="unitToggle">Display temperature in Fahrenheit</label>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="weather-settings-option">
              <button class="btn" style="background-color: var(--theme-primary, #4CAF50); color: white;" id="refreshWeatherBtn">
                <i class="bi bi-arrow-clockwise"></i> Refresh Weather Data
              </button>
            </div>
          </div>
        </div>
        <div class="mt-3">
          <div class="card border-0 p-3" style="background-color: var(--weather-dark-lighter);">
            <div class="d-flex">
              <i class="bi bi-info-circle-fill me-2" style="color: var(--theme-primary, #4CAF50);"></i>
              <p class="text-muted small mb-0">
                Weather data is provided by OpenWeatherMap and is updated every 10 minutes. Last updated: <span id="lastUpdated">--</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Alert -->
  <div class="alert" role="alert" id="weatherErrorAlert" style="display: none; background-color: rgba(220, 53, 69, 0.2); color: #ff6b6b; border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 10px;">
    <div class="d-flex align-items-center">
      <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
      <div>
        <h5 class="alert-heading mb-1">Weather Data Error</h5>
        <span id="weatherErrorMessage">An error occurred while fetching weather data.</span>
      </div>
    </div>
  </div>
</div>

<!-- Weather CSS -->
<link rel="stylesheet" href="/css/weather.css">

<!-- Weather JavaScript -->
<!-- Include Chart.js for precipitation chart -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Google Maps API is disabled for now -->
<!-- <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script> -->
<script src="/js/weather-enhanced.js"></script>
