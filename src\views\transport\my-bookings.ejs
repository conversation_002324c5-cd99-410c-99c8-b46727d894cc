<%- include('../partials/header') %>

<!-- Transport CSS -->
<link rel="stylesheet" href="/css/agri-network.css">
<link rel="stylesheet" href="/css/toast-notifications.css">

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-3">
      <!-- Sidebar -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-truck me-2" style="color: var(--network-primary);"></i>
            Transport Services
          </h5>
        </div>
        <div class="p-3">
          <div class="d-grid gap-2">
            <a href="/transport" class="linkedin-btn linkedin-btn-outline">
              <i class="bi bi-house-door-fill me-2"></i> Home
            </a>
            <a href="/transport/my-bookings" class="linkedin-btn linkedin-btn-primary">
              <i class="bi bi-calendar-check me-2"></i> My Bookings
            </a>
            <% if (userData && userData.isDriver) { %>
              <a href="/transport/driver-dashboard" class="linkedin-btn linkedin-btn-outline">
                <i class="bi bi-speedometer2 me-2"></i> Driver Dashboard
              </a>
            <% } else { %>
              <a href="/transport/register-driver" class="linkedin-btn linkedin-btn-outline">
                <i class="bi bi-person-plus-fill me-2"></i> Register as Driver
              </a>
            <% } %>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-9">
      <!-- Main Content -->
      <div class="linkedin-card mb-4">
        <div class="linkedin-card-header">
          <h5 class="linkedin-card-title">
            <i class="bi bi-calendar-check me-2" style="color: var(--network-primary);"></i>
            My Transport Bookings
          </h5>
        </div>
        <div class="linkedin-card-body p-0">
          <% if (bookings && bookings.length > 0) { %>
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Driver</th>
                    <th>Vehicle</th>
                    <th>Pickup</th>
                    <th>Delivery</th>
                    <th>Cargo</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <% bookings.forEach(booking => { %>
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <% if (booking.driver && booking.driver.photoURL) { %>
                            <img src="<%= booking.driver.photoURL %>" alt="Driver photo" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                          <% } else { %>
                            <div class="d-flex align-items-center justify-content-center bg-light rounded-circle me-2" style="width: 40px; height: 40px;">
                              <i class="bi bi-person-fill text-secondary"></i>
                            </div>
                          <% } %>
                          <div>
                            <div class="fw-bold"><%= booking.driver ? booking.driver.displayName : 'Driver' %></div>
                            <div class="small text-muted">
                              <i class="bi bi-star-fill text-warning"></i>
                              <%= booking.driver && booking.driver.rating ? booking.driver.rating : '0.0' %>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <% if (booking.vehicle) { %>
                          <div><%= booking.vehicle.make %> <%= booking.vehicle.model %></div>
                          <div class="small text-muted"><%= booking.vehicle.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) %></div>
                        <% } else { %>
                          <div class="text-muted">Not specified</div>
                        <% } %>
                      </td>
                      <td>
                        <div><%= booking.pickupLocation %></div>
                        <div class="small text-muted">
                          <%= booking.pickupDate ? new Date(booking.pickupDate).toLocaleDateString() : 'N/A' %>
                          <br>
                          <%= booking.pickupDate ? new Date(booking.pickupDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : '' %>
                        </div>
                      </td>
                      <td>
                        <div><%= booking.deliveryLocation %></div>
                        <div class="small text-muted">
                          <%= booking.deliveryDate ? new Date(booking.deliveryDate).toLocaleDateString() : 'N/A' %>
                          <br>
                          <%= booking.deliveryDate ? new Date(booking.deliveryDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : '' %>
                        </div>
                      </td>
                      <td>
                        <div><%= booking.cargoDescription %></div>
                        <div class="small text-muted"><%= booking.cargoWeight %> kg</div>
                      </td>
                      <td>
                        <% 
                          let statusClass = '';
                          let statusIcon = '';
                          
                          switch(booking.status) {
                            case bookingStatus.PENDING:
                              statusClass = 'bg-warning';
                              statusIcon = 'bi-hourglass-split';
                              break;
                            case bookingStatus.CONFIRMED:
                              statusClass = 'bg-info';
                              statusIcon = 'bi-check-circle';
                              break;
                            case bookingStatus.IN_PROGRESS:
                              statusClass = 'bg-primary';
                              statusIcon = 'bi-truck';
                              break;
                            case bookingStatus.COMPLETED:
                              statusClass = 'bg-success';
                              statusIcon = 'bi-check-all';
                              break;
                            case bookingStatus.CANCELLED:
                              statusClass = 'bg-danger';
                              statusIcon = 'bi-x-circle';
                              break;
                            default:
                              statusClass = 'bg-secondary';
                              statusIcon = 'bi-question-circle';
                          }
                        %>
                        <span class="badge <%= statusClass %>">
                          <i class="bi <%= statusIcon %> me-1"></i>
                          <%= booking.status.charAt(0).toUpperCase() + booking.status.slice(1) %>
                        </span>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          <% } else { %>
            <div class="p-5 text-center">
              <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Bookings Found</h5>
              <p class="text-muted">You haven't made any transport bookings yet.</p>
              <a href="/transport" class="linkedin-btn linkedin-btn-primary mt-2">
                <i class="bi bi-truck me-1"></i> Find Transport
              </a>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/toast-notifications.js"></script>

<%- include('../partials/footer') %>
