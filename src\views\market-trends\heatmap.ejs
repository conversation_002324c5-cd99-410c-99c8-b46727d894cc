<!-- Heatmap View -->
<div class="card shadow-sm h-100 heatmap-card d-none" id="heatmapCard">
  <div class="card-header bg-white d-flex justify-content-between align-items-center">
    <h5 class="mb-0">Price Heatmap</h5>
    <div class="btn-group" role="group">
      <button type="button" class="btn btn-outline-secondary btn-sm" id="heatmapByLocationBtn">By Location</button>
      <button type="button" class="btn btn-outline-secondary btn-sm active" id="heatmapByCropBtn">By Crop</button>
    </div>
  </div>
  <div class="card-body">
    <div id="heatmapChart" style="height: 400px;"></div>
    <div class="mt-3">
      <div class="d-flex justify-content-between align-items-center">
        <small class="text-muted">Lower Prices</small>
        <small class="text-muted">Higher Prices</small>
      </div>
      <div class="heatmap-legend">
        <div class="heatmap-legend-gradient"></div>
      </div>
    </div>
  </div>
</div>

<!-- Historical Analysis View -->
<div class="card shadow-sm h-100 historical-analysis-card d-none" id="historicalAnalysisCard">
  <div class="card-header bg-white d-flex justify-content-between align-items-center">
    <h5 class="mb-0">Historical Analysis</h5>
    <div class="btn-group" role="group">
      <button type="button" class="btn btn-outline-secondary btn-sm active" id="trendAnalysisBtn">Trend</button>
      <button type="button" class="btn btn-outline-secondary btn-sm" id="seasonalAnalysisBtn">Seasonal</button>
      <button type="button" class="btn btn-outline-secondary btn-sm" id="volatilityAnalysisBtn">Volatility</button>
    </div>
  </div>
  <div class="card-body">
    <div id="historicalAnalysisChart" style="height: 400px;"></div>
    <div class="mt-3">
      <div class="historical-analysis-legend d-flex justify-content-center gap-4">
        <div class="d-flex align-items-center">
          <div class="legend-color" style="background-color: rgba(76, 175, 80, 0.7);"></div>
          <span>Actual Price</span>
        </div>
        <div class="d-flex align-items-center">
          <div class="legend-color" style="background-color: rgba(33, 150, 243, 0.7);"></div>
          <span>Trend Line</span>
        </div>
        <div class="d-flex align-items-center seasonal-legend d-none">
          <div class="legend-color" style="background-color: rgba(255, 193, 7, 0.7);"></div>
          <span>Seasonal Pattern</span>
        </div>
        <div class="d-flex align-items-center volatility-legend d-none">
          <div class="legend-color" style="background-color: rgba(244, 67, 54, 0.7);"></div>
          <span>Volatility</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Price Forecast View -->
<div class="card shadow-sm h-100 forecast-card d-none" id="forecastCard">
  <div class="card-header bg-white d-flex justify-content-between align-items-center">
    <h5 class="mb-0">Price Forecast</h5>
    <div class="btn-group" role="group">
      <button type="button" class="btn btn-outline-secondary btn-sm active" id="shortTermForecastBtn">Short-term</button>
      <button type="button" class="btn btn-outline-secondary btn-sm" id="longTermForecastBtn">Long-term</button>
    </div>
  </div>
  <div class="card-body">
    <div id="forecastChart" style="height: 400px;"></div>
    <div class="mt-3">
      <div class="forecast-legend d-flex justify-content-center gap-4">
        <div class="d-flex align-items-center">
          <div class="legend-color" style="background-color: rgba(76, 175, 80, 0.7);"></div>
          <span>Historical Data</span>
        </div>
        <div class="d-flex align-items-center">
          <div class="legend-color" style="background-color: rgba(33, 150, 243, 0.7);"></div>
          <span>Forecast</span>
        </div>
        <div class="d-flex align-items-center">
          <div class="legend-color" style="background-color: rgba(33, 150, 243, 0.2);"></div>
          <span>Confidence Interval</span>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .heatmap-legend {
    height: 10px;
    width: 100%;
    margin-top: 5px;
    border-radius: 5px;
    overflow: hidden;
  }
  
  .heatmap-legend-gradient {
    height: 100%;
    width: 100%;
    background: linear-gradient(to right, #4CAF50, #FFC107, #F44336);
  }
  
  .legend-color {
    width: 15px;
    height: 15px;
    border-radius: 3px;
    margin-right: 5px;
  }
  
  .historical-analysis-legend,
  .forecast-legend {
    font-size: 0.8rem;
  }
</style>
