<div class="container auth-container">
  <div class="row">
    <div class="col-md-12">
      <% if (typeof error !== 'undefined') { %>
        <div class="alert alert-danger" role="alert" style="border-radius: 10px; border: none;">
          <i class="bi bi-exclamation-triangle-fill me-2"></i>
          <%= error %>
        </div>
      <% } %>
    </div>
  </div>

  <div class="row g-4 align-items-stretch">
    <div class="col-md-5 mb-4">
      <div class="auth-card h-100">
        <div class="auth-header">
          <h3 class="auth-title">Join Our Community</h3>
          <p class="auth-subtitle mb-0">Connect with sustainable farmers worldwide</p>
        </div>
        <div class="card-body">
          <div class="text-center mb-4">
            <div style="width: 120px; height: 120px; background-color: var(--primary-lightest); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
              <i class="bi bi-people" style="font-size: 3.5rem; color: var(--primary);"></i>
            </div>
            <h4 class="mt-4 fw-bold">Why Join Us?</h4>
            <p class="text-muted">Connect with farmers, share knowledge, and access exclusive resources.</p>
          </div>

          <div class="benefits-list">
            <div class="d-flex align-items-center mb-3">
              <div style="width: 40px; height: 40px; background-color: var(--primary-lightest); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                <i class="bi bi-share" style="color: var(--primary);"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Share Your Knowledge</h6>
                <p class="mb-0 small text-muted">Post your farming techniques and crops</p>
              </div>
            </div>

            <div class="d-flex align-items-center mb-3">
              <div style="width: 40px; height: 40px; background-color: var(--primary-lightest); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                <i class="bi bi-book" style="color: var(--primary);"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Access Resources</h6>
                <p class="mb-0 small text-muted">Learn from our extensive farming guides</p>
              </div>
            </div>

            <div class="d-flex align-items-center mb-3">
              <div style="width: 40px; height: 40px; background-color: var(--primary-lightest); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                <i class="bi bi-chat-dots" style="color: var(--primary);"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Network with Farmers</h6>
                <p class="mb-0 small text-muted">Connect with like-minded individuals</p>
              </div>
            </div>
          </div>

          <div class="text-center mt-4">
            <p>Already have an account? <a href="/auth/login" class="auth-link">Login here</a></p>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-7">
      <div class="auth-card h-100">
        <div class="auth-header">
          <h3 class="auth-title">Create Your Account</h3>
          <p class="auth-subtitle mb-0">Join our sustainable farming community</p>
        </div>
        <div class="card-body">
          <form action="/auth/register" method="POST" id="registrationForm" class="auth-form">
            <div class="row g-3">
              <div class="col-md-6">
                <label for="firstName" class="form-label">First Name</label>
                <div class="input-group">
                  <span class="input-group-text" style="background-color: transparent; border-right: none;">
                    <i class="bi bi-person" style="color: var(--primary);"></i>
                  </span>
                  <input type="text" class="form-control auth-input" id="firstName" name="firstName" placeholder="Enter your first name" style="border-left: none;" value="<%= typeof formData !== 'undefined' && formData.firstName ? formData.firstName : '' %>" required>
                </div>
              </div>
              <div class="col-md-6">
                <label for="lastName" class="form-label">Last Name</label>
                <input type="text" class="form-control auth-input" id="lastName" name="lastName" placeholder="Enter your last name" value="<%= typeof formData !== 'undefined' && formData.lastName ? formData.lastName : '' %>" required>
              </div>
            </div>

            <div class="mb-3 mt-3">
              <label for="email" class="form-label">Email address</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-envelope" style="color: var(--primary);"></i>
                </span>
                <input type="email" class="form-control auth-input" id="email" name="email" placeholder="Enter your email" style="border-left: none;" value="<%= typeof formData !== 'undefined' && formData.email ? formData.email : '' %>" required>
              </div>
            </div>

            <div class="mb-3">
              <label for="location" class="form-label">Location</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-geo-alt" style="color: var(--primary);"></i>
                </span>
                <input type="text" class="form-control auth-input" id="location" name="location" placeholder="City, State/Province, Country" style="border-left: none;" value="<%= typeof formData !== 'undefined' && formData.location ? formData.location : '' %>" required>
              </div>
            </div>

            <div class="mb-3">
              <label for="farmName" class="form-label">Farm Name (Optional)</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-house" style="color: var(--primary);"></i>
                </span>
                <input type="text" class="form-control auth-input" id="farmName" name="farmName" placeholder="Enter your farm name (if applicable)" style="border-left: none;" value="<%= typeof formData !== 'undefined' && formData.farmName ? formData.farmName : '' %>">
              </div>
            </div>

            <div class="mb-3">
              <label for="password" class="form-label">Password</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-lock" style="color: var(--primary);"></i>
                </span>
                <input type="password" class="form-control auth-input" id="password" name="password" placeholder="Create a password" style="border-left: none; border-right: none;" minlength="6" required>
                <button class="input-group-text" type="button" id="togglePassword" style="background-color: transparent; border-left: none;">
                  <i class="bi bi-eye" style="color: var(--primary);"></i>
                </button>
              </div>
              <div class="form-text">Password must be at least 6 characters long.</div>
            </div>

            <div class="mb-3">
              <label for="confirmPassword" class="form-label">Confirm Password</label>
              <div class="input-group">
                <span class="input-group-text" style="background-color: transparent; border-right: none;">
                  <i class="bi bi-lock-fill" style="color: var(--primary);"></i>
                </span>
                <input type="password" class="form-control auth-input" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" style="border-left: none; border-right: none;" minlength="6" required>
                <button class="input-group-text" type="button" id="toggleConfirmPassword" style="background-color: transparent; border-left: none;">
                  <i class="bi bi-eye" style="color: var(--primary);"></i>
                </button>
              </div>
              <div id="passwordMatchFeedback" class="invalid-feedback">
                Passwords do not match.
              </div>
            </div>

            <div class="mb-4 form-check">
              <input type="checkbox" class="form-check-input" id="termsAgreement" name="termsAgreement" style="border-color: var(--primary);" required>
              <label class="form-check-label" for="termsAgreement">I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal" class="auth-link">Terms and Conditions</a></label>
            </div>

            <div class="d-grid">
              <button type="submit" class="auth-btn">
                Create Account <i class="bi bi-arrow-right-circle ms-2"></i>
              </button>
            </div>
          </form>

          <!-- Terms and Conditions Modal -->
          <div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-scrollable">
              <div class="modal-content" style="border-radius: 12px; border: none; overflow: hidden;">
                <div class="modal-header" style="background: var(--gradient-primary); color: white; border-bottom: none;">
                  <h5 class="modal-title fw-bold" id="termsModalLabel">Terms and Conditions</h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <h6 class="fw-bold">1. Acceptance of Terms</h6>
                  <p>By registering for an account on Sustainable Farming, you agree to these Terms and Conditions.</p>

                  <h6 class="fw-bold">2. User Registration</h6>
                  <p>You agree to provide accurate and complete information when creating your account.</p>

                  <h6 class="fw-bold">3. Privacy Policy</h6>
                  <p>Your personal information will be handled in accordance with our Privacy Policy.</p>

                  <h6 class="fw-bold">4. User Conduct</h6>
                  <p>You agree to use the platform responsibly and not engage in any harmful activities.</p>

                  <h6 class="fw-bold">5. Content</h6>
                  <p>Any content you share on the platform must be appropriate and not infringe on others' rights.</p>
                </div>
                <div class="modal-footer" style="border-top: none;">
                  <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border-radius: 8px;">Close</button>
                  <button type="button" class="btn" style="background: var(--gradient-primary); color: white; border-radius: 8px;" data-bs-dismiss="modal" id="agreeTerms">I Agree</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Firebase Client Configuration -->
          <script type="module" src="/js/firebase-client.js"></script>
          <script src="/js/register.js"></script>
        </div>
      </div>
    </div>
  </div>
</div>
