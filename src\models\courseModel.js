/**
 * Course Model Structure
 * 
 * This file defines the structure of courses and modules in the application.
 * It serves as documentation for the Firestore database schema.
 */

/**
 * Course Collection: 'courses'
 * 
 * {
 *   id: string,                  // Auto-generated Firestore ID
 *   title: string,               // Course title
 *   description: string,         // Course description
 *   category: string,            // Course category (e.g., 'organic-farming', 'water-conservation')
 *   imageUrl: string,            // URL to course cover image
 *   authorId: string,            // ID of the course creator
 *   authorName: string,          // Name of the course creator
 *   level: string,               // Difficulty level ('beginner', 'intermediate', 'advanced')
 *   duration: number,            // Estimated completion time in minutes
 *   moduleCount: number,         // Number of modules in the course
 *   enrollmentCount: number,     // Number of users enrolled
 *   isPublished: boolean,        // Whether the course is published and visible to users
 *   createdAt: timestamp,        // When the course was created
 *   updatedAt: timestamp,        // When the course was last updated
 *   tags: array,                 // Array of tags for search and filtering
 *   prerequisites: array,        // Array of prerequisite courses or skills
 *   objectives: array            // Array of learning objectives
 * }
 */

/**
 * Module Collection: 'courses/{courseId}/modules'
 * 
 * {
 *   id: string,                  // Auto-generated Firestore ID
 *   courseId: string,            // Reference to parent course
 *   title: string,               // Module title
 *   description: string,         // Module description
 *   order: number,               // Order in the course (1, 2, 3, etc.)
 *   content: string,             // HTML content of the module
 *   videoUrl: string,            // Optional URL to video content
 *   duration: number,            // Estimated time to complete in minutes
 *   isPublished: boolean,        // Whether the module is published
 *   createdAt: timestamp,        // When the module was created
 *   updatedAt: timestamp,        // When the module was last updated
 *   resources: array             // Array of downloadable resources
 *   /* Resource structure:
 *     {
 *       name: string,            // Name of the resource
 *       description: string,     // Description of the resource
 *       url: string,             // URL to the resource
 *       type: string             // Type of resource (pdf, doc, etc.)
 *     }
 *   */
 * }
 */

/**
 * Progress Collection: 'users/{userId}/courseProgress'
 * 
 * {
 *   id: string,                  // Auto-generated Firestore ID (usually courseId)
 *   courseId: string,            // Reference to the course
 *   userId: string,              // Reference to the user
 *   enrolledAt: timestamp,       // When the user enrolled
 *   lastAccessedAt: timestamp,   // When the user last accessed the course
 *   completedModules: array,     // Array of completed module IDs
 *   currentModuleId: string,     // ID of the current module
 *   progress: number,            // Percentage of course completed (0-100)
 *   isCompleted: boolean,        // Whether the course is completed
 *   completedAt: timestamp,      // When the course was completed
 *   notes: string                // User's personal notes about the course
 * }
 */

export default {
  // This is just a documentation file, no actual code to export
};
