<%- include('partials/header') %>

<link rel="stylesheet" href="/css/facebook-profile.css">

<div class="container-fluid px-0">
  <!-- Cover Photo -->
  <div class="profile-cover" style="<%= userData && userData.coverPhotoURL ? `background-image: url('${userData.coverPhotoURL}')` : '' %>">
    <% if (!userData || !userData.coverPhotoURL) { %>
      <div class="profile-cover-placeholder">
        <i class="bi bi-image" style="font-size: 3rem;"></i>
      </div>
    <% } %>

    <% if (isOwnProfile) { %>
      <label for="coverPhotoInput" class="cover-photo-edit" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Change your cover photo">
        <i class="bi bi-camera me-1"></i> Edit Cover Photo
      </label>
      <input type="file" id="coverPhotoInput" class="d-none" accept="image/*">
    <% } %>

    <!-- Profile Picture -->
    <div class="profile-picture-container">
      <% if (userData && userData.photoURL) { %>
        <img src="<%= userData.photoURL %>" alt="Profile Picture" class="profile-picture">
      <% } else { %>
        <div class="profile-picture-placeholder">
          <i class="bi bi-person-fill" style="font-size: 5rem;"></i>
        </div>
      <% } %>

      <% if (isOwnProfile) { %>
        <label for="profilePictureInput" class="profile-picture-edit" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Change your profile picture">
          <i class="bi bi-camera"></i>
        </label>
        <input type="file" id="profilePictureInput" class="d-none" accept="image/*">
      <% } %>
    </div>
  </div>

  <div class="container">
    <!-- Profile Info -->
    <div class="profile-info">
      <h1 class="profile-name"><%= user.displayName || 'User' %></h1>
      <div class="profile-headline"><%= userData && userData.headline ? userData.headline : 'Sustainable Farmer' %></div>

      <div class="profile-meta">
        <% if (userData && userData.location) { %>
          <div class="profile-meta-item">
            <i class="bi bi-geo-alt"></i> <%= userData.location %>
          </div>
        <% } %>

        <% if (userData && userData.farmName) { %>
          <div class="profile-meta-item">
            <i class="bi bi-building"></i> <%= userData.farmName %>
          </div>
        <% } %>

        <div class="profile-meta-item">
          <i class="bi bi-calendar-check"></i> Joined <%= userData && userData.createdAt ? new Date(userData.createdAt).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently' %>
        </div>
      </div>

      <div class="profile-actions">
        <% if (isOwnProfile) { %>
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editProfileModal">
            <i class="bi bi-pencil me-1"></i> Edit Profile
          </button>
        <% } else { %>
          <button type="button" class="btn btn-primary">
            <i class="bi bi-person-plus me-1"></i> Connect
          </button>
          <button type="button" class="btn btn-outline-primary">
            <i class="bi bi-chat-dots me-1"></i> Message
          </button>
        <% } %>
      </div>
    </div>

    <!-- Profile Navigation -->
    <ul class="nav profile-nav">
      <li class="nav-item">
        <a class="nav-link active" href="#" data-bs-toggle="tab" data-bs-target="#posts" data-bs-animation="true">
          <i class="bi bi-file-text me-1"></i> Posts
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#" data-bs-toggle="tab" data-bs-target="#about" data-bs-animation="true">
          <i class="bi bi-info-circle me-1"></i> About
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#" data-bs-toggle="tab" data-bs-target="#photos" data-bs-animation="true">
          <i class="bi bi-images me-1"></i> Photos
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#" data-bs-toggle="tab" data-bs-target="#uploads" data-bs-animation="true">
          <i class="bi bi-cloud-upload me-1"></i> Uploads
        </a>
      </li>
    </ul>

    <!-- Profile Content -->
    <div class="row">
      <div class="col-md-4">
        <!-- Left Sidebar -->
        <div class="about-section">
          <h5>Intro</h5>

          <% if (userData && userData.bio) { %>
            <p><%= userData.bio %></p>
          <% } else { %>
            <p class="text-muted">No bio available</p>
          <% } %>

          <% if (isOwnProfile && (!userData || !userData.bio)) { %>
            <div class="d-grid">
              <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                <i class="bi bi-plus-lg me-1"></i> Add Bio
              </button>
            </div>
          <% } %>

          <hr>

          <div class="about-item">
            <div class="about-item-icon">
              <i class="bi bi-building"></i>
            </div>
            <div class="about-item-content">
              <% if (userData && userData.farmName) { %>
                <p class="about-item-title"><%= userData.farmName %></p>
                <p class="about-item-subtitle">Farm</p>
              <% } else { %>
                <p class="text-muted">No farm information</p>
                <% if (isOwnProfile) { %>
                  <button class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                    <i class="bi bi-plus-lg me-1"></i> Add Farm
                  </button>
                <% } %>
              <% } %>
            </div>
          </div>

          <div class="about-item">
            <div class="about-item-icon">
              <i class="bi bi-geo-alt"></i>
            </div>
            <div class="about-item-content">
              <% if (userData && userData.location) { %>
                <p class="about-item-title"><%= userData.location %></p>
                <p class="about-item-subtitle">Location</p>
              <% } else { %>
                <p class="text-muted">No location information</p>
                <% if (isOwnProfile) { %>
                  <button class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                    <i class="bi bi-plus-lg me-1"></i> Add Location
                  </button>
                <% } %>
              <% } %>
            </div>
          </div>
        </div>

        <!-- Photos Preview -->
        <div class="about-section">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">Photos</h5>
            <a href="#" class="text-decoration-none" data-bs-toggle="tab" data-bs-target="#photos">See All</a>
          </div>

          <div class="photos-grid">
            <% if (uploads && uploads.length > 0) { %>
              <%
                const photoUploads = uploads.filter(upload => {
                  if (!upload.fileUrl) return false;
                  const url = upload.fileUrl.toLowerCase();
                  return url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.webp') || url.includes('data:image/');
                }).slice(0, 9);
              %>

              <% if (photoUploads.length > 0) { %>
                <% photoUploads.forEach(photo => { %>
                  <div class="photo-item">
                    <img src="<%= photo.fileUrl %>" alt="<%= photo.title || 'Photo' %>">
                  </div>
                <% }); %>
              <% } else { %>
                <div class="text-center py-3 w-100">
                  <p class="text-muted">No photos available</p>
                </div>
              <% } %>
            <% } else { %>
              <div class="text-center py-3 w-100">
                <p class="text-muted">No photos available</p>
              </div>
            <% } %>
          </div>
        </div>
      </div>

      <div class="col-md-8">
        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Posts Tab -->
          <div class="tab-pane fade show active" id="posts">
            <% if (isOwnProfile) { %>
              <!-- Create Post -->
              <div class="post-card mb-4">
                <div class="card-body">
                  <div class="d-flex mb-3">
                    <% if (userData && userData.photoURL) { %>
                      <img src="<%= userData.photoURL %>" class="post-user-img">
                    <% } else { %>
                      <div class="post-user-placeholder">
                        <i class="bi bi-person-fill"></i>
                      </div>
                    <% } %>
                    <div class="flex-grow-1">
                      <textarea class="form-control" rows="2" placeholder="What's on your mind?"></textarea>
                    </div>
                  </div>
                  <hr>
                  <div class="d-flex">
                    <button class="btn btn-light flex-grow-1 me-2">
                      <i class="bi bi-image text-success me-1"></i> Photo
                    </button>
                    <button class="btn btn-primary">Post</button>
                  </div>
                </div>
              </div>
            <% } %>

            <!-- Posts Feed -->
            <% if (uploads && uploads.length > 0) { %>
              <% uploads.forEach(post => { %>
                <div class="post-card">
                  <div class="post-header">
                    <% if (userData && userData.photoURL) { %>
                      <img src="<%= userData.photoURL %>" class="post-user-img">
                    <% } else { %>
                      <div class="post-user-placeholder">
                        <i class="bi bi-person-fill"></i>
                      </div>
                    <% } %>
                    <div class="post-user-info">
                      <p class="post-user-name"><%= user.displayName || 'User' %></p>
                      <p class="post-time"><%= new Date(post.createdAt).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) %></p>
                    </div>
                  </div>

                  <div class="post-content">
                    <p class="post-text"><%= post.description %></p>
                    <% if (post.fileUrl) { %>
                      <img src="<%= post.fileUrl %>" class="post-image">
                    <% } %>
                  </div>

                  <div class="post-actions">
                    <div class="post-action" data-bs-toggle="tooltip" data-bs-placement="top" title="Show appreciation for this post">
                      <i class="bi bi-hand-thumbs-up"></i> Like
                    </div>
                    <div class="post-action" data-bs-toggle="tooltip" data-bs-placement="top" title="Leave a comment">
                      <i class="bi bi-chat"></i> Comment
                    </div>
                    <div class="post-action" data-bs-toggle="tooltip" data-bs-placement="top" title="Share this post with others">
                      <i class="bi bi-share"></i> Share
                    </div>
                  </div>
                </div>
              <% }); %>
            <% } else { %>
              <div class="text-center py-5">
                <i class="bi bi-file-earmark-text text-muted" style="font-size: 3rem;"></i>
                <h4 class="mt-3">No Posts Yet</h4>
                <p class="text-muted">When you create posts, they'll appear here.</p>
              </div>
            <% } %>
          </div>

          <!-- About Tab -->
          <div class="tab-pane fade" id="about">
            <div class="about-section">
              <h5>About</h5>

              <% if (userData && userData.bio) { %>
                <p><%= userData.bio %></p>
              <% } else { %>
                <p class="text-muted">No bio available</p>
              <% } %>

              <% if (isOwnProfile) { %>
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                  <i class="bi bi-pencil me-1"></i> Edit Bio
                </button>
              <% } %>
            </div>

            <div class="about-section">
              <h5>Farm Information</h5>

              <div class="row mb-3">
                <div class="col-md-4 fw-bold">Farm Name</div>
                <div class="col-md-8"><%= userData && userData.farmName ? userData.farmName : 'Not specified' %></div>
              </div>

              <div class="row mb-3">
                <div class="col-md-4 fw-bold">Location</div>
                <div class="col-md-8"><%= userData && userData.location ? userData.location : 'Not specified' %></div>
              </div>

              <% if (isOwnProfile) { %>
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                  <i class="bi bi-pencil me-1"></i> Edit Farm Information
                </button>
              <% } %>
            </div>
          </div>

          <!-- Photos Tab -->
          <div class="tab-pane fade" id="photos">
            <div class="about-section">
              <h5>Photos</h5>

              <% if (uploads && uploads.length > 0) { %>
                <%
                  const photoUploads = uploads.filter(upload => {
                    if (!upload.fileUrl) return false;
                    const url = upload.fileUrl.toLowerCase();
                    return url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.webp') || url.includes('data:image/');
                  });
                %>

                <% if (photoUploads.length > 0) { %>
                  <div class="photos-grid">
                    <% photoUploads.forEach(photo => { %>
                      <div class="photo-item">
                        <img src="<%= photo.fileUrl %>" alt="<%= photo.title || 'Photo' %>">
                      </div>
                    <% }); %>
                  </div>
                <% } else { %>
                  <div class="text-center py-3">
                    <p class="text-muted">No photos available</p>
                  </div>
                <% } %>
              <% } else { %>
                <div class="text-center py-3">
                  <p class="text-muted">No photos available</p>
                </div>
              <% } %>
            </div>
          </div>

          <!-- Uploads Tab -->
          <div class="tab-pane fade" id="uploads">
            <div class="about-section">
              <h5>Uploads</h5>

              <% if (uploads && uploads.length > 0) { %>
                <div class="row">
                  <% uploads.forEach(upload => { %>
                    <div class="col-md-6 mb-4">
                      <div class="card h-100">
                        <% if (upload.fileUrl) { %>
                          <%
                            // Determine file type from either the fileType property or the URL extension
                            let isImage = false;
                            let isVideo = false;
                            let isPdf = false;

                            if (upload.fileType) {
                              // If we have a fileType property, use it
                              isImage = upload.fileType.startsWith('image/');
                              isVideo = upload.fileType.startsWith('video/');
                              isPdf = upload.fileType === 'application/pdf';
                            } else {
                              // Otherwise try to determine from URL
                              const url = upload.fileUrl.toLowerCase();
                              isImage = url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.webp') || url.includes('data:image/');
                              isVideo = url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg') || url.includes('data:video/');
                              isPdf = url.endsWith('.pdf') || url.includes('data:application/pdf');
                            }
                          %>

                          <% if (isImage) { %>
                            <img src="<%= upload.fileUrl %>" class="card-img-top" alt="<%= upload.title %>" style="height: 180px; object-fit: cover;">
                          <% } else if (isVideo) { %>
                            <div class="card-img-top bg-dark d-flex align-items-center justify-content-center" style="height: 180px;">
                              <i class="bi bi-film text-light" style="font-size: 3rem;"></i>
                            </div>
                          <% } else if (isPdf) { %>
                            <div class="card-img-top bg-danger d-flex align-items-center justify-content-center" style="height: 180px;">
                              <i class="bi bi-file-pdf text-light" style="font-size: 3rem;"></i>
                            </div>
                          <% } else { %>
                            <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 180px;">
                              <i class="bi bi-file-earmark text-light" style="font-size: 3rem;"></i>
                            </div>
                          <% } %>
                        <% } else { %>
                          <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 180px;">
                            <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                          </div>
                        <% } %>
                        <div class="card-body">
                          <h5 class="card-title"><%= upload.title %></h5>
                          <p class="card-text"><%= upload.description %></p>
                        </div>
                        <div class="card-footer bg-white">
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                              <%= new Date(upload.createdAt).toLocaleDateString() %>
                            </small>
                            <a href="<%= upload.fileUrl %>" class="btn btn-sm btn-outline-primary" target="_blank">
                              <i class="bi bi-eye"></i> View
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% }); %>
                </div>
              <% } else { %>
                <div class="text-center py-5">
                  <i class="bi bi-cloud-upload text-muted" style="font-size: 3rem;"></i>
                  <h4 class="mt-3">No Uploads Yet</h4>
                  <p class="text-muted">When you upload content, it'll appear here.</p>
                </div>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Profile Modal -->
<% if (isOwnProfile) { %>
  <div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editProfileModalLabel">Edit Profile</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form action="/profile/update" method="POST" id="profileForm">
            <div class="mb-3">
              <label for="displayName" class="form-label">Display Name</label>
              <input type="text" class="form-control" id="displayName" name="displayName" value="<%= user.displayName || '' %>">
            </div>

            <div class="mb-3">
              <label for="headline" class="form-label">Headline</label>
              <input type="text" class="form-control" id="headline" name="headline" value="<%= userData && userData.headline ? userData.headline : '' %>" placeholder="e.g., Organic Farmer, Sustainable Agriculture Specialist">
            </div>

            <div class="mb-3">
              <label for="farmName" class="form-label">Farm Name</label>
              <input type="text" class="form-control" id="farmName" name="farmName" value="<%= userData && userData.farmName ? userData.farmName : '' %>">
            </div>

            <div class="mb-3">
              <label for="location" class="form-label">Location</label>
              <input type="text" class="form-control" id="location" name="location" value="<%= userData && userData.location ? userData.location : '' %>">
            </div>

            <div class="mb-3">
              <label for="bio" class="form-label">Bio</label>
              <textarea class="form-control" id="bio" name="bio" rows="4"><%= userData && userData.bio ? userData.bio : '' %></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" form="profileForm" class="btn btn-primary">Save Changes</button>
        </div>
      </div>
    </div>
  </div>
<% } %>

<!-- Upload Cover Photo Modal -->
<div class="modal fade" id="uploadCoverPhotoModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Upload Cover Photo</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form action="/profile/upload-cover-photo" method="POST" enctype="multipart/form-data" id="coverPhotoForm">
          <div class="mb-3">
            <label for="coverPhoto" class="form-label">Select a new cover photo</label>
            <input class="form-control" type="file" id="coverPhoto" name="coverPhoto" accept="image/*">
            <div class="form-text">Recommended size: 820x312 pixels</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" form="coverPhotoForm" class="btn btn-primary">Upload</button>
      </div>
    </div>
  </div>
</div>

<script src="/js/facebook-profile.js"></script>

<%- include('partials/footer') %>
