/* Modern Design CSS for Sustainable Farming Application */

:root {
  /* Main color palette */
  --primary: #4CAF50;
  --primary-dark: #388E3C;
  --primary-light: #8BC34A;
  --primary-lighter: #C5E1A5;
  --primary-lightest: #F1F8E9;

  /* Accent colors */
  --accent-yellow: #FFC107;
  --accent-orange: #FF9800;
  --accent-teal: #009688;

  /* Neutral colors */
  --neutral-100: #FFFFFF;
  --neutral-200: #F8F9FA;
  --neutral-300: #E9ECEF;
  --neutral-400: #DEE2E6;
  --neutral-500: #ADB5BD;
  --neutral-600: #6C757D;
  --neutral-700: #495057;
  --neutral-800: #343A40;
  --neutral-900: #212529;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-orange) 100%);
  --gradient-light: linear-gradient(135deg, var(--primary-lighter) 0%, var(--primary-light) 100%);
}

/* ===== GENERAL STYLES ===== */
body {
  font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--neutral-200);
  color: var(--neutral-800);
}

.card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.card-header {
  border-bottom: none;
  padding: 1.5rem;
  background: var(--gradient-primary);
}

.card-body {
  padding: 2rem;
}

.btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: var(--gradient-primary);
  border: none;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  border: 2px solid var(--primary);
  color: var(--primary);
  background: transparent;
}

.btn-outline:hover {
  background-color: var(--primary-lightest);
  color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 2px solid var(--neutral-300);
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-label {
  font-weight: 600;
  color: var(--neutral-700);
  margin-bottom: 0.5rem;
}

/* ===== HOME PAGE STYLES ===== */
.hero-section {
  background: var(--gradient-light);
  border-radius: 16px;
  padding: 4rem 2rem;
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  color: var(--neutral-900);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--neutral-700);
  margin-bottom: 2rem;
}

.hero-get-started {
  padding: 1rem 2.5rem;
  font-size: 1.25rem;
  border-radius: 50px;
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-lg);
  border: none;
  transition: all 0.3s ease;
}

.hero-get-started:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.feature-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
  box-shadow: var(--shadow-md);
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  background-color: var(--primary-lightest);
  color: var(--primary);
  font-size: 2.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  background-color: var(--primary);
  color: white;
  transform: scale(1.1);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--neutral-800);
}

.feature-text {
  color: var(--neutral-600);
  margin-bottom: 1.5rem;
}

/* ===== AUTH PAGES STYLES ===== */
.auth-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.auth-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.auth-header {
  background: var(--gradient-primary);
  padding: 2rem;
  color: white;
}

.auth-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  opacity: 0.9;
}

.auth-form {
  padding: 2rem;
}

.auth-input {
  border-radius: 8px;
  padding: 1rem;
  border: 2px solid var(--neutral-300);
  transition: all 0.3s ease;
}

.auth-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.auth-btn {
  width: 100%;
  padding: 1rem;
  border-radius: 8px;
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.auth-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.auth-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.auth-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1.5rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .auth-container {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-get-started {
    width: 100%;
    margin-bottom: 1rem;
  }
}
