<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Function Test</h1>
        
        <!-- Test Like Button -->
        <div class="card mb-3" data-post-id="test-post-1">
            <div class="card-body">
                <h5>Test Post</h5>
                <p>This is a test post to check if the like function works.</p>
                
                <!-- Post Actions -->
                <div class="fb-post-actions">
                    <div class="fb-post-action-button like" 
                         data-item-id="test-post-1"
                         onclick="handleLike('test-post-1')"
                         style="cursor: pointer; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; display: inline-block; margin-right: 10px;">
                        <i class="bi bi-hand-thumbs-up"></i>
                        <span>Like</span>
                    </div>
                    
                    <div class="fb-post-action-button comment" 
                         data-item-id="test-post-1" 
                         onclick="focusCommentInput('test-post-1')"
                         style="cursor: pointer; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; display: inline-block; margin-right: 10px;">
                        <i class="bi bi-chat"></i>
                        <span>Comment</span>
                    </div>
                </div>
                
                <!-- Comment Section -->
                <div id="comments-test-post-1" class="mt-3">
                    <div class="fb-comment-input-section">
                        <textarea id="comment-text-test-post-1" 
                                  class="fb-comment-input form-control" 
                                  placeholder="Write a comment..."
                                  onkeydown="handleCommentKeydown(event, 'test-post-1')"
                                  oninput="autoResizeTextarea(this)"></textarea>
                        <button class="fb-comment-send-btn btn btn-primary btn-sm mt-2" 
                                data-post-id="test-post-1"
                                onclick="submitComment('test-post-1')">
                            <i class="bi bi-send"></i> Send
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Function Test Results</h5>
            </div>
            <div class="card-body">
                <div id="test-results"></div>
                <button class="btn btn-primary" onclick="runTests()">Run Function Tests</button>
                <button class="btn btn-success ms-2" onclick="testHandleLike()">Test HandleLike Directly</button>
            </div>
        </div>
    </div>

    <!-- Load the JavaScript files -->
    <script src="/js/toast-notifications.js"></script>
    <script src="/js/facebook-interactions.js"></script>
    
    <script>
        // Global variables for testing
        window.currentUserId = 'test-user-123';

        // Test function
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let results = '<h6>Function Availability Test:</h6><ul>';

            // Test function availability
            const functions = [
                'handleLike',
                'focusCommentInput',
                'handleCommentKeydown',
                'autoResizeTextarea',
                'submitComment',
                'handleShare'
            ];

            functions.forEach(funcName => {
                const isAvailable = typeof window[funcName] === 'function';
                const status = isAvailable ? '✅' : '❌';
                results += `<li>${status} ${funcName}: ${typeof window[funcName]}</li>`;

                // Log to console for debugging
                console.log(`${funcName}:`, typeof window[funcName], window[funcName]);
            });

            results += '</ul>';

            // Test function calls
            results += '<h6>Function Call Test:</h6><ul>';

            try {
                if (typeof window.handleLike === 'function') {
                    results += '<li>✅ handleLike function is callable</li>';
                    console.log('✅ handleLike test passed');
                } else {
                    results += '<li>❌ handleLike function is not callable</li>';
                    console.error('❌ handleLike test failed');
                }

                if (typeof window.focusCommentInput === 'function') {
                    results += '<li>✅ focusCommentInput function is callable</li>';
                    console.log('✅ focusCommentInput test passed');
                } else {
                    results += '<li>❌ focusCommentInput function is not callable</li>';
                    console.error('❌ focusCommentInput test failed');
                }

                if (typeof window.autoResizeTextarea === 'function') {
                    const textarea = document.getElementById('comment-text-test-post-1');
                    window.autoResizeTextarea(textarea);
                    results += '<li>✅ autoResizeTextarea function executed successfully</li>';
                    console.log('✅ autoResizeTextarea test passed');
                } else {
                    results += '<li>❌ autoResizeTextarea function is not callable</li>';
                    console.error('❌ autoResizeTextarea test failed');
                }

            } catch (error) {
                results += `<li>❌ Error testing functions: ${error.message}</li>`;
                console.error('Error testing functions:', error);
            }

            results += '</ul>';
            resultsDiv.innerHTML = results;

            // Test actual like button click
            results += '<h6>Like Button Click Test:</h6>';
            const likeButton = document.querySelector('.like');
            if (likeButton) {
                results += '<p>Like button found. Try clicking it to test the handleLike function.</p>';
            } else {
                results += '<p>❌ Like button not found</p>';
            }

            resultsDiv.innerHTML = results;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== TEST PAGE LOADED ===');
            setTimeout(() => {
                console.log('=== RUNNING TESTS ===');
                runTests();
            }, 1000);
        });

        // Mock notification function for testing
        if (typeof showNotification === 'undefined') {
            window.showNotification = function(message, type) {
                console.log(`Notification (${type}): ${message}`);
                alert(`${type.toUpperCase()}: ${message}`);
            };
        }

        // Test handleLike directly
        function testHandleLike() {
            console.log('=== TESTING HANDLELIKE DIRECTLY ===');
            if (typeof window.handleLike === 'function') {
                console.log('Calling handleLike with test-post-1');
                window.handleLike('test-post-1');
            } else {
                console.error('handleLike is not a function:', typeof window.handleLike);
            }
        }
    </script>
</body>
</html>
