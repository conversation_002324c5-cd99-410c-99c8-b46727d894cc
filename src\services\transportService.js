import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc,
  Timestamp
} from 'firebase/firestore';
import { db, storage } from '../config/initFirebase.js';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getCurrentUser, getUserData } from './firebaseService.js';
import { safeFirestoreOperation } from '../utils/errorHandler.js';

// Collection names
const USERS_COLLECTION = 'users';
const DRIVERS_COLLECTION = 'drivers';
const VEHICLES_COLLECTION = 'vehicles';
const BOOKINGS_COLLECTION = 'bookings';

// Booking status constants
export const BOOKING_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// Vehicle types
export const VEHICLE_TYPES = {
  PICKUP: 'pickup',
  TRUCK_SMALL: 'small_truck',
  TRUCK_MEDIUM: 'medium_truck',
  TRUCK_LARGE: 'large_truck',
  TRACTOR: 'tractor',
  SPECIALIZED: 'specialized'
};

/**
 * Register a new driver
 */
export const registerDriver = async (driverData) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to register as a driver');
    }

    // Check if driver already exists
    const driverRef = doc(db, DRIVERS_COLLECTION, user.uid);
    const driverDoc = await getDoc(driverRef);

    if (driverDoc.exists()) {
      throw new Error('You are already registered as a driver');
    }

    // Create driver document
    await setDoc(driverRef, {
      userId: user.uid,
      ...driverData,
      isAvailable: true,
      rating: 0,
      totalRatings: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Update user profile to mark as driver
    const userRef = doc(db, USERS_COLLECTION, user.uid);
    await updateDoc(userRef, {
      isDriver: true,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Driver registered successfully'
    };
  } catch (error) {
    console.error('Error registering driver:', error);
    throw error;
  }
};

/**
 * Add a vehicle for a driver
 */
export const addVehicle = async (vehicleData) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to add a vehicle');
    }

    // Check if user is a driver
    const driverRef = doc(db, DRIVERS_COLLECTION, user.uid);
    const driverDoc = await getDoc(driverRef);

    if (!driverDoc.exists()) {
      throw new Error('You must be registered as a driver to add a vehicle');
    }

    // Create vehicle document
    const vehicleRef = doc(collection(db, VEHICLES_COLLECTION));
    await setDoc(vehicleRef, {
      driverId: user.uid,
      ...vehicleData,
      isAvailable: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      vehicleId: vehicleRef.id,
      message: 'Vehicle added successfully'
    };
  } catch (error) {
    console.error('Error adding vehicle:', error);
    throw error;
  }
};

/**
 * Get all available drivers
 *
 * Note: This query requires a composite index on the drivers collection:
 * - Fields: isAvailable (ASC), rating (DESC)
 * - Create the index at: https://console.firebase.google.com/project/sustainablefarming-bf265/firestore/indexes
 */
export const getAvailableDrivers = async () => {
  return await safeFirestoreOperation(async () => {
    let drivers = [];

    try {
      // Try the optimized query first (requires composite index)
      const q = query(
        collection(db, DRIVERS_COLLECTION),
        where('isAvailable', '==', true),
        orderBy('rating', 'desc')
      );

      const snapshot = await getDocs(q);

      drivers = await Promise.all(snapshot.docs.map(async (doc) => {
        const driverData = doc.data();
        const userData = await getUserData(driverData.userId);

        return {
          id: doc.id,
          ...driverData,
          user: userData
        };
      }));
    } catch (indexError) {
      console.warn('Index not yet available, falling back to simple query:', indexError.message);

      // Fallback to a simpler query that doesn't require the composite index
      const q = query(
        collection(db, DRIVERS_COLLECTION),
        where('isAvailable', '==', true)
      );

      const snapshot = await getDocs(q);

      drivers = await Promise.all(snapshot.docs.map(async (doc) => {
        const driverData = doc.data();
        const userData = await getUserData(driverData.userId);

        return {
          id: doc.id,
          ...driverData,
          user: userData
        };
      }));

      // Sort manually since we can't use orderBy in the query
      drivers.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    }

    return drivers;
  }, []); // Return empty array as fallback
};

/**
 * Get driver vehicles
 */
export const getDriverVehicles = async (driverId) => {
  try {
    const q = query(
      collection(db, VEHICLES_COLLECTION),
      where('driverId', '==', driverId)
    );

    const snapshot = await getDocs(q);

    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting driver vehicles:', error);
    throw error;
  }
};

/**
 * Create a new booking
 */
export const createBooking = async (bookingData) => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to create a booking');
    }

    // Create booking document
    const bookingRef = doc(collection(db, BOOKINGS_COLLECTION));

    // Convert date strings to Firestore timestamps
    const pickupDate = new Date(bookingData.pickupDate);
    const deliveryDate = bookingData.deliveryDate ? new Date(bookingData.deliveryDate) : null;

    await setDoc(bookingRef, {
      userId: user.uid,
      driverId: bookingData.driverId,
      vehicleId: bookingData.vehicleId,
      status: BOOKING_STATUS.PENDING,
      pickupDate: Timestamp.fromDate(pickupDate),
      deliveryDate: deliveryDate ? Timestamp.fromDate(deliveryDate) : null,
      pickupLocation: bookingData.pickupLocation,
      deliveryLocation: bookingData.deliveryLocation,
      cargoDescription: bookingData.cargoDescription,
      cargoWeight: bookingData.cargoWeight,
      specialInstructions: bookingData.specialInstructions || '',
      price: bookingData.price || 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      bookingId: bookingRef.id,
      message: 'Booking created successfully'
    };
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

/**
 * Get user bookings
 */
export const getUserBookings = async () => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to view your bookings');
    }

    const q = query(
      collection(db, BOOKINGS_COLLECTION),
      where('userId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);

    const bookings = await Promise.all(snapshot.docs.map(async (doc) => {
      const bookingData = doc.data();
      const driverData = await getUserData(bookingData.driverId);

      // Get vehicle data if available
      let vehicleData = null;
      if (bookingData.vehicleId) {
        const vehicleDoc = await getDoc(doc(db, VEHICLES_COLLECTION, bookingData.vehicleId));
        if (vehicleDoc.exists()) {
          vehicleData = vehicleDoc.data();
        }
      }

      return {
        id: doc.id,
        ...bookingData,
        driver: driverData,
        vehicle: vehicleData,
        pickupDate: bookingData.pickupDate?.toDate(),
        deliveryDate: bookingData.deliveryDate?.toDate(),
        createdAt: bookingData.createdAt?.toDate(),
        updatedAt: bookingData.updatedAt?.toDate()
      };
    }));

    return bookings;
  } catch (error) {
    console.error('Error getting user bookings:', error);
    throw error;
  }
};

/**
 * Get driver bookings
 */
export const getDriverBookings = async () => {
  try {
    const user = getCurrentUser();

    if (!user) {
      throw new Error('You must be logged in to view your bookings');
    }

    const q = query(
      collection(db, BOOKINGS_COLLECTION),
      where('driverId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);

    const bookings = await Promise.all(snapshot.docs.map(async (doc) => {
      const bookingData = doc.data();
      const userData = await getUserData(bookingData.userId);

      // Get vehicle data if available
      let vehicleData = null;
      if (bookingData.vehicleId) {
        const vehicleDoc = await getDoc(doc(db, VEHICLES_COLLECTION, bookingData.vehicleId));
        if (vehicleDoc.exists()) {
          vehicleData = vehicleDoc.data();
        }
      }

      return {
        id: doc.id,
        ...bookingData,
        user: userData,
        vehicle: vehicleData,
        pickupDate: bookingData.pickupDate?.toDate(),
        deliveryDate: bookingData.deliveryDate?.toDate(),
        createdAt: bookingData.createdAt?.toDate(),
        updatedAt: bookingData.updatedAt?.toDate()
      };
    }));

    return bookings;
  } catch (error) {
    console.error('Error getting driver bookings:', error);
    throw error;
  }
};
