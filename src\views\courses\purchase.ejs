<div class="container-fluid vh-100 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, var(--primary-500, #4CAF50) 0%, var(--primary-600, #45a049) 100%); position: relative; overflow: hidden;">
  <!-- Background Pattern -->
  <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"premium-pattern\" width=\"50\" height=\"50\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"2\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23premium-pattern)\"/></svg>'); opacity: 0.3;"></div>
  
  <div class="row w-100 justify-content-center position-relative">
    <div class="col-md-8 col-lg-6">
      <div class="card-enhanced animate-on-scroll" style="animation: fadeInUp 0.6s ease-out;">
        <div class="card-enhanced-body" style="padding: var(--space-12, 3rem);">
          
          <!-- Header -->
          <div class="text-center mb-5">
            <div class="mb-4">
              <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #FFD700, #FFA500); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; box-shadow: var(--shadow-xl);">
                <i class="bi bi-gem" style="font-size: 2.5rem; color: #333;"></i>
              </div>
            </div>
            <h1 class="h2 mb-3 fw-bold" style="color: var(--gray-900); font-family: var(--font-family-heading);">Premium Course Purchase</h1>
            <p class="text-muted">Unlock advanced content with expert-level training</p>
          </div>

          <!-- Course Information -->
          <div class="course-purchase-info mb-5" style="background: var(--gray-50); padding: 2rem; border-radius: 1rem; border: 2px solid var(--gray-200);">
            <div class="row align-items-center">
              <div class="col-md-3 text-center mb-3 mb-md-0">
                <% if (course.imageUrl) { %>
                  <img src="<%= course.imageUrl %>" alt="<%= course.title %>" style="width: 80px; height: 80px; object-fit: cover; border-radius: 12px; box-shadow: var(--shadow-md);">
                <% } else { %>
                  <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin: 0 auto; box-shadow: var(--shadow-md);">
                    <i class="bi bi-book" style="font-size: 2rem; color: white;"></i>
                  </div>
                <% } %>
              </div>
              <div class="col-md-6">
                <h3 class="h4 mb-2" style="color: var(--gray-900);">
                  <%= course.title %>
                  <span class="badge-enhanced badge-enhanced-warning ms-2">
                    <i class="bi bi-star-fill"></i> Premium
                  </span>
                </h3>
                <p class="text-muted mb-2"><%= course.description %></p>
                <div class="course-meta-small">
                  <span class="me-3">
                    <i class="bi bi-person-circle text-primary me-1"></i>
                    <%= course.authorName %>
                  </span>
                  <span class="me-3">
                    <i class="bi bi-clock text-primary me-1"></i>
                    <%= Math.round(course.duration / 60) %> hours
                  </span>
                  <span>
                    <i class="bi bi-collection text-primary me-1"></i>
                    <%= course.moduleCount %> modules
                  </span>
                </div>
              </div>
              <div class="col-md-3 text-center">
                <div class="price-display">
                  <div class="price-amount" style="font-size: 2rem; font-weight: bold; color: var(--primary-600);">
                    $<%= course.price %>
                  </div>
                  <div class="price-currency text-muted">
                    <%= course.currency %> • One-time payment
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Enrollment Status Check -->
          <% if (typeof enrollment !== 'undefined' && enrollment) { %>
            <% if (enrollment.status === 'pending_approval') { %>
              <div class="alert-enhanced alert-enhanced-info text-center mb-4">
                <i class="bi bi-clock fs-3 mb-3 d-block"></i>
                <h4>Payment Processed Successfully!</h4>
                <p class="mb-3">Your payment has been received and is being processed. Your enrollment is currently pending admin approval.</p>
                <div class="enrollment-status-timeline">
                  <div class="d-flex justify-content-center align-items-center gap-3">
                    <div class="status-step completed">
                      <i class="bi bi-check-circle-fill text-success"></i>
                      <small>Payment</small>
                    </div>
                    <div class="status-line"></div>
                    <div class="status-step active">
                      <i class="bi bi-clock text-warning"></i>
                      <small>Admin Review</small>
                    </div>
                    <div class="status-line"></div>
                    <div class="status-step">
                      <i class="bi bi-circle text-muted"></i>
                      <small>Access Granted</small>
                    </div>
                  </div>
                </div>
                <div class="mt-4">
                  <a href="/courses/<%= course.id %>" class="btn-enhanced btn-secondary">
                    <i class="bi bi-arrow-left"></i>
                    Back to Course
                  </a>
                </div>
              </div>
            <% } else if (enrollment.status === 'rejected') { %>
              <div class="alert-enhanced alert-enhanced-error text-center mb-4">
                <i class="bi bi-x-circle fs-3 mb-3 d-block"></i>
                <h4>Enrollment Rejected</h4>
                <p class="mb-3"><%= enrollment.rejectionReason || 'Your enrollment request has been rejected. Please contact support for more information.' %></p>
                <div class="mt-4">
                  <a href="/courses/<%= course.id %>" class="btn-enhanced btn-secondary me-2">
                    <i class="bi bi-arrow-left"></i>
                    Back to Course
                  </a>
                  <a href="mailto:<EMAIL>" class="btn-enhanced btn-primary">
                    <i class="bi bi-envelope"></i>
                    Contact Support
                  </a>
                </div>
              </div>
            <% } else if (enrollment.status === 'approved') { %>
              <div class="alert-enhanced alert-enhanced-success text-center mb-4">
                <i class="bi bi-check-circle fs-3 mb-3 d-block"></i>
                <h4>Enrollment Approved!</h4>
                <p class="mb-3">Congratulations! Your enrollment has been approved. You now have full access to this premium course.</p>
                <div class="mt-4">
                  <a href="/courses/<%= course.id %>" class="btn-enhanced btn-primary">
                    <i class="bi bi-play-fill"></i>
                    Start Learning
                  </a>
                </div>
              </div>
            <% } %>
          <% } else { %>
            <!-- Payment Form -->
            <div class="payment-section">
              <h3 class="h4 mb-4 text-center">
                <i class="bi bi-credit-card me-2"></i>
                Complete Your Purchase
              </h3>
              
              <!-- Payment Method Selection -->
              <div class="payment-methods mb-4">
                <div class="form-group-enhanced">
                  <label class="form-label-enhanced">Payment Method</label>
                  <div class="payment-method-options">
                    <div class="payment-method-option active" data-method="stripe">
                      <i class="bi bi-credit-card"></i>
                      <span>Credit/Debit Card</span>
                      <div class="payment-logos">
                        <i class="bi bi-credit-card-2-front"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Mock Payment Form -->
              <form id="payment-form" class="form-enhanced">
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group-enhanced">
                      <label class="form-label-enhanced">Card Number</label>
                      <input type="text" class="form-input-enhanced" placeholder="1234 5678 9012 3456" maxlength="19" required>
                    </div>
                  </div>
                </div>
                
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group-enhanced">
                      <label class="form-label-enhanced">Expiry Date</label>
                      <input type="text" class="form-input-enhanced" placeholder="MM/YY" maxlength="5" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group-enhanced">
                      <label class="form-label-enhanced">CVV</label>
                      <input type="text" class="form-input-enhanced" placeholder="123" maxlength="4" required>
                    </div>
                  </div>
                </div>

                <div class="form-group-enhanced">
                  <label class="form-label-enhanced">Cardholder Name</label>
                  <input type="text" class="form-input-enhanced" placeholder="John Doe" required>
                </div>

                <!-- Terms and Conditions -->
                <div class="form-group-enhanced">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="terms" required>
                    <label class="form-check-label" for="terms">
                      I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and understand that enrollment requires admin approval
                    </label>
                  </div>
                </div>

                <!-- Purchase Button -->
                <div class="d-grid gap-2 mt-4">
                  <button type="submit" class="btn-enhanced btn-primary" style="background: linear-gradient(135deg, #FFD700, #FFA500); color: #333; font-weight: bold; padding: 1rem;">
                    <i class="bi bi-shield-check me-2"></i>
                    Complete Purchase - $<%= course.price %> <%= course.currency %>
                  </button>
                  <a href="/courses/<%= course.id %>" class="btn-enhanced btn-secondary">
                    <i class="bi bi-arrow-left"></i>
                    Back to Course
                  </a>
                </div>
              </form>
            </div>
          <% } %>

          <!-- Security Notice -->
          <div class="security-notice mt-4 text-center">
            <small class="text-muted">
              <i class="bi bi-shield-lock me-1"></i>
              Your payment information is secure and encrypted. 
              <br>
              <i class="bi bi-info-circle me-1"></i>
              Admin approval is required for all premium course enrollments.
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.payment-method-options {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.payment-method-option {
  flex: 1;
  padding: 1rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: white;
}

.payment-method-option:hover {
  border-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.payment-method-option.active {
  border-color: var(--primary-500);
  background: var(--primary-50);
}

.payment-method-option i {
  font-size: 1.5rem;
  color: var(--primary-500);
  margin-bottom: 0.5rem;
  display: block;
}

.status-step {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.status-step i {
  font-size: 1.5rem;
}

.status-line {
  width: 3rem;
  height: 2px;
  background: var(--gray-300);
}

.status-step.completed .status-line {
  background: var(--success);
}

.security-notice {
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const paymentForm = document.getElementById('payment-form');
  
  if (paymentForm) {
    paymentForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const submitButton = this.querySelector('button[type="submit"]');
      const originalText = submitButton.innerHTML;
      
      // Show loading state
      submitButton.disabled = true;
      submitButton.innerHTML = '<span class="spinner-enhanced"></span> Processing Payment...';
      
      try {
        // Create payment record
        const createResponse = await fetch('/payments/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            courseId: '<%= course.id %>'
          })
        });
        
        const createResult = await createResponse.json();
        
        if (!createResult.success) {
          throw new Error(createResult.message);
        }
        
        // Process payment
        const processResponse = await fetch('/payments/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            paymentId: createResult.payment.id,
            paymentMethodId: 'real_payment_method_id' // TODO: Implement real payment method selection
          })
        });
        
        const processResult = await processResponse.json();
        
        if (processResult.success) {
          // Show success notification
          if (window.showNotification) {
            window.showNotification('Payment successful! Your enrollment is pending admin approval.', 'success');
          }
          
          // Redirect to course page
          setTimeout(() => {
            window.location.href = '/courses/<%= course.id %>';
          }, 2000);
        } else {
          throw new Error(processResult.message);
        }
        
      } catch (error) {
        console.error('Payment error:', error);
        
        // Show error notification
        if (window.showNotification) {
          window.showNotification('Payment failed: ' + error.message, 'error');
        }
        
        // Reset button
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
      }
    });
  }
  
  // Format card number input
  const cardNumberInput = document.querySelector('input[placeholder="1234 5678 9012 3456"]');
  if (cardNumberInput) {
    cardNumberInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
      let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
      e.target.value = formattedValue;
    });
  }
  
  // Format expiry date input
  const expiryInput = document.querySelector('input[placeholder="MM/YY"]');
  if (expiryInput) {
    expiryInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
      }
      e.target.value = value;
    });
  }
});
</script>
