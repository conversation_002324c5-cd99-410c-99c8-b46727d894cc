/**
 * Time-based greeting functionality
 *
 * This script provides a time-based greeting that changes based on the time of day:
 * - 5:00 AM to 11:59 AM: Good morning
 * - 12:00 PM to 4:59 PM: Good afternoon
 * - 5:00 PM to 8:59 PM: Good evening
 * - 9:00 PM to 4:59 AM: Good night
 *
 * The greeting updates automatically when the time changes to a different period.
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Time greeting script loaded');

  // Function to update the greeting based on current time
  function updateGreeting() {
    // Find all elements with the time-greeting class
    const greetingElements = document.querySelectorAll('.time-greeting');
    console.log('Found greeting elements:', greetingElements.length);

    if (greetingElements.length > 0) {
      // Get the current hour
      const currentHour = new Date().getHours();
      console.log('Current hour:', currentHour);

      // Determine the appropriate greeting
      let greeting;
      if (currentHour >= 5 && currentHour < 12) {
        greeting = 'Good morning';
      } else if (currentHour >= 12 && currentHour < 17) {
        greeting = 'Good afternoon';
      } else if (currentHour >= 17 && currentHour < 21) {
        greeting = 'Good evening';
      } else {
        greeting = 'Good night';
      }

      console.log('Setting greeting to:', greeting);

      // Update all greeting elements
      greetingElements.forEach(element => {
        console.log('Current element text:', element.textContent);

        // Only animate if the text is changing
        if (element.textContent !== greeting && element.textContent !== '') {
          // Add animation class
          element.classList.add('updating');

          // Remove animation class after animation completes
          setTimeout(() => {
            element.classList.remove('updating');
          }, 1000);
        }

        element.textContent = greeting;
      });

      return { hour: currentHour, greeting: greeting };
    }

    return null;
  }

  // Initial update
  const initialState = updateGreeting();

  // Set up periodic checking (every minute)
  if (initialState) {
    setInterval(() => {
      const currentState = updateGreeting();

      // Log only when the greeting changes (for debugging)
      if (currentState && currentState.greeting !== initialState.greeting) {
        console.log(`Greeting updated from "${initialState.greeting}" to "${currentState.greeting}"`);
        // Update the initial state reference
        initialState.greeting = currentState.greeting;
        initialState.hour = currentState.hour;
      }
    }, 60000); // Check every minute
  }
});
